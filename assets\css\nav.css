/* Custom styles for larger sidebar */
            .sidebar {
                width: 280px;  /* Increased sidebar width */
            }
            .sidebar .nav > li > a {
                padding: 18px 25px;  /* Increased padding */
                font-size: 18px;  /* Larger font size */
            }
            .sidebar .nav > li > a i {
                font-size: 24px;  /* Larger icons */
                width: 36px;  /* More width */
                margin-right: 15px;  /* More spacing */
            }
            .navbar-brand {
                padding: 18px;
                font-size: 22px;
            }
            .navbar-brand i {
                font-size: 28px;  /* Larger logo icon */
                margin-right: 15px;
            }
            .user-info {
                display: flex;
                align-items: center;
                padding: 15px 20px;
            }
            .user-info i {
                font-size: 32px !important;  /* Much larger user icon */
                margin-right: 15px;
                color: #337ab7;  /* Blue color for user icon */
            }
            .user-info span {
                font-size: 18px;
                font-weight: bold;
            }
            .logout-btn {
                padding: 15px 20px !important;
            }
            .logout-btn i {
                font-size: 28px !important;  /* Much larger logout icon */
                margin-right: 10px;
                color: #d9534f;  /* Red color for logout icon */
            }
            .logout-btn:hover i {
                color: #c9302c;  /* Darker red on hover */
            }
            .navbar-top-links {
                display: flex;
                align-items: center;
            }