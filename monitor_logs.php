<?php
// Script untuk memonitor log error dan aktivitas sistem

echo "<h2>Monitor Log Sistem Prediksi</h2>\n";

// Cek PHP error log
$php_error_log = ini_get('error_log');
if (!$php_error_log) {
    $php_error_log = 'php_errors.log';
}

echo "<h3>1. PHP Error Log</h3>\n";
if (file_exists($php_error_log)) {
    $errors = file($php_error_log);
    $recent_errors = array_slice($errors, -20); // 20 error terakhir
    
    if (!empty($recent_errors)) {
        echo "<div style='background: #f8f8f8; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>\n";
        echo "<pre>" . htmlspecialchars(implode('', $recent_errors)) . "</pre>\n";
        echo "</div>\n";
    } else {
        echo "<p>✅ Tidak ada error PHP</p>\n";
    }
} else {
    echo "<p>ℹ️ File error log tidak ditemukan: $php_error_log</p>\n";
}

// Cek Apache/Nginx error log (jika ada)
$apache_error_logs = [
    'C:/laragon/logs/apache_error.log',
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log'
];

echo "<h3>2. Web Server Error Log</h3>\n";
$found_server_log = false;
foreach ($apache_error_logs as $log_file) {
    if (file_exists($log_file)) {
        $found_server_log = true;
        echo "<h4>$log_file</h4>\n";
        $errors = file($log_file);
        $recent_errors = array_slice($errors, -10); // 10 error terakhir
        
        if (!empty($recent_errors)) {
            echo "<div style='background: #f8f8f8; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;'>\n";
            echo "<pre>" . htmlspecialchars(implode('', $recent_errors)) . "</pre>\n";
            echo "</div>\n";
        }
        break;
    }
}

if (!$found_server_log) {
    echo "<p>ℹ️ Log web server tidak ditemukan atau tidak dapat diakses</p>\n";
}

// Test koneksi database
echo "<h3>3. Test Koneksi Database</h3>\n";
try {
    include 'koneksi.php';
    if ($koneksi) {
        echo "<p>✅ Koneksi database berhasil</p>\n";
        
        // Test query sederhana
        $test_query = "SELECT COUNT(*) as total FROM nasabah";
        $result = mysqli_query($koneksi, $test_query);
        if ($result) {
            $data = mysqli_fetch_assoc($result);
            echo "<p>✅ Query test berhasil. Total nasabah: " . $data['total'] . "</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Error query: " . mysqli_error($koneksi) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>❌ Koneksi database gagal</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>\n";
}

// Test server Python
echo "<h3>4. Test Server Python</h3>\n";
$python_url = 'http://localhost:5000/api/predict';
$ch = curl_init($python_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['test' => true]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 3);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "<p style='color: orange;'>⚠️ Server Python tidak berjalan: $curl_error</p>\n";
    echo "<p>ℹ️ Sistem akan menggunakan prediksi fallback</p>\n";
} else {
    echo "<p>✅ Server Python berjalan (HTTP $http_code)</p>\n";
    echo "<p>Response: " . htmlspecialchars($response) . "</p>\n";
}

// Informasi sistem
echo "<h3>5. Informasi Sistem</h3>\n";
echo "<ul>\n";
echo "<li>PHP Version: " . phpversion() . "</li>\n";
echo "<li>Server: " . $_SERVER['SERVER_SOFTWARE'] . "</li>\n";
echo "<li>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</li>\n";
echo "<li>Current Time: " . date('Y-m-d H:i:s') . "</li>\n";
echo "<li>Memory Usage: " . number_format(memory_get_usage() / 1024 / 1024, 2) . " MB</li>\n";
echo "</ul>\n";

echo "<hr>\n";
echo "<p><strong>Refresh otomatis dalam 30 detik...</strong></p>\n";
echo "<p><a href='prediksi_baru.php'>Test Form Prediksi</a> | ";
echo "<a href='debug_data.php'>Debug Data</a> | ";
echo "<a href='test_form_simple.php'>Test Form Sederhana</a></p>\n";
?>

<script>
// Auto refresh setiap 30 detik
setTimeout(function() {
    window.location.reload();
}, 30000);
</script>
