<?php
// mengaktifkan session pada php
session_start();

// menghubungkan php dengan koneksi database
include 'koneksi.php';

// menangkap data yang dikirim dari form login
$username = $_POST['username'];
$password = $_POST['password'];

// Mencegah SQL Injection
$username = mysqli_real_escape_string($koneksi, $username);
$password = mysqli_real_escape_string($koneksi, $password);

// menyeleksi data user dengan username dan password yang sesuai
$login = mysqli_query($koneksi, "SELECT * FROM users WHERE username='$username' AND password='$password'");
// menghitung jumlah data yang ditemukan
$cek = mysqli_num_rows($login);

// cek apakah username dan password di temukan pada database
if($cek > 0){
    $data = mysqli_fetch_assoc($login);

    // Simpan data pengguna dalam session
    $_SESSION['id_user'] = $data['id_user'];
    $_SESSION['username'] = $data['username'];
    $_SESSION['nama'] = $data['nama'];
    $_SESSION['password'] = $password; // Sebaiknya password tidak disimpan di session

    // cek jika user login sebagai admin
    if($data['level'] == "admin"){
        $_SESSION['level'] = "admin";
        // alihkan ke halaman dashboard admin
        header("location:index_admin.php");
        exit();

    // cek jika user login sebagai analis kredit
    } else if($data['level'] == "analis kredit" || $data['level'] == "analis"){
        $_SESSION['level'] = "analis";
        // alihkan ke halaman dashboard analis
        header("location:index_analis.php");
        exit();

    } else {
        // alihkan ke halaman login kembali
        header("location:login.php?pesan=gagal");
        exit();
    }
} else {
    header("location:login.php?pesan=gagal");
    exit();
}

?>