<?php
// Script untuk menguji koneksi ke server Python
echo "=== TEST KONEKSI SERVER PYTHON ===\n";

// Test koneksi ke server Python
$api_url = 'http://localhost:5000/api/model-info';

echo "Menguji koneksi ke: $api_url\n";

$ch = curl_init($api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "ERROR: Tidak dapat terhubung ke server Python\n";
    echo "Error: $curl_error\n";
    echo "\nPastikan server Python sudah berjalan dengan menjalankan:\n";
    echo "start_model_server.bat\n";
} else {
    echo "SUCCESS: Server Python berjalan\n";
    echo "HTTP Code: $http_code\n";
    echo "Response: $response\n";
}

echo "\n=== TEST SELESAI ===\n";
?>
