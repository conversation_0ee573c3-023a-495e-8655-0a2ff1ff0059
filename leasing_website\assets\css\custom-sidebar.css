/* Custom Sidebar Styles */

/* Wrapper styles */
#wrapper {
    transition: all 0.3s ease;
}

/* Sidebar toggle button */
.navbar-toggle.sidebar-toggle {
    float: left;
    margin-left: 15px;
    margin-right: 0;
}

/* Sidebar styles */
.sidebar {
    width: 250px;
    position: fixed;
    top: 51px;
    bottom: 0;
    left: 0;
    z-index: 1000;
    display: block;
    padding: 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #f8f8f8;
    border-right: 1px solid #e7e7e7;
    transition: all 0.3s ease;
}

/* Sidebar collapsed state */
.sidebar-collapse.collapse {
    display: none !important;
}

/* Sidebar menu styles */
.sidebar-nav {
    padding: 0;
    margin: 0;
}

.sidebar-nav .nav li {
    border-bottom: 1px solid #e7e7e7;
}

.sidebar-nav .nav li a {
    color: #555;
    padding: 15px;
    display: block;
    transition: all 0.2s ease;
}

.sidebar-nav .nav li a:hover,
.sidebar-nav .nav li a:focus {
    background-color: #eee;
    color: #333;
    text-decoration: none;
}

/* Active menu item */
.sidebar-nav .nav li a.active {
    background-color: #337ab7;
    color: white;
}

/* Icon styles */
.sidebar-nav .nav li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* User info in navbar */
.user-info {
    padding: 15px;
    color: #777;
}

.user-info i {
    margin-right: 5px;
}

/* Content wrapper */
#page-wrapper {
    padding: 20px;
    min-height: calc(100vh - 51px);
    margin-left: 250px;
    transition: all 0.3s ease;
}

/* Responsive styles */
@media (max-width: 768px) {
    .sidebar {
        width: 0;
    }
    
    .sidebar.active {
        width: 250px;
    }
    
    #page-wrapper {
        margin-left: 0;
    }
    
    #wrapper.sidebar-open #page-wrapper {
        margin-left: 250px;
    }
}

/* Hamburger button styles */
.navbar-toggle.sidebar-toggle {
    display: block;
    border-color: #ddd;
}

.navbar-toggle.sidebar-toggle:hover,
.navbar-toggle.sidebar-toggle:focus {
    background-color: #ddd;
}

.navbar-toggle.sidebar-toggle .icon-bar {
    background-color: #888;
}

/* Navbar brand positioning */
.navbar-brand {
    margin-left: 15px;
}

/* Overlay for mobile when sidebar is open */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 51px;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.3);
}

.sidebar-overlay.active {
    display: block;
}

/* Body styles when sidebar is open on mobile */
body.sidebar-open {
    overflow: hidden;
}

/* Adjust content when sidebar is toggled */
body.sidebar-toggled #page-wrapper {
    margin-left: 0;
}

body.sidebar-toggled .sidebar {
    width: 0;
}

/* Transition for smooth sidebar toggle */
.sidebar,
#page-wrapper {
    transition: all 0.3s ease;
}
