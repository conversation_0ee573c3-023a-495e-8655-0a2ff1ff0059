# Sistem Prediksi Kelayakan Pemberian Kredit dengan Algoritma Backpropagation

## Deskripsi Sistem

Sistem ini adalah aplikasi web untuk memprediksi kelayakan pemberian kredit menggunakan algoritma Backpropagation Neural Network. Sistem terdiri dari:

1. **Web Service PHP** - Interface pengguna dan manajemen data
2. **Python API** - Perhitungan prediksi dengan algoritma backpropagation
3. **REST API** - Komunikasi antara PHP dan Python
4. **Model Machine Learning** - Disimpan dalam format pickle (.pkl)
5. **Database MySQL** - Penyimpanan data nasabah dan hasil prediksi

## Fitur Utama

### 1. Dashboard dengan Visualisasi
- **File**: `dashboard.php`
- **Fitur**:
  - Statistik jumlah nasabah layak/tidak layak
  - Grafik pie chart distribusi kelayakan
  - Grafik bar chart trend 6 bulan terakhir
  - Prediksi terbaru
  - Cards dengan informasi ringkas

### 2. Halaman Prediksi Nasabah Baru
- **File**: `prediksi_baru.php`
- **Fitur**:
  - Form input data nasabah lengkap
  - Validasi data real-time
  - Format currency otomatis
  - Integrasi dengan Python API untuk prediksi
  - Redirect ke halaman hasil setelah prediksi

### 3. Daftar Kelayakan
- **File**: `daftar_kelayakan.php`
- **Fitur**:
  - Tabel data nasabah dengan status kelayakan
  - Export ke Excel (semua, layak, tidak layak)
  - Statistik kelayakan dengan progress bar
  - DataTables untuk sorting dan searching

### 4. Laporan Lengkap
- **File**: `laporan_lengkap.php`
- **Fitur**:
  - Filter berdasarkan kelayakan, bulan, pekerjaan
  - Export Excel dengan filter
  - Print laporan
  - Modal detail nasabah
  - Statistik dinamis

### 5. Export Excel
- **File**: `export_excel.php`, `export_kelayakan.php`
- **Fitur**:
  - Export data dengan filter
  - Format Excel yang rapi
  - Statistik dan ringkasan
  - Header dan footer informatif

### 6. Detail dan Print Nasabah
- **File**: `detail_nasabah.php`, `print_nasabah.php`
- **Fitur**:
  - Detail lengkap data nasabah
  - Analisis faktor kelayakan
  - Print individual dengan format profesional
  - Perhitungan rasio dan analisis

## Struktur Database

### Tabel Utama:
1. **nasabah** - Data pribadi dan finansial nasabah
2. **users** - Data pengguna sistem (admin/analis)
3. **hasil_prediksi** - Hasil prediksi dengan probabilitas
4. **laporan_prediksi** - Laporan umum prediksi
5. **prediksi_detail** - Detail hasil prediksi per nasabah
6. **proses_perhitungan** - Log proses backpropagation

## Algoritma Backpropagation

### Input Features:
1. Umur
2. Jenis kelamin (encoded)
3. Status perkawinan (encoded)
4. Pekerjaan (encoded)
5. Penghasilan
6. Jumlah tanggungan
7. Jumlah pinjaman
8. Jangka waktu
9. Kepemilikan rumah (encoded)
10. Jaminan (encoded)
11. Tahun kendaraan
12. Status pajak (encoded)

### Output:
- **Layak** atau **Tidak Layak**
- **Probabilitas** (0-1)

### Model Architecture:
- **Input Layer**: 12 neurons
- **Hidden Layer 1**: 20 neurons (ReLU activation)
- **Hidden Layer 2**: 10 neurons (ReLU activation)
- **Output Layer**: 1 neuron (Sigmoid activation)

## REST API Endpoints

### Python Flask API (Port 5000):
- `GET /api/model-info` - Informasi model
- `POST /api/predict` - Prediksi kelayakan

### PHP API:
- `api/predict.php` - Wrapper untuk Python API
- `api/model-info.php` - Informasi model

## Cara Instalasi dan Menjalankan

### 1. Setup Database
```bash
setup_database.bat
```

### 2. Install Dependencies Python
```bash
pip install -r requirements.txt
```

### 3. Start Model Server
```bash
start_model_server.bat
```

### 4. Akses Web Interface
```
http://localhost/sistem_prediksi_backpropagation(bismillah)/
```

### 5. Login
- **Admin**: username=`admin`, password=`password`
- **Analis**: username=`analis`, password=`password`

## File-File Utama

### Frontend (PHP):
- `dashboard.php` - Dashboard dengan visualisasi
- `prediksi_baru.php` - Form prediksi nasabah baru
- `daftar_kelayakan.php` - Daftar nasabah dengan kelayakan
- `laporan_lengkap.php` - Laporan dengan filter dan export
- `detail_nasabah.php` - Detail nasabah (AJAX)
- `print_nasabah.php` - Print individual nasabah
- `export_excel.php` - Export ke Excel
- `nav_admin.php`, `nav_analis.php` - Navigation

### Backend (Python):
- `model_wrapper.py` - Flask API server
- `backpropagation_model.py` - Model training
- `model/` - Folder model dan dataset

### Database:
- `database_setup.sql` - Setup database dan tabel
- `koneksi.php` - Konfigurasi database

### Utilities:
- `requirements.txt` - Python dependencies
- `setup_system.bat` - Setup otomatis
- `test_system.py` - Testing sistem
- `fix_database_compatibility.php` - Fix compatibility

## Workflow Sistem

1. **Input Data Nasabah** → `prediksi_baru.php`
2. **Simpan ke Database** → Tabel `nasabah`
3. **Kirim ke Python API** → `model_wrapper.py`
4. **Prediksi dengan Model** → Backpropagation Neural Network
5. **Simpan Hasil** → Tabel `hasil_prediksi`
6. **Tampilkan Hasil** → `hasil_prediksi.php`
7. **Lihat Laporan** → `laporan_lengkap.php`
8. **Export Data** → `export_excel.php`

## Keamanan

- **Session Management** - `cek_session.php`
- **Role-based Access** - Admin dan Analis
- **SQL Injection Prevention** - Prepared statements
- **Input Validation** - Client dan server side
- **Error Handling** - Comprehensive error logging

## Monitoring dan Logging

- **Error Logs** - PHP dan Python error logging
- **Database Logs** - Query error tracking
- **Model Performance** - Accuracy tracking
- **User Activity** - Session dan access logs

## Backup dan Recovery

- **Database Backup** - Otomatis saat setup
- **Model Backup** - Versioning model files
- **Code Backup** - File backup saat edit
- **Export Data** - Regular data export

## Maintenance

- **Model Retraining** - Periodic dengan data baru
- **Database Cleanup** - Archive old predictions
- **Performance Monitoring** - Query optimization
- **Security Updates** - Regular security patches

## Support dan Troubleshooting

- **Documentation**: `README.md`, `TROUBLESHOOTING.md`
- **Testing Tools**: `test_system.py`, `test_system.bat`
- **Fix Scripts**: `fix_database_issues.bat`
- **Quick Fix**: `QUICK_FIX.bat`

Sistem ini dirancang untuk memberikan prediksi kelayakan kredit yang akurat dan dapat diandalkan dengan interface yang user-friendly dan fitur-fitur lengkap untuk manajemen data dan pelaporan.
