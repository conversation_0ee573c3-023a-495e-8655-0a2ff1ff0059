@echo off
echo ======================================================
echo MENJALANKAN SISTEM PREDIKSI KELAYAKAN KREDIT
echo ======================================================
echo.

echo [1/4] Memeriksa Python dan dependencies...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python tidak ditemukan!
    pause
    exit /b 1
)

python -c "import flask, sklearn, pandas, numpy, mysql.connector" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Dependencies tidak lengkap!
    echo Menjalankan instalasi dependencies...
    pip install -r requirements.txt
)

echo [2/4] Memeriksa database...
mysql -u root -e "USE sistem_prediksi_backpropagation;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Database tidak ditemukan!
    echo Membuat database...
    mysql -u root < database_setup.sql
)

echo [3/4] Memeriksa model...
if not exist "model\backpropagation_model.pkl" (
    echo Model tidak ditemukan, akan dibuat otomatis saat server berjalan
)

echo [4/4] Memulai server Python...
echo.
echo ======================================================
echo SERVER PYTHON BERJALAN DI PORT 5000
echo ======================================================
echo.
echo JANGAN TUTUP JENDELA INI!
echo Buka browser dan akses: http://localhost/ngasal/
echo.

python model_wrapper.py

echo.
echo Server berhenti. Tekan tombol apa saja untuk keluar...
pause
