/*
 * Compact Layout CSS
 * Merapikan tampilan halaman agar tidak perlu scroll
 */

/* Mengatur ukuran navbar */
.navbar {
    min-height: 45px;
}

.navbar-brand {
    height: 45px;
    padding: 12px 15px;
    font-size: 16px;
}

.navbar-toggle {
    margin-top: 5px;
    margin-bottom: 5px;
    padding: 8px 10px;
}

.navbar-top-links li a {
    padding: 12px;
    min-height: 45px;
}

.user-info {
    padding: 12px;
    font-size: 14px;
}

/* Mengatur ukuran sidebar */
.sidebar {
    top: 45px;
    width: 220px;
}

.sidebar-nav .nav li a {
    padding: 10px 15px;
    font-size: 14px;
}

.sidebar-nav .nav li a i {
    font-size: 15px;
    margin-right: 8px;
}

/* Mengatur content wrapper */
#page-wrapper {
    padding: 15px 20px;
    min-height: calc(100vh - 45px);
    margin-top: 0;
    margin-left: 220px;
}

/* Mengatur panel dan card */
.panel {
    margin-bottom: 20px;
    border-radius: 4px;
}

.panel-heading {
    padding: 12px 15px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.panel-body {
    padding: 15px;
}

.panel-footer {
    padding: 10px 15px;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
}

/* Mengatur tabel */
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
    padding: 8px;
    vertical-align: middle;
}

.table-responsive {
    margin-bottom: 15px;
}

.table-bordered {
    border: 1px solid #ddd;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #f9f9f9;
}

/* Mengatur form */
.form-group {
    margin-bottom: 10px;
}

.form-control {
    height: 32px;
    padding: 5px 10px;
}

.btn {
    padding: 5px 10px;
}

/* Mengatur page header */
.page-header {
    margin: 10px 0 15px;
    padding-bottom: 5px;
    font-size: 24px;
}

/* Mengatur breadcrumb */
.breadcrumb {
    padding: 5px 10px;
    margin-bottom: 10px;
}

/* Mengatur alert */
.alert {
    padding: 8px 15px;
    margin-bottom: 10px;
}

/* Mengatur well */
.well {
    padding: 10px;
    margin-bottom: 15px;
}

/* Mengatur progress bar */
.progress {
    margin-bottom: 10px;
    height: 15px;
}

/* Mengatur list group */
.list-group-item {
    padding: 8px 15px;
}

/* Mengatur card */
.card {
    margin-bottom: 15px;
}

.card-header {
    padding: 8px 15px;
}

.card-body {
    padding: 10px;
}

.card-footer {
    padding: 5px 15px;
}

/* Mengatur jumbotron */
.jumbotron {
    padding: 15px;
    margin-bottom: 15px;
}

/* Mengatur modal */
.modal-header {
    padding: 10px 15px;
}

.modal-body {
    padding: 10px 15px;
}

.modal-footer {
    padding: 10px 15px;
}

/* Mengatur grid spacing */
.row {
    margin-right: -10px;
    margin-left: -10px;
}

.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1,
.col-xs-2, .col-sm-2, .col-md-2, .col-lg-2,
.col-xs-3, .col-sm-3, .col-md-3, .col-lg-3,
.col-xs-4, .col-sm-4, .col-md-4, .col-lg-4,
.col-xs-5, .col-sm-5, .col-md-5, .col-lg-5,
.col-xs-6, .col-sm-6, .col-md-6, .col-lg-6,
.col-xs-7, .col-sm-7, .col-md-7, .col-lg-7,
.col-xs-8, .col-sm-8, .col-md-8, .col-lg-8,
.col-xs-9, .col-sm-9, .col-md-9, .col-lg-9,
.col-xs-10, .col-sm-10, .col-md-10, .col-lg-10,
.col-xs-11, .col-sm-11, .col-md-11, .col-lg-11,
.col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
    padding-right: 10px;
    padding-left: 10px;
}

/* Mengatur font size */
body {
    font-size: 14px;
}

h1 {
    font-size: 24px;
}

h2 {
    font-size: 20px;
}

h3 {
    font-size: 18px;
}

h4 {
    font-size: 16px;
}

h5 {
    font-size: 15px;
}

h6 {
    font-size: 14px;
}

.page-header {
    font-size: 24px;
    margin: 15px 0 20px;
    padding-bottom: 8px;
}

/* Mengatur dashboard panel */
.huge {
    font-size: 24px;
}

/* Mengatur chart container */
.chart-container {
    height: 250px !important;
}

/* Mengatur datatable */
.dataTables_wrapper .dataTables_length,
.dataTables_wrapper .dataTables_filter,
.dataTables_wrapper .dataTables_info,
.dataTables_wrapper .dataTables_processing,
.dataTables_wrapper .dataTables_paginate {
    padding: 5px 0;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 3px 8px;
}

/* Mengatur select2 */
.select2-container--default .select2-selection--single {
    height: 32px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 32px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 30px;
}

/* Mengatur content wrapper untuk sidebar */
@media (min-width: 768px) {
    #page-wrapper {
        margin-left: 180px;
    }

    .sidebar {
        width: 180px;
    }
}

/* Mengatur sidebar pada mobile */
@media (max-width: 767px) {
    #page-wrapper {
        margin-left: 0;
    }

    .sidebar {
        width: 0;
    }

    .sidebar.active {
        width: 180px;
    }

    #wrapper.sidebar-open #page-wrapper {
        margin-left: 180px;
    }
}
