<?php
// Script untuk debug data nasabah dan hasil prediksi

include 'koneksi.php';

echo "<h2>Debug Data Sistem Prediksi</h2>\n";

// Cek data nasabah terbaru
echo "<h3>1. Data Nasabah Terbaru (5 terakhir)</h3>\n";
$query_nasabah = "SELECT id_nasabah, nama_nasabah, penghasilan, jumlah_pinjaman, kelayakan, created_at 
                  FROM nasabah 
                  ORDER BY created_at DESC 
                  LIMIT 5";
$result_nasabah = mysqli_query($koneksi, $query_nasabah);

if ($result_nasabah) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>ID</th><th>Nama</th><th>Penghasilan</th><th>Pinjaman</th><th>Kelayakan</th><th>Tanggal</th></tr>\n";
    while ($row = mysqli_fetch_assoc($result_nasabah)) {
        echo "<tr>";
        echo "<td>" . $row['id_nasabah'] . "</td>";
        echo "<td>" . htmlspecialchars($row['nama_nasabah']) . "</td>";
        echo "<td>Rp " . number_format($row['penghasilan'], 0, ',', '.') . "</td>";
        echo "<td>Rp " . number_format($row['jumlah_pinjaman'], 0, ',', '.') . "</td>";
        echo "<td>" . ($row['kelayakan'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "Error: " . mysqli_error($koneksi) . "\n";
}

// Cek data hasil_prediksi terbaru
echo "<h3>2. Data Hasil Prediksi Terbaru (5 terakhir)</h3>\n";
$query_prediksi = "SELECT hp.id_prediksi, hp.id_nasabah, n.nama_nasabah, hp.hasil_prediksi, hp.probabilitas, hp.tanggal_prediksi 
                   FROM hasil_prediksi hp 
                   LEFT JOIN nasabah n ON hp.id_nasabah = n.id_nasabah 
                   ORDER BY hp.tanggal_prediksi DESC 
                   LIMIT 5";
$result_prediksi = mysqli_query($koneksi, $query_prediksi);

if ($result_prediksi) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr><th>ID Prediksi</th><th>ID Nasabah</th><th>Nama</th><th>Hasil</th><th>Probabilitas</th><th>Tanggal</th></tr>\n";
    while ($row = mysqli_fetch_assoc($result_prediksi)) {
        echo "<tr>";
        echo "<td>" . $row['id_prediksi'] . "</td>";
        echo "<td>" . $row['id_nasabah'] . "</td>";
        echo "<td>" . htmlspecialchars($row['nama_nasabah'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['hasil_prediksi'] . "</td>";
        echo "<td>" . number_format($row['probabilitas'] * 100, 2) . "%</td>";
        echo "<td>" . $row['tanggal_prediksi'] . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "Error: " . mysqli_error($koneksi) . "\n";
}

// Cek nasabah yang belum diprediksi (query dari prediksi_nasabah.php)
echo "<h3>3. Nasabah yang Belum Diprediksi</h3>\n";
$query_belum = "SELECT n.id_nasabah, n.nama_nasabah, n.penghasilan, n.jumlah_pinjaman, n.created_at
                FROM nasabah n
                LEFT JOIN hasil_prediksi hp ON n.id_nasabah = hp.id_nasabah
                WHERE hp.id_prediksi IS NULL
                ORDER BY n.created_at DESC";
$result_belum = mysqli_query($koneksi, $query_belum);

if ($result_belum) {
    if (mysqli_num_rows($result_belum) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
        echo "<tr><th>ID</th><th>Nama</th><th>Penghasilan</th><th>Pinjaman</th><th>Tanggal</th></tr>\n";
        while ($row = mysqli_fetch_assoc($result_belum)) {
            echo "<tr>";
            echo "<td>" . $row['id_nasabah'] . "</td>";
            echo "<td>" . htmlspecialchars($row['nama_nasabah']) . "</td>";
            echo "<td>Rp " . number_format($row['penghasilan'], 0, ',', '.') . "</td>";
            echo "<td>Rp " . number_format($row['jumlah_pinjaman'], 0, ',', '.') . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "<p>✅ Tidak ada nasabah yang belum diprediksi</p>\n";
    }
} else {
    echo "Error: " . mysqli_error($koneksi) . "\n";
}

// Cek konsistensi data
echo "<h3>4. Konsistensi Data</h3>\n";
$query_konsistensi = "SELECT 
    (SELECT COUNT(*) FROM nasabah WHERE kelayakan IS NOT NULL) as nasabah_dengan_kelayakan,
    (SELECT COUNT(*) FROM hasil_prediksi) as total_hasil_prediksi,
    (SELECT COUNT(DISTINCT id_nasabah) FROM hasil_prediksi) as nasabah_unik_prediksi";
$result_konsistensi = mysqli_query($koneksi, $query_konsistensi);

if ($result_konsistensi) {
    $data = mysqli_fetch_assoc($result_konsistensi);
    echo "<ul>\n";
    echo "<li>Nasabah dengan kelayakan: " . $data['nasabah_dengan_kelayakan'] . "</li>\n";
    echo "<li>Total hasil prediksi: " . $data['total_hasil_prediksi'] . "</li>\n";
    echo "<li>Nasabah unik yang diprediksi: " . $data['nasabah_unik_prediksi'] . "</li>\n";
    echo "</ul>\n";
    
    if ($data['nasabah_dengan_kelayakan'] != $data['nasabah_unik_prediksi']) {
        echo "<p style='color: red;'>⚠️ Ada ketidakkonsistenan data!</p>\n";
    } else {
        echo "<p style='color: green;'>✅ Data konsisten</p>\n";
    }
}

echo "<hr>\n";
echo "<p><a href='prediksi_baru.php'>Kembali ke Prediksi Baru</a> | ";
echo "<a href='prediksi_nasabah.php'>Prediksi Nasabah Website</a> | ";
echo "<a href='daftar_kelayakan.php'>Daftar Kelayakan</a></p>\n";
?>
