<?php
// Mulai session jika belum dimulai
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Cek apakah pengguna sudah login
if (!isset($_SESSION['username']) || !isset($_SESSION['level'])) {
    header("Location: login.php?pesan=belum_login");
    exit;
}

// Fungsi untuk memeriksa akses berdasarkan level
function cek_akses($allowed_levels) {
    if (!in_array($_SESSION['level'], $allowed_levels)) {
        header("Location: login.php?pesan=akses_ditolak");
        exit;
    }
}
?>
