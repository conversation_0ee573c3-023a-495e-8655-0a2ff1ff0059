<?php
include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya admin yang boleh mengakses halaman ini
cek_akses(['admin']);
require_once 'nav.php';
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Data Nasabah</h1>
            </div>

            <!-- Menampilkan Tabel Data -->
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
                        <span>Data Nasabah</span>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover" id="dataTables-nasabah">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th><PERSON><PERSON></th>
                                        <th><PERSON><PERSON></th>
                                        <th><PERSON></th>
                                        <th><PERSON><PERSON><PERSON><PERSON></th>
                                        <th><PERSON><PERSON><PERSON></th>
                                        <th>Jaminan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $sql = "SELECT * FROM nasabah ORDER BY id_nasabah DESC";
                                    $hasil = mysqli_query($koneksi, $sql);
                                    $no = 0;

                                    while ($data = mysqli_fetch_array($hasil)) {
                                        $no++;
                                    ?>
                                    <tr>
                                        <td><?php echo $no; ?></td>
                                        <td><?php echo htmlspecialchars($data["nama_nasabah"]); ?></td>
                                        <td><?php echo htmlspecialchars($data["jenis_kelamin"]); ?></td>
                                        <td><?php echo htmlspecialchars($data["status_perkawinan"]); ?></td>
                                        <td><?php echo formatRupiah($data["penghasilan"]); ?></td>
                                        <td><?php echo formatRupiah($data["jumlah_pinjaman"]); ?></td>
                                        <td><?php echo htmlspecialchars($data["jaminan"]); ?></td>
                                        <td>
                                            <a href="edit_data.php?table=nasabah&id=<?php echo htmlspecialchars($data['id_nasabah']); ?>" class="btn btn-warning btn-sm">
                                                <i class="fa fa-edit"></i></a>
                                            <a href="hapus_data.php?table=nasabah&id=<?php echo htmlspecialchars($data['id_nasabah']); ?>" class="btn btn-danger btn-sm">
                                                <i class="fa fa-trash"></i></a>
                                        </td>
                                    </tr>
                                    <?php
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- end Tabel Data -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</div>
<!-- /#page-wrapper -->

<script>
$(document).ready(function() {
    $('#dataTables-nasabah').DataTable({
        responsive: true,
        "language": {
            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
            "zeroRecords": "Tidak ada data yang ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total entri)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "pageLength": 25,
        "order": [[ 0, "desc" ]] // Urutkan berdasarkan nomor (kolom ke-1) secara descending
    });
});
</script>

<?php
require_once 'foot.php';
?>