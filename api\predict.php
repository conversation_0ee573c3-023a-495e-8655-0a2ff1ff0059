<?php
// Koneksi ke database
include 'koneksi.php';

// Header untuk API
header('Content-Type: application/json');

// Terima request
$request_body = file_get_contents('php://input');

// Log raw input for debugging
error_log("API predict.php - Raw input: " . $request_body);

$data = json_decode($request_body, true);

// Log decoded data for debugging
error_log("API predict.php - Decoded data: " . print_r($data, true));

// Validasi data
if (!$data) {
    error_log("API predict.php - JSON decode error: " . json_last_error_msg());
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON input: ' . json_last_error_msg()]);
    exit;
}

// Check if this is a direct prediction call (with all data) or ID-based call
if (isset($data['umur']) && isset($data['penghasilan'])) {
    // Direct prediction call - forward to Python API
    error_log("API predict.php - Direct prediction call detected, forwarding to Python API");

    // Forward directly to Python API
    $python_api_url = 'http://localhost:5000/api/predict';

    // Inisialisasi cURL
    $ch = curl_init($python_api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $request_body);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    // Eksekusi request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    if ($response === false || !empty($curl_error)) {
        error_log("API predict.php - Error forwarding to Python API: " . $curl_error);
        http_response_code(500);
        echo json_encode(['error' => 'Error connecting to prediction service: ' . $curl_error]);
        exit;
    }

    if ($http_code !== 200) {
        error_log("API predict.php - Python API returned error: " . $http_code . " - " . $response);
        http_response_code($http_code);
        echo $response;
        exit;
    }

    // Return the response from Python API
    header('Content-Type: application/json');
    echo $response;
    exit;
}

// ID-based prediction call
if (!isset($data['id_nasabah'])) {
    http_response_code(400);
    echo json_encode(['error' => 'ID Nasabah tidak ditemukan']);
    exit;
}

$id_nasabah = $data['id_nasabah'];

// Ambil data nasabah dari database
$query = "SELECT * FROM nasabah WHERE id_nasabah = ?";
$stmt = mysqli_prepare($koneksi, $query);
if (!$stmt) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . mysqli_error($koneksi)]);
    exit;
}

mysqli_stmt_bind_param($stmt, "i", $id_nasabah);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) === 0) {
    http_response_code(404);
    echo json_encode(['error' => 'Nasabah tidak ditemukan']);
    mysqli_stmt_close($stmt);
    exit;
}

$nasabah = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

// Lakukan prediksi menggunakan model backpropagation melalui API Python
$hasil_prediksi = prediksi_backpropagation($nasabah);

// Simpan hasil prediksi ke database
$id_prediksi = simpan_hasil_prediksi($nasabah, $hasil_prediksi);

// Kirim hasil prediksi
echo json_encode([
    'id_prediksi' => $id_prediksi,
    'hasil_prediksi' => $hasil_prediksi['hasil_prediksi'],
    'probabilitas' => $hasil_prediksi['probabilitas'],
    'keterangan' => $hasil_prediksi['keterangan']
]);

// Fungsi untuk melakukan prediksi kelayakan kredit menggunakan model backpropagation
function prediksi_backpropagation($nasabah) {
    // URL API Python untuk model backpropagation
    $api_url = 'http://localhost:5000/api/predict';

    // Siapkan data untuk dikirim ke API Python
    $request_data = [
        'id_nasabah' => $nasabah['id_nasabah']
    ];

    // Inisialisasi cURL
    $ch = curl_init($api_url);

    // Set opsi cURL
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);

    // Eksekusi cURL dan ambil respons
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    // Tutup koneksi cURL
    curl_close($ch);

    // Periksa apakah ada error pada cURL
    if ($response === false) {
        // Jika server model tidak berjalan, gunakan fallback ke rule-based system
        error_log("Error connecting to Python API: " . curl_error($ch));
        return [
            'hasil_prediksi' => 'Error',
            'probabilitas' => 0,
            'keterangan' => 'Terjadi kesalahan saat menghubungi server model. Pastikan server model berjalan dengan menjalankan start_model_server.bat.',
            'skor' => 0,
            'max_skor' => 100
        ];
    }

    // Periksa HTTP status code
    if ($http_code != 200) {
        error_log("API returned error with status code: " . $http_code . ", response: " . $response);
        return [
            'hasil_prediksi' => 'Error',
            'probabilitas' => 0,
            'keterangan' => 'Terjadi kesalahan pada server model. Silakan coba lagi nanti.',
            'skor' => 0,
            'max_skor' => 100
        ];
    }

    // Decode respons JSON
    $result = json_decode($response, true);

    // Periksa apakah respons valid
    if (!$result || isset($result['error'])) {
        error_log("Invalid response from API: " . $response);
        return [
            'hasil_prediksi' => 'Error',
            'probabilitas' => 0,
            'keterangan' => isset($result['error']) ? $result['error'] : 'Respons dari server model tidak valid.',
            'skor' => 0,
            'max_skor' => 100
        ];
    }

    // Konversi hasil ke format yang diharapkan
    return [
        'hasil_prediksi' => $result['hasil_prediksi'],
        'probabilitas' => $result['probabilitas'],
        'keterangan' => $result['keterangan'],
        'skor' => isset($result['skor']) ? $result['skor'] : (int)($result['probabilitas'] * 100),
        'max_skor' => isset($result['max_skor']) ? $result['max_skor'] : 100
    ];
}

//Fungsi untuk menyimpan hasil prediksi ke database
function simpan_hasil_prediksi($nasabah, $hasil_prediksi) {
    global $koneksi;

    // Simpan ke tabel laporan_prediksi
    $query_laporan = "INSERT INTO laporan_prediksi (tanggal_prediksi, parameter, akurasi)
                     VALUES (NOW(), 'Backpropagation Neural Network', 87.5)";
    $result_laporan = mysqli_query($koneksi, $query_laporan);
    if (!$result_laporan) {
        error_log("Error inserting laporan_prediksi: " . mysqli_error($koneksi));
        return null;
    }
    $id_laporan = mysqli_insert_id($koneksi);

    // Simpan ke tabel prediksi_detail
    $query_detail = "INSERT INTO prediksi_detail (id_laporan, id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                    VALUES (?, ?, ?, ?, ?, NOW())";
    $stmt = mysqli_prepare($koneksi, $query_detail);
    if (!$stmt) {
        error_log("Error preparing prediksi_detail query: " . mysqli_error($koneksi));
        return null;
    }

    mysqli_stmt_bind_param($stmt, "iisds",
        $id_laporan,
        $nasabah['id_nasabah'],
        $hasil_prediksi['hasil_prediksi'],
        $hasil_prediksi['probabilitas'],
        $hasil_prediksi['keterangan']
    );

    if (!mysqli_stmt_execute($stmt)) {
        error_log("Error executing prediksi_detail query: " . mysqli_stmt_error($stmt));
        mysqli_stmt_close($stmt);
        return null;
    }

    $id_prediksi = mysqli_insert_id($koneksi);
    mysqli_stmt_close($stmt);

    // Update kolom kelayakan di tabel nasabah
    $query_update = "UPDATE nasabah SET kelayakan = ? WHERE id_nasabah = ?";
    $stmt_update = mysqli_prepare($koneksi, $query_update);
    if ($stmt_update) {
        mysqli_stmt_bind_param($stmt_update, "si", $hasil_prediksi['hasil_prediksi'], $nasabah['id_nasabah']);
        mysqli_stmt_execute($stmt_update);
        mysqli_stmt_close($stmt_update);
    }

    // Hitung rasio-rasio penting untuk proses perhitungan
    $penghasilan = (float)$nasabah['penghasilan'];
    $jumlah_pinjaman = (float)$nasabah['jumlah_pinjaman'];
    $jumlah_tanggungan = isset($nasabah['jumlah_tanggungan']) ? max(1, (int)$nasabah['jumlah_tanggungan']) : 1;
    $jangka_waktu = isset($nasabah['jangka_waktu']) ? (int)$nasabah['jangka_waktu'] : 12;

    // Hitung penghasilan per kapita
    $penghasilan_per_kapita = $penghasilan / $jumlah_tanggungan;

    // Hitung rasio pinjaman terhadap penghasilan tahunan
    $rasio_pinjaman = $jumlah_pinjaman / ($penghasilan * 12);

    // Hitung angsuran bulanan (estimasi sederhana dengan bunga 1% flat per bulan)
    $bunga_bulanan = 0.01; // 1% per bulan
    $total_bunga = $jumlah_pinjaman * $bunga_bulanan * $jangka_waktu;
    $total_pembayaran = $jumlah_pinjaman + $total_bunga;
    $angsuran_bulanan = $total_pembayaran / $jangka_waktu;

    // Hitung rasio angsuran terhadap penghasilan
    $rasio_angsuran = $angsuran_bulanan / $penghasilan;

    // Simpan proses perhitungan backpropagation
    $proses_data = [
        // Data input
        'umur' => (int)$nasabah['umur'],
        'jenis_kelamin' => $nasabah['jenis_kelamin'],
        'status_perkawinan' => $nasabah['status_perkawinan'],
        'pekerjaan' => $nasabah['pekerjaan'],
        'penghasilan' => $penghasilan,
        'jumlah_pinjaman' => $jumlah_pinjaman,
        'kepemilikan_rumah' => $nasabah['kepemilikan_rumah'],
        'jaminan' => $nasabah['jaminan'],

        // Fitur tambahan
        'tahun_kendaraan' => isset($nasabah['tahun_kendaraan']) ? (int)$nasabah['tahun_kendaraan'] : 0,
        'status_pajak' => isset($nasabah['status_pajak']) ? $nasabah['status_pajak'] : '',
        'jumlah_tanggungan' => $jumlah_tanggungan,
        'jangka_waktu' => $jangka_waktu,

        // Rasio-rasio penting
        'penghasilan_per_kapita' => $penghasilan_per_kapita,
        'rasio_pinjaman' => $rasio_pinjaman,
        'angsuran_bulanan' => $angsuran_bulanan,
        'rasio_angsuran' => $rasio_angsuran,

        // Hasil prediksi
        'probabilitas' => $hasil_prediksi['probabilitas'],
        'skor' => $hasil_prediksi['skor'],
        'max_skor' => $hasil_prediksi['max_skor'],
        'threshold' => 0.5, // Threshold untuk model backpropagation
        'hasil_prediksi' => $hasil_prediksi['hasil_prediksi']
    ];

    $query_proses = "INSERT INTO proses_perhitungan (id_prediksi, langkah, hasil, waktu_proses)
                    VALUES (?, 'Prediksi Backpropagation', ?, NOW())";
    $stmt_proses = mysqli_prepare($koneksi, $query_proses);
    if ($stmt_proses) {
        $proses_json = json_encode($proses_data);
        mysqli_stmt_bind_param($stmt_proses, "is", $id_prediksi, $proses_json);
        mysqli_stmt_execute($stmt_proses);
        mysqli_stmt_close($stmt_proses);
    }

    return $id_prediksi;
}
?>
