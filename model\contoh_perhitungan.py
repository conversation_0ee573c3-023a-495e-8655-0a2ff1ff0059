import numpy as np
import pandas as pd
from datetime import datetime

# Fungsi untuk menghitung fitur turunan
def hitung_fitur_turunan(data):
    """
    Menghitung fitur turunan dari data nasabah
    
    Parameters:
    -----------
    data : dict
        Dictionary berisi data nasabah
        
    Returns:
    --------
    dict
        Dictionary berisi data nasabah dengan fitur turunan
    """
    # Salin data
    hasil = data.copy()
    
    # Hitung penghasilan per kapita
    if 'jumlah_tanggungan' in hasil and hasil['jumlah_tanggungan'] > 0:
        hasil['penghasilan_per_kapita'] = hasil['penghasilan'] / hasil['jumlah_tanggungan']
    else:
        hasil['penghasilan_per_kapita'] = hasil['penghasilan']
    
    # Hitung rasio pinjaman terhadap penghasilan tahunan
    hasil['rasio_pinjaman'] = hasil['jumlah_pinjaman'] / (hasil['penghasilan'] * 12)
    
    # Hitung angsuran bulanan (dengan bunga flat 1% per bulan)
    bunga_bulanan = 0.01  # 1% per bulan
    hasil['total_bunga'] = hasil['jumlah_pinjaman'] * bunga_bulanan * hasil['waktu_pengembalian']
    hasil['total_pembayaran'] = hasil['jumlah_pinjaman'] + hasil['total_bunga']
    hasil['angsuran_bulanan'] = hasil['total_pembayaran'] / hasil['waktu_pengembalian']
    
    # Hitung rasio angsuran terhadap penghasilan
    hasil['rasio_angsuran'] = hasil['angsuran_bulanan'] / hasil['penghasilan']
    
    # Hitung umur kendaraan
    current_year = datetime.now().year
    hasil['umur_kendaraan'] = current_year - hasil['tahun_kendaraan']
    
    return hasil

# Fungsi untuk menentukan kelayakan kredit
def tentukan_kelayakan(data):
    """
    Menentukan kelayakan kredit berdasarkan aturan sederhana
    
    Parameters:
    -----------
    data : dict
        Dictionary berisi data nasabah dengan fitur turunan
        
    Returns:
    --------
    str
        'Layak' atau 'Tidak Layak'
    float
        Probabilitas kelayakan (0.0 - 1.0)
    """
    # Inisialisasi skor
    skor = 0.0
    
    # Aturan 1: Rasio angsuran terhadap penghasilan
    if data['rasio_angsuran'] <= 0.2:
        skor += 0.4
    elif data['rasio_angsuran'] <= 0.3:
        skor += 0.3
    elif data['rasio_angsuran'] <= 0.4:
        skor += 0.1
    
    # Aturan 2: Rasio pinjaman terhadap penghasilan tahunan
    if data['rasio_pinjaman'] <= 0.5:
        skor += 0.3
    elif data['rasio_pinjaman'] <= 1.0:
        skor += 0.2
    elif data['rasio_pinjaman'] <= 1.5:
        skor += 0.1
    
    # Aturan 3: Penghasilan per kapita
    if data['penghasilan_per_kapita'] >= 5000000:
        skor += 0.2
    elif data['penghasilan_per_kapita'] >= 3000000:
        skor += 0.1
    
    # Aturan 4: Umur kendaraan (untuk agunan)
    if data['jenis_agunan'] == 'BPKB Mobil':
        if data['umur_kendaraan'] <= 5:
            skor += 0.1
        elif data['umur_kendaraan'] <= 10:
            skor += 0.05
    else:  # BPKB Motor
        if data['umur_kendaraan'] <= 3:
            skor += 0.1
        elif data['umur_kendaraan'] <= 7:
            skor += 0.05
    
    # Aturan 5: Status pajak
    if data['status_pajak'] == 'Aktif':
        skor += 0.1
    
    # Tentukan kelayakan berdasarkan skor
    probabilitas = min(1.0, skor)  # Pastikan probabilitas tidak lebih dari 1.0
    
    if probabilitas >= 0.6:
        return 'Layak', probabilitas
    else:
        return 'Tidak Layak', probabilitas

# Fungsi untuk menampilkan penjelasan keputusan
def jelaskan_keputusan(data, kelayakan, probabilitas):
    """
    Menampilkan penjelasan keputusan kelayakan kredit
    
    Parameters:
    -----------
    data : dict
        Dictionary berisi data nasabah dengan fitur turunan
    kelayakan : str
        Hasil kelayakan ('Layak' atau 'Tidak Layak')
    probabilitas : float
        Probabilitas kelayakan (0.0 - 1.0)
    """
    print(f"\nHasil Prediksi: {kelayakan} (Probabilitas: {probabilitas:.4f})")
    
    print("\nPenjelasan Keputusan:")
    if kelayakan == 'Layak':
        print("Nasabah dinyatakan LAYAK untuk kredit karena:")
        if data['rasio_angsuran'] < 0.3:
            print(f"- Rasio angsuran terhadap penghasilan ({data['rasio_angsuran']:.2f}) < 0.3 (baik)")
        if data['rasio_pinjaman'] < 1.0:
            print(f"- Rasio pinjaman terhadap penghasilan tahunan ({data['rasio_pinjaman']:.2f}) < 1.0 (baik)")
        if data['penghasilan_per_kapita'] > 3000000:
            print(f"- Penghasilan per kapita (Rp {data['penghasilan_per_kapita']:,.2f}) cukup tinggi")
        if data['status_pajak'] == 'Aktif':
            print(f"- Status pajak kendaraan aktif")
        if data['jenis_agunan'] == 'BPKB Mobil' and data['umur_kendaraan'] <= 5:
            print(f"- Agunan berupa mobil dengan umur kendaraan yang masih baru ({data['umur_kendaraan']} tahun)")
        elif data['jenis_agunan'] == 'BPKB Motor' and data['umur_kendaraan'] <= 3:
            print(f"- Agunan berupa motor dengan umur kendaraan yang masih baru ({data['umur_kendaraan']} tahun)")
    else:
        print("Nasabah dinyatakan TIDAK LAYAK untuk kredit karena:")
        if data['rasio_angsuran'] > 0.3:
            print(f"- Rasio angsuran terhadap penghasilan ({data['rasio_angsuran']:.2f}) > 0.3 (terlalu tinggi)")
        if data['rasio_pinjaman'] > 1.0:
            print(f"- Rasio pinjaman terhadap penghasilan tahunan ({data['rasio_pinjaman']:.2f}) > 1.0 (terlalu tinggi)")
        if data['penghasilan_per_kapita'] < 3000000:
            print(f"- Penghasilan per kapita (Rp {data['penghasilan_per_kapita']:,.2f}) terlalu rendah")
        if data['status_pajak'] == 'Tidak Aktif':
            print(f"- Status pajak kendaraan tidak aktif")
        if data['jenis_agunan'] == 'BPKB Mobil' and data['umur_kendaraan'] > 10:
            print(f"- Agunan berupa mobil dengan umur kendaraan yang sudah tua ({data['umur_kendaraan']} tahun)")
        elif data['jenis_agunan'] == 'BPKB Motor' and data['umur_kendaraan'] > 7:
            print(f"- Agunan berupa motor dengan umur kendaraan yang sudah tua ({data['umur_kendaraan']} tahun)")

# Fungsi untuk membuat contoh data nasabah
def buat_contoh_data(num_samples=5):
    """
    Membuat contoh data nasabah
    
    Parameters:
    -----------
    num_samples : int
        Jumlah contoh data yang akan dibuat
        
    Returns:
    --------
    list
        List berisi dictionary data nasabah
    """
    # Set random seed untuk reproduksibilitas
    np.random.seed(42)
    
    # Definisikan rentang nilai untuk setiap fitur
    umur_range = (25, 60)
    penghasilan_range = (3000000, 15000000)
    jumlah_pinjaman_range = (5000000, 50000000)
    waktu_pengembalian_range = (12, 60)
    jumlah_tanggungan_range = (0, 5)
    tahun_kendaraan_range = (2010, 2023)
    
    # Buat contoh data
    contoh_data = []
    for i in range(num_samples):
        # Variasi nilai untuk setiap sampel
        data = {
            'umur': np.random.randint(umur_range[0], umur_range[1]),
            'jenis_kelamin': np.random.choice(['Laki-laki', 'Perempuan']),
            'status_pernikahan': np.random.choice(['Menikah', 'Belum Menikah', 'Cerai']),
            'pekerjaan': np.random.choice(['PNS', 'Karyawan Swasta', 'Wiraswasta', 'Profesional', 'Lainnya']),
            'penghasilan': np.random.randint(penghasilan_range[0], penghasilan_range[1]),
            'jumlah_pinjaman': np.random.randint(jumlah_pinjaman_range[0], jumlah_pinjaman_range[1]),
            'kepemilikan_rumah': np.random.choice(['Milik Sendiri', 'Sewa', 'Milik Keluarga']),
            'jenis_agunan': np.random.choice(['BPKB Mobil', 'BPKB Motor']),
            'tahun_kendaraan': np.random.randint(tahun_kendaraan_range[0], tahun_kendaraan_range[1]),
            'status_pajak': np.random.choice(['Aktif', 'Tidak Aktif']),
            'jumlah_tanggungan': np.random.randint(jumlah_tanggungan_range[0], jumlah_tanggungan_range[1]),
            'waktu_pengembalian': np.random.randint(waktu_pengembalian_range[0], waktu_pengembalian_range[1])
        }
        contoh_data.append(data)
    
    return contoh_data

# Program utama
if __name__ == "__main__":
    print("="*50)
    print("DEMONSTRASI PROSES PERHITUNGAN PREDIKSI KREDIT")
    print("="*50)
    
    # Buat contoh data
    contoh_data = buat_contoh_data(5)
    
    # Proses setiap contoh data
    for i, data in enumerate(contoh_data):
        print(f"\n\nCONTOH DATA #{i+1}")
        print("="*30)
        
        # Tampilkan data asli
        print("\nData Nasabah:")
        for key, value in data.items():
            if key in ['penghasilan', 'jumlah_pinjaman']:
                print(f"- {key}: Rp {value:,}")
            else:
                print(f"- {key}: {value}")
        
        # Hitung fitur turunan
        data_dengan_fitur = hitung_fitur_turunan(data)
        
        # Tampilkan fitur turunan
        print("\nFitur Turunan:")
        print(f"- penghasilan_per_kapita: Rp {data_dengan_fitur['penghasilan_per_kapita']:,.2f}")
        print(f"- rasio_pinjaman: {data_dengan_fitur['rasio_pinjaman']:.4f}")
        print(f"- total_bunga: Rp {data_dengan_fitur['total_bunga']:,.2f}")
        print(f"- total_pembayaran: Rp {data_dengan_fitur['total_pembayaran']:,.2f}")
        print(f"- angsuran_bulanan: Rp {data_dengan_fitur['angsuran_bulanan']:,.2f}")
        print(f"- rasio_angsuran: {data_dengan_fitur['rasio_angsuran']:.4f}")
        print(f"- umur_kendaraan: {data_dengan_fitur['umur_kendaraan']} tahun")
        
        # Tentukan kelayakan
        kelayakan, probabilitas = tentukan_kelayakan(data_dengan_fitur)
        
        # Tampilkan penjelasan keputusan
        jelaskan_keputusan(data_dengan_fitur, kelayakan, probabilitas)
        
        print("-"*50)
    
    # Simpan contoh data ke file CSV
    df_contoh = pd.DataFrame(contoh_data)
    df_contoh.to_csv('model/contoh_data.csv', index=False)
    print("\nContoh data telah disimpan ke model/contoh_data.csv")
    
    print("\n" + "="*50)
    print("SELESAI")
    print("="*50)
