<?php
include 'koneksi.php';
include 'cek_session.php';
cek_akses(['admin', 'analis']);

// Set header untuk download Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="Data_Kelayakan_Kredit_' . date('Y-m-d_H-i-s') . '.xls"');
header('Cache-Control: max-age=0');

// Ambil filter jika ada
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$where_clause = "";

switch ($filter) {
    case 'layak':
        $where_clause = "WHERE kelayakan = 'Layak'";
        break;
    case 'tidak_layak':
        $where_clause = "WHERE kelayakan = 'Tidak Layak'";
        break;
    case 'belum_diprediksi':
        $where_clause = "WHERE kelayakan IS NULL";
        break;
    default:
        $where_clause = "WHERE kelayakan IS NOT NULL";
        break;
}

// Query untuk mengambil data nasabah
$sql = "SELECT 
    id_nasabah,
    nama_nasabah,
    umur,
    jenis_kelamin,
    status_perkawinan,
    pekerjaan,
    penghasilan,
    jumlah_tanggungan,
    jumlah_pinjaman,
    jangka_waktu,
    kepemilikan_rumah,
    jaminan,
    tahun_kendaraan,
    status_pajak,
    tujuan_pinjaman,
    kelayakan,
    created_at
FROM nasabah 
$where_clause
ORDER BY created_at DESC";

$result = mysqli_query($koneksi, $sql);

// Mulai output HTML untuk Excel
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Data Kelayakan Kredit</title>
    <style>
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #000; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .center { text-align: center; }
        .right { text-align: right; }
        .layak { background-color: #d4edda; color: #155724; }
        .tidak-layak { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>';

echo '<h2>LAPORAN DATA KELAYAKAN KREDIT</h2>';
echo '<p>Tanggal Export: ' . date('d/m/Y H:i:s') . '</p>';
echo '<p>Filter: ' . ucfirst(str_replace('_', ' ', $filter)) . '</p>';
echo '<br>';

echo '<table>';
echo '<thead>';
echo '<tr>';
echo '<th>No</th>';
echo '<th>ID Nasabah</th>';
echo '<th>Nama Nasabah</th>';
echo '<th>Umur</th>';
echo '<th>Jenis Kelamin</th>';
echo '<th>Status Perkawinan</th>';
echo '<th>Pekerjaan</th>';
echo '<th>Penghasilan</th>';
echo '<th>Jumlah Tanggungan</th>';
echo '<th>Jumlah Pinjaman</th>';
echo '<th>Jangka Waktu (Bulan)</th>';
echo '<th>Kepemilikan Rumah</th>';
echo '<th>Jaminan</th>';
echo '<th>Tahun Kendaraan</th>';
echo '<th>Status Pajak</th>';
echo '<th>Tujuan Pinjaman</th>';
echo '<th>Hasil Kelayakan</th>';
echo '<th>Tanggal Input</th>';
echo '</tr>';
echo '</thead>';
echo '<tbody>';

$no = 1;
$total_penghasilan = 0;
$total_pinjaman = 0;
$count_layak = 0;
$count_tidak_layak = 0;

while ($row = mysqli_fetch_assoc($result)) {
    $kelayakan_class = '';
    if ($row['kelayakan'] == 'Layak') {
        $kelayakan_class = 'layak';
        $count_layak++;
    } elseif ($row['kelayakan'] == 'Tidak Layak') {
        $kelayakan_class = 'tidak-layak';
        $count_tidak_layak++;
    }
    
    $total_penghasilan += $row['penghasilan'];
    $total_pinjaman += $row['jumlah_pinjaman'];
    
    echo '<tr>';
    echo '<td class="center">' . $no . '</td>';
    echo '<td class="center">' . $row['id_nasabah'] . '</td>';
    echo '<td>' . htmlspecialchars($row['nama_nasabah']) . '</td>';
    echo '<td class="center">' . $row['umur'] . '</td>';
    echo '<td>' . $row['jenis_kelamin'] . '</td>';
    echo '<td>' . $row['status_perkawinan'] . '</td>';
    echo '<td>' . $row['pekerjaan'] . '</td>';
    echo '<td class="right">Rp ' . number_format($row['penghasilan'], 0, ',', '.') . '</td>';
    echo '<td class="center">' . $row['jumlah_tanggungan'] . '</td>';
    echo '<td class="right">Rp ' . number_format($row['jumlah_pinjaman'], 0, ',', '.') . '</td>';
    echo '<td class="center">' . $row['jangka_waktu'] . '</td>';
    echo '<td>' . $row['kepemilikan_rumah'] . '</td>';
    echo '<td>' . $row['jaminan'] . '</td>';
    echo '<td class="center">' . $row['tahun_kendaraan'] . '</td>';
    echo '<td>' . $row['status_pajak'] . '</td>';
    echo '<td>' . htmlspecialchars($row['tujuan_pinjaman']) . '</td>';
    echo '<td class="center ' . $kelayakan_class . '">' . ($row['kelayakan'] ?? 'Belum Diprediksi') . '</td>';
    echo '<td class="center">' . date('d/m/Y H:i', strtotime($row['created_at'])) . '</td>';
    echo '</tr>';
    $no++;
}

echo '</tbody>';
echo '</table>';

// Tampilkan ringkasan
echo '<br><br>';
echo '<h3>RINGKASAN DATA</h3>';
echo '<table style="width: 50%;">';
echo '<tr><td><strong>Total Data</strong></td><td class="right"><strong>' . ($no - 1) . '</strong></td></tr>';
echo '<tr><td>Nasabah Layak</td><td class="right">' . $count_layak . '</td></tr>';
echo '<tr><td>Nasabah Tidak Layak</td><td class="right">' . $count_tidak_layak . '</td></tr>';
echo '<tr><td>Total Penghasilan</td><td class="right">Rp ' . number_format($total_penghasilan, 0, ',', '.') . '</td></tr>';
echo '<tr><td>Total Pinjaman</td><td class="right">Rp ' . number_format($total_pinjaman, 0, ',', '.') . '</td></tr>';

if ($no > 1) {
    echo '<tr><td>Rata-rata Penghasilan</td><td class="right">Rp ' . number_format($total_penghasilan / ($no - 1), 0, ',', '.') . '</td></tr>';
    echo '<tr><td>Rata-rata Pinjaman</td><td class="right">Rp ' . number_format($total_pinjaman / ($no - 1), 0, ',', '.') . '</td></tr>';
}

echo '</table>';

// Tampilkan statistik kelayakan
if ($count_layak + $count_tidak_layak > 0) {
    $persentase_layak = ($count_layak / ($count_layak + $count_tidak_layak)) * 100;
    $persentase_tidak_layak = ($count_tidak_layak / ($count_layak + $count_tidak_layak)) * 100;
    
    echo '<br>';
    echo '<h3>STATISTIK KELAYAKAN</h3>';
    echo '<table style="width: 50%;">';
    echo '<tr><td>Persentase Layak</td><td class="right">' . number_format($persentase_layak, 2) . '%</td></tr>';
    echo '<tr><td>Persentase Tidak Layak</td><td class="right">' . number_format($persentase_tidak_layak, 2) . '%</td></tr>';
    echo '</table>';
}

echo '<br><br>';
echo '<p><em>Laporan ini digenerate otomatis oleh Sistem Prediksi Kelayakan Kredit</em></p>';
echo '<p><em>Menggunakan Algoritma Backpropagation Neural Network</em></p>';

echo '</body></html>';

// Tutup koneksi database
mysqli_close($koneksi);
?>
