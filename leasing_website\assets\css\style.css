/* Custom CSS for Leasing Website - Matching Sistem Prediksi Kelayakan Kredit */

/* General Styles */

body {
    font-family: sans-serif;
    color: #333;
    line-height: 1.6;
    font-size: 16px;
    background-color: #f8f8f8;
    padding-top: 0;
    margin-top: 0;
}
  .html, body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        .body {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        .header {
            margin-top: 0 !important;
        }

/* Container adjustments */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-fluid {
    padding: 0 30px;
    max-width: 1400px;
    margin: 0 auto;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    opacity: 0.8;
}

.btn {
    border-radius: 4px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #337ab7;
    border-color: #2e6da4;
}

.btn-primary:hover {
    background-color: #286090;
    border-color: #204d74;
}

/* Page Wrapper Styles */
#page-wrapper {
    padding: 30px;
    min-height: 568px;
    background-color: #fff;
    margin: 0 auto 30px;
    max-width: 1200px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.page-header {
    padding-bottom: 9px;
    margin: 20px 0 20px;
    border-bottom: 1px solid #eee;
}

/* Panel Styles */
.panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.panel-body {
    padding: 15px;
}

/* Form Styles */
.form-horizontal .form-group {
    margin-right: -15px;
    margin-left: -15px;
    margin-bottom: 15px;
}

.form-control {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}

.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
}

/* Navbar Styles */
.navbar-default {
    background-color: #f8f8f8;
    border-color: #e7e7e7;
}

.navbar-default .navbar-brand {
    color: #337ab7;
}

.navbar-default .navbar-nav > li > a {
    color: #777;
}

.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
    color: #333;
    background-color: transparent;
}

.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
    color: #555;
    background-color: #e7e7e7;
}

/* Header */
header {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('../img/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero-section h1 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

/* Features Section */
.features-section {
    padding: 80px 0;
}

.feature-box {
    text-align: center;
    padding: 30px 20px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.feature-box:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 3rem;
    color: #0056b3;
    margin-bottom: 20px;
}

/* Services Section */
.services-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.service-card {
    border: none;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
}

.service-card img {
    height: 200px;
    object-fit: cover;
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
}

.testimonial-card {
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.testimonial-card img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
}

.testimonial-card .rating {
    color: #ffc107;
    margin-bottom: 15px;
}

/* Application Form */
.application-form {
    padding: 40px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    background-color: white;
}

.form-section {
    margin-bottom: 30px;
}

.form-section h3 {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

/* Success Page */
.success-message {
    text-align: center;
    padding: 50px 20px;
}

.success-icon {
    font-size: 5rem;
    color: #28a745;
    margin-bottom: 20px;
}

/* Footer */
footer {
    background-color: #343a40;
}

footer a {
    color: white;
}

footer a:hover {
    color: #f8f9fa;
    text-decoration: underline;
}

.social-icons a {
    display: inline-block;
    width: 36px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 10px;
}

.social-icons a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .application-form {
        padding: 20px;
    }
}
