<?php
// Aktifkan error reporting untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Koneksi ke database
require_once 'koneksi.php';
require_once 'date_helper.php';

// Terima data dari request POST atau GET
$id_nasabah = $_REQUEST['id_nasabah'] ?? null;

// Buat log untuk debugging
$log_file = fopen("api_predict_log.txt", "a");
fwrite($log_file, date("Y-m-d H:i:s") . " - Request received for ID: " . ($id_nasabah ?? 'NULL') . "\n");

// Validasi parameter ID nasabah
if (!$id_nasabah) {
    fwrite($log_file, date("Y-m-d H:i:s") . " - Error: ID Nasabah tidak ditemukan\n");
    echo json_encode(['error' => 'ID Nasabah tidak ditemukan']);
    fclose($log_file);
    exit;
}

// Ambil data nasabah dari database berdasarkan ID
$query = "SELECT * FROM nasabah WHERE id_nasabah = ?";
$stmt = $koneksi->prepare($query);
$stmt->bind_param("i", $id_nasabah);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    fwrite($log_file, date("Y-m-d H:i:s") . " - Error: Nasabah dengan ID $id_nasabah tidak ditemukan\n");
    echo json_encode(['error' => 'Nasabah tidak ditemukan']);
    fclose($log_file);
    exit;
}

$nasabah = $result->fetch_assoc();
fwrite($log_file, date("Y-m-d H:i:s") . " - Nasabah data retrieved: " . json_encode($nasabah) . "\n");

// Lakukan prediksi dengan model backpropagation Python
try {
    $hasil_prediksi = prediksi_dengan_backpropagation($nasabah);
    fwrite($log_file, date("Y-m-d H:i:s") . " - Prediction result: " . json_encode($hasil_prediksi) . "\n");
} catch (Exception $e) {
    fwrite($log_file, date("Y-m-d H:i:s") . " - Error during prediction: " . $e->getMessage() . "\n");
    echo json_encode(['error' => 'Gagal melakukan prediksi: ' . $e->getMessage()]);
    fclose($log_file);
    exit;
}

// Simpan hasil prediksi ke database (hanya jika Python API tidak berhasil menyimpan)
$id_prediksi = null;
if (!isset($hasil_prediksi['id_prediksi']) || empty($hasil_prediksi['id_prediksi'])) {
    try {
        $id_prediksi = simpan_hasil_prediksi($nasabah, $hasil_prediksi);
        fwrite($log_file, date("Y-m-d H:i:s") . " - Prediction saved by PHP with ID: $id_prediksi\n");
    } catch (Exception $e) {
        fwrite($log_file, date("Y-m-d H:i:s") . " - Error saving prediction: " . $e->getMessage() . "\n");
        echo json_encode(['error' => 'Gagal menyimpan hasil prediksi: ' . $e->getMessage()]);
        fclose($log_file);
        exit;
    }
} else {
    $id_prediksi = $hasil_prediksi['id_prediksi'];
    fwrite($log_file, date("Y-m-d H:i:s") . " - Prediction already saved by Python with ID: $id_prediksi\n");
}

// Kirim hasil prediksi
$response = [
    'id_prediksi' => $id_prediksi,
    'hasil_prediksi' => $hasil_prediksi['hasil_prediksi'],
    'probabilitas' => $hasil_prediksi['probabilitas'],
    'keterangan' => $hasil_prediksi['keterangan']
];

fwrite($log_file, date("Y-m-d H:i:s") . " - Sending response: " . json_encode($response) . "\n");
fclose($log_file);

echo json_encode($response);

/**
 * Fungsi untuk memanggil model backpropagation Python melalui API
 */
function prediksi_dengan_backpropagation($nasabah) {
    // Validasi data yang diperlukan
    $required_fields = ['penghasilan', 'jumlah_pinjaman', 'pekerjaan', 'jaminan', 'kepemilikan_rumah', 'umur'];
    foreach ($required_fields as $field) {
        if (!isset($nasabah[$field])) {
            throw new Exception("Field $field tidak ditemukan dalam data nasabah");
        }
    }

    // Pastikan jumlah_tanggungan ada, jika tidak set default ke 1
    if (!isset($nasabah['jumlah_tanggungan']) || empty($nasabah['jumlah_tanggungan'])) {
        $nasabah['jumlah_tanggungan'] = 1;
    }

    // Coba menggunakan API Python jika tersedia
    try {
        // URL API model Python
        $api_url = 'http://localhost:5000/api/predict';

        // Siapkan data untuk dikirim ke API
        $data_untuk_api = [
            'id_nasabah' => $nasabah['id_nasabah']
        ];

        // Konversi ke JSON
        $json_data = json_encode($data_untuk_api);

        // Log untuk debugging
        error_log("Mengirim request ke API model Python: " . $json_data);

        // Inisialisasi cURL
        $ch = curl_init($api_url);

        // Set opsi cURL
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($json_data)
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Timeout 10 detik

        // Eksekusi request
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);

        // Tutup cURL
        curl_close($ch);

        // Log untuk debugging
        error_log("Response dari API model Python (HTTP $http_code): " . $response);

        // Cek error cURL
        if ($response === false) {
            error_log("Error cURL: " . $curl_error);
            error_log("Server Python tidak tersedia, menggunakan prediksi fallback");

            // Jika server Python tidak tersedia, gunakan prediksi fallback
            // Langsung ke bagian catch untuk menggunakan prediksi sederhana
            throw new Exception("Server Python tidak tersedia");
        }

        // Cek HTTP status code
        if ($http_code !== 200) {
            error_log("HTTP Error: " . $http_code);
            throw new Exception("API model Python mengembalikan status error: " . $http_code);
        }

        // Parse hasil dari API
        $hasil_api = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Response API tidak valid JSON: " . $response);
            throw new Exception("Gagal memproses hasil dari API model Python");
        }

        // Cek error dari API
        if (isset($hasil_api['error'])) {
            error_log("Error dari API model Python: " . $hasil_api['error']);
            throw new Exception("API model Python mengembalikan error: " . $hasil_api['error']);
        }

        // Cek kelengkapan data
        if (!isset($hasil_api['hasil_prediksi']) || !isset($hasil_api['probabilitas'])) {
            error_log("Response API tidak lengkap: " . print_r($hasil_api, true));
            throw new Exception("Hasil prediksi tidak valid dari API model Python");
        }

        // Format hasil sesuai dengan yang diharapkan
        return [
            'id_prediksi' => isset($hasil_api['id_prediksi']) ? $hasil_api['id_prediksi'] : null,
            'hasil_prediksi' => $hasil_api['hasil_prediksi'],
            'probabilitas' => floatval($hasil_api['probabilitas']),
            'keterangan' => $hasil_api['keterangan'] ?? 'Prediksi menggunakan model backpropagation neural network',
            'skor' => isset($hasil_api['skor']) ? intval($hasil_api['skor']) : 0,
            'max_skor' => isset($hasil_api['max_skor']) ? intval($hasil_api['max_skor']) : 100
        ];
    } catch (Exception $api_error) {
        // Jika API tidak tersedia, lakukan prediksi sederhana berdasarkan data nasabah
        error_log("Error saat menggunakan API Python: " . $api_error->getMessage());
        error_log("Melakukan prediksi sederhana sebagai fallback...");

                // Hitung rasio pinjaman terhadap penghasilan bulanan
        $jumlah_tanggungan = max(1, intval($nasabah['jumlah_tanggungan']));
        // Pastikan penghasilan dan jumlah pinjaman adalah nilai numerik
        $penghasilan = is_numeric($nasabah['penghasilan']) ? floatval($nasabah['penghasilan']) : floatval(str_replace('.', '', $nasabah['penghasilan']));
        $jumlah_pinjaman = is_numeric($nasabah['jumlah_pinjaman']) ? floatval($nasabah['jumlah_pinjaman']) : floatval(str_replace('.', '', $nasabah['jumlah_pinjaman']));

        // Hitung penghasilan per kapita tahunan
        $penghasilan_tahunan = $penghasilan * 12;
        $penghasilan_per_kapita_tahunan = $penghasilan_tahunan / $jumlah_tanggungan;

        // Rasio pinjaman terhadap penghasilan per kapita tahunan
        $rasio_pinjaman = $jumlah_pinjaman / $penghasilan_per_kapita_tahunan;

        // Tentukan hasil prediksi berdasarkan rasio pinjaman tahunan
        // Nilai threshold ditingkatkan untuk memberikan lebih banyak kesempatan "Layak"
        $hasil_prediksi = ($rasio_pinjaman <= 0.9) ? 'Layak' : 'Tidak Layak'; // 0.9 = 90% dari penghasilan tahunan per kapita

        // Hitung probabilitas berdasarkan rasio pinjaman
        if ($hasil_prediksi == 'Layak') {
            // Untuk hasil "Layak", probabilitas menurun saat rasio pinjaman mendekati threshold
            // Meningkatkan probabilitas minimum untuk hasil "Layak"
            $probabilitas = max(0.6, min(0.95, 1 - ($rasio_pinjaman / 0.9)));
        } else {
            // Untuk hasil "Tidak Layak", probabilitas meningkat saat rasio pinjaman melebihi threshold
            // Meningkatkan probabilitas minimum untuk hasil "Tidak Layak"
            $probabilitas = max(0.3, min(0.6, ($rasio_pinjaman - 0.9) / 0.9));
        }

        // Format angka untuk keterangan
        $penghasilan_fmt = "Rp " . number_format($penghasilan, 0, ',', '.') . " per bulan";
        $penghasilan_tahunan_fmt = "Rp " . number_format($penghasilan_tahunan, 0, ',', '.') . " per tahun";
        $penghasilan_per_kapita_tahunan_fmt = "Rp " . number_format($penghasilan_per_kapita_tahunan, 0, ',', '.') . " per tahun";
        $jumlah_pinjaman_fmt = "Rp " . number_format($jumlah_pinjaman, 0, ',', '.');

        // Buat keterangan dengan bahasa yang lebih manusiawi
        if ($hasil_prediksi == 'Layak') {
            $prob_display = number_format($probabilitas * 100, 2);
            $keterangan = "Nasabah diprediksi layak menerima kredit dengan probabilitas {$prob_display}%. ";
            $keterangan .= "Jumlah pinjaman yang diajukan ({$jumlah_pinjaman_fmt}) masih dalam batas wajar ";
            $keterangan .= "dibandingkan dengan penghasilan per kapita tahunan nasabah ({$penghasilan_per_kapita_tahunan_fmt}). ";
            $keterangan .= "Rasio pinjaman terhadap penghasilan per kapita tahunan adalah " . number_format($rasio_pinjaman * 100, 0) . "%, ";
            $keterangan .= "yang masih dalam batas aman (di bawah 90%).";

            // Tambahkan faktor pendukung
            $faktor_pendukung = [];

            // Cek penghasilan
            if ($penghasilan > 5000000) {
                $faktor_pendukung[] = "penghasilan yang baik ({$penghasilan_fmt})";
            }

            // Cek pekerjaan
            $pekerjaan = strtolower($nasabah['pekerjaan']);
            if (in_array($pekerjaan, ['pns', 'pegawai negeri', 'dokter', 'dosen', 'karyawan', 'pegawai swasta', 'guru', 'wiraswasta', 'pengusaha'])) {
                $faktor_pendukung[] = "pekerjaan yang stabil ({$nasabah['pekerjaan']})";
            }

            // Cek jaminan
            $jaminan = strtolower($nasabah['jaminan']);
            if (strpos($jaminan, 'bpkb mobil') !== false) {
                $faktor_pendukung[] = "jaminan yang memadai (BPKB Mobil)";
            } elseif (strpos($jaminan, 'bpkb motor') !== false) {
                // Cek tahun kendaraan jika ada
                if (isset($nasabah['tahun_kendaraan']) && !empty($nasabah['tahun_kendaraan'])) {
                    $tahun_kendaraan = intval($nasabah['tahun_kendaraan']);
                    $tahun_sekarang = intval(date('Y'));
                    $umur_kendaraan = $tahun_sekarang - $tahun_kendaraan;

                    if ($umur_kendaraan <= 5) {
                        $faktor_pendukung[] = "jaminan BPKB Motor dengan umur kendaraan yang masih baru ({$umur_kendaraan} tahun)";
                    } else {
                        $faktor_pendukung[] = "jaminan BPKB Motor";
                    }
                } else {
                    $faktor_pendukung[] = "jaminan BPKB Motor";
                }
            }

            // Tambahkan faktor pendukung ke keterangan jika ada
            if (!empty($faktor_pendukung)) {
                $keterangan .= " Faktor pendukung: " . implode(", ", $faktor_pendukung) . ".";
            }
        } else {
            $prob_display = number_format((1 - $probabilitas) * 100, 2);
            $keterangan = "Nasabah diprediksi tidak layak menerima kredit dengan probabilitas {$prob_display}%. ";
            $keterangan .= "Jumlah pinjaman yang diajukan ({$jumlah_pinjaman_fmt}) terlalu besar ";
            $keterangan .= "dibandingkan dengan penghasilan per kapita tahunan nasabah ({$penghasilan_per_kapita_tahunan_fmt}). ";
            $keterangan .= "Rasio pinjaman terhadap penghasilan per kapita tahunan adalah " . number_format($rasio_pinjaman * 100, 0) . "%, ";
            $keterangan .= "yang melebihi batas aman (di atas 90%).";

            // Tambahkan rekomendasi
            $keterangan .= " Rekomendasi: ";

            // Hitung jumlah pinjaman yang lebih sesuai
            $pinjaman_rekomendasi = $penghasilan_per_kapita_tahunan * 0.8; // 80% dari penghasilan per kapita tahunan
            $pinjaman_rekomendasi_fmt = "Rp " . number_format($pinjaman_rekomendasi, 0, ',', '.');

            $keterangan .= "Pertimbangkan untuk mengajukan pinjaman yang lebih kecil (sekitar {$pinjaman_rekomendasi_fmt}) ";
            $keterangan .= "atau meningkatkan penghasilan untuk meningkatkan kelayakan kredit.";
        }

        return [
            'hasil_prediksi' => $hasil_prediksi,
            'probabilitas' => $probabilitas,
            'keterangan' => $keterangan,
            'skor' => $hasil_prediksi == 'Layak' ? 75 : 45,
            'max_skor' => 100
        ];
    }
}


/**
 * Fungsi untuk menyimpan hasil prediksi ke database
 */
function simpan_hasil_prediksi($nasabah, $hasil_prediksi) {
    global $koneksi;

    // Mulai transaksi
    $koneksi->begin_transaction();

    try {
        // Cek apakah tabel prediksi_detail ada
        $check_table = "SHOW TABLES LIKE 'prediksi_detail'";
        $table_result = $koneksi->query($check_table);
        $table_exists = $table_result && $table_result->num_rows > 0;

        $existing_prediksi = null;
        if ($table_exists) {
            // Cek apakah nasabah sudah memiliki prediksi sebelumnya
            $check_query = "SELECT pd.id_prediksi, pd.id_laporan
                           FROM prediksi_detail pd
                           WHERE pd.id_nasabah = ?
                           ORDER BY pd.tanggal_prediksi DESC LIMIT 1";
            $check_stmt = $koneksi->prepare($check_query);
            $check_stmt->bind_param("i", $nasabah['id_nasabah']);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            $existing_prediksi = $check_result->fetch_assoc();
            $check_stmt->close();
        }

        // Jika nasabah sudah memiliki prediksi sebelumnya dan tabel ada, update data yang ada
        if ($existing_prediksi && $table_exists) {
            $id_prediksi = $existing_prediksi['id_prediksi'];
            $id_laporan = $existing_prediksi['id_laporan'];

            // 1. Update tabel laporan_prediksi
            $query_laporan_update = "UPDATE laporan_prediksi
                                    SET tanggal_prediksi = NOW(),
                                        parameter = 'Backpropagation Neural Network',
                                        akurasi = 87.5
                                    WHERE id_laporan = ?";
            $stmt_laporan = $koneksi->prepare($query_laporan_update);
            $stmt_laporan->bind_param("i", $id_laporan);
            if (!$stmt_laporan->execute()) {
                throw new Exception("Gagal mengupdate laporan prediksi: " . $stmt_laporan->error);
            }
            $stmt_laporan->close();

            // 2. Update tabel prediksi_detail
            $query_detail_update = "UPDATE prediksi_detail
                                   SET hasil_prediksi = ?,
                                       probabilitas = ?,
                                       keterangan = ?,
                                       tanggal_prediksi = NOW()
                                   WHERE id_prediksi = ?";
            $stmt_detail = $koneksi->prepare($query_detail_update);
            $stmt_detail->bind_param("sdsi",
                $hasil_prediksi['hasil_prediksi'],
                $hasil_prediksi['probabilitas'],
                $hasil_prediksi['keterangan'],
                $id_prediksi
            );
            if (!$stmt_detail->execute()) {
                throw new Exception("Gagal mengupdate detail prediksi: " . $stmt_detail->error);
            }
            $stmt_detail->close();

            // 2.1 Update tabel hasil_prediksi untuk kompatibilitas
            $query_hasil_update = "UPDATE hasil_prediksi
                                  SET hasil_prediksi = ?,
                                      probabilitas = ?,
                                      keterangan = ?,
                                      tanggal_prediksi = NOW()
                                  WHERE id_nasabah = ?";
            $stmt_hasil = $koneksi->prepare($query_hasil_update);
            $stmt_hasil->bind_param("sdsi",
                $hasil_prediksi['hasil_prediksi'],
                $hasil_prediksi['probabilitas'],
                $hasil_prediksi['keterangan'],
                $nasabah['id_nasabah']
            );
            if (!$stmt_hasil->execute()) {
                throw new Exception("Gagal mengupdate hasil prediksi: " . $stmt_hasil->error);
            }
            $stmt_hasil->close();
        }
        // Jika nasabah belum memiliki prediksi, buat entri baru
        else {
            $id_prediksi = null;

            // Hanya simpan ke tabel prediksi_detail jika tabel ada
            if ($table_exists) {
                // 1. Simpan ke tabel laporan_prediksi
                $query_laporan = "INSERT INTO laporan_prediksi (tanggal_prediksi, parameter, akurasi)
                                 VALUES (NOW(), 'Backpropagation Neural Network', 87.5)";
                if (!$koneksi->query($query_laporan)) {
                    throw new Exception("Gagal menyimpan laporan prediksi: " . $koneksi->error);
                }
                $id_laporan = $koneksi->insert_id;

                // 2. Simpan ke tabel prediksi_detail
                $stmt_detail = $koneksi->prepare("INSERT INTO prediksi_detail
                                                (id_laporan, id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                                                VALUES (?, ?, ?, ?, ?, NOW())");
                $stmt_detail->bind_param("iisds",
                    $id_laporan,
                    $nasabah['id_nasabah'],
                    $hasil_prediksi['hasil_prediksi'],
                    $hasil_prediksi['probabilitas'],
                    $hasil_prediksi['keterangan']
                );
                if (!$stmt_detail->execute()) {
                    throw new Exception("Gagal menyimpan detail prediksi: " . $stmt_detail->error);
                }
                $id_prediksi = $koneksi->insert_id;
                $stmt_detail->close();
            }

            // 2.1 Simpan juga ke tabel hasil_prediksi untuk kompatibilitas
            // Tabel ini digunakan oleh hasil_prediksi.php
            $query_hasil = "INSERT INTO hasil_prediksi (id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                            VALUES (?, ?, ?, ?, NOW())";
            $stmt_hasil = $koneksi->prepare($query_hasil);
            $stmt_hasil->bind_param("isds",
                $nasabah['id_nasabah'],
                $hasil_prediksi['hasil_prediksi'],
                $hasil_prediksi['probabilitas'],
                $hasil_prediksi['keterangan']
            );
            if (!$stmt_hasil->execute()) {
                throw new Exception("Gagal menyimpan hasil prediksi: " . $stmt_hasil->error);
            }
            $stmt_hasil->close();
        }

        // 3. Update kolom kelayakan di tabel nasabah
        $stmt_update = $koneksi->prepare("UPDATE nasabah SET kelayakan = ? WHERE id_nasabah = ?");
        $stmt_update->bind_param("si",
            $hasil_prediksi['hasil_prediksi'],
            $nasabah['id_nasabah']
        );
        if (!$stmt_update->execute()) {
            throw new Exception("Gagal mengupdate kelayakan nasabah: " . $stmt_update->error);
        }
        $stmt_update->close();

        // 4. Simpan proses perhitungan
        // Untuk model backpropagation, kita tidak memiliki skor terperinci seperti rule-based
        // Jadi kita simpan informasi yang relevan untuk model
        $jumlah_tanggungan = max(1, intval($nasabah['jumlah_tanggungan'] ?? 1));

        // Pastikan penghasilan dan jumlah pinjaman adalah nilai numerik
        $penghasilan = is_numeric($nasabah['penghasilan']) ? floatval($nasabah['penghasilan']) : floatval(str_replace('.', '', $nasabah['penghasilan']));
        $jumlah_pinjaman = is_numeric($nasabah['jumlah_pinjaman']) ? floatval($nasabah['jumlah_pinjaman']) : floatval(str_replace('.', '', $nasabah['jumlah_pinjaman']));

        $penghasilan_per_kapita = $penghasilan / $jumlah_tanggungan;
        $rasio_pinjaman = $jumlah_pinjaman / ($penghasilan * 12);

        $proses_data = [
            'model' => 'Backpropagation Neural Network',
            'fitur_input' => [
                'umur' => intval($nasabah['umur']),
                'jenis_kelamin' => $nasabah['jenis_kelamin'],
                'status_perkawinan' => $nasabah['status_perkawinan'],
                'pekerjaan' => $nasabah['pekerjaan'],
                'penghasilan' => $penghasilan,
                'jumlah_pinjaman' => $jumlah_pinjaman,
                'jumlah_tanggungan' => $jumlah_tanggungan,
                'penghasilan_per_kapita' => $penghasilan_per_kapita,
                'rasio_pinjaman' => $rasio_pinjaman,
                'kepemilikan_rumah' => $nasabah['kepemilikan_rumah'],
                'jaminan' => $nasabah['jaminan'],
                'tahun_kendaraan' => intval($nasabah['tahun_kendaraan'] ?? 0),
                'status_pajak' => $nasabah['status_pajak'] ?? ''
            ],
            'hasil_prediksi' => $hasil_prediksi['hasil_prediksi'],
            'probabilitas' => $hasil_prediksi['probabilitas'],
            'threshold' => 0.6  // Threshold yang lebih rendah untuk meningkatkan peluang "Layak"
        ];

        // Hapus data proses perhitungan lama jika ada
        if (isset($id_prediksi)) {
            $delete_proses = "DELETE FROM proses_perhitungan WHERE id_nasabah = ?";
            $stmt_delete = $koneksi->prepare($delete_proses);
            $stmt_delete->bind_param("i", $nasabah['id_nasabah']);
            $stmt_delete->execute();
            $stmt_delete->close();
        }

        // Simpan proses perhitungan baru
        $query_proses = "INSERT INTO proses_perhitungan (id_nasabah, input_layer, hidden_layer_1, hidden_layer_2, output_layer, weights, bias, tanggal_proses)
                        VALUES (?, ?, '', '', '', '', '', NOW())";
        $stmt_proses = $koneksi->prepare($query_proses);
        $proses_json = json_encode($proses_data);
        $stmt_proses->bind_param("is", $nasabah['id_nasabah'], $proses_json);
        $stmt_proses->execute();
        $stmt_proses->close();

        // Commit transaksi
        $koneksi->commit();

        return $id_prediksi;

    } catch (Exception $e) {
        // Rollback transaksi jika ada error
        $koneksi->rollback();
        throw $e;
    }
}

/**
 * Fungsi untuk sinkronisasi data prediksi yang hilang
 * Memastikan semua nasabah yang sudah diprediksi memiliki data di tabel hasil_prediksi
 */
function sinkronisasi_data_prediksi() {
    global $koneksi;

    try {
        // Cari nasabah yang memiliki kelayakan tapi tidak ada di hasil_prediksi
        $query_missing = "SELECT n.*
                         FROM nasabah n
                         LEFT JOIN hasil_prediksi hp ON n.id_nasabah = hp.id_nasabah
                         WHERE n.kelayakan IS NOT NULL
                         AND hp.id_prediksi IS NULL";

        $result_missing = mysqli_query($koneksi, $query_missing);
        $count_synced = 0;

        if ($result_missing && mysqli_num_rows($result_missing) > 0) {
            while ($nasabah = mysqli_fetch_assoc($result_missing)) {
                // Buat data prediksi berdasarkan kelayakan yang ada
                $hasil_prediksi = [
                    'hasil_prediksi' => $nasabah['kelayakan'],
                    'probabilitas' => $nasabah['kelayakan'] == 'Layak' ? 0.75 : 0.25,
                    'keterangan' => 'Data disinkronisasi dari kolom kelayakan nasabah'
                ];

                // Simpan ke tabel hasil_prediksi
                $query_sync = "INSERT INTO hasil_prediksi (id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                              VALUES (?, ?, ?, ?, COALESCE((SELECT created_at FROM nasabah WHERE id_nasabah = ?), NOW()))";
                $stmt_sync = $koneksi->prepare($query_sync);
                $stmt_sync->bind_param("isdsi",
                    $nasabah['id_nasabah'],
                    $hasil_prediksi['hasil_prediksi'],
                    $hasil_prediksi['probabilitas'],
                    $hasil_prediksi['keterangan'],
                    $nasabah['id_nasabah']
                );

                if ($stmt_sync->execute()) {
                    $count_synced++;
                }
                $stmt_sync->close();
            }
        }

        return $count_synced;

    } catch (Exception $e) {
        error_log("Error dalam sinkronisasi data prediksi: " . $e->getMessage());
        return 0;
    }
}

/**
 * Fungsi untuk memastikan konsistensi data prediksi
 * Memastikan data di hasil_prediksi dan nasabah.kelayakan selalu sinkron
 */
function pastikan_konsistensi_prediksi($id_nasabah) {
    global $koneksi;

    try {
        // Ambil data terbaru dari hasil_prediksi
        $query_hasil = "SELECT * FROM hasil_prediksi WHERE id_nasabah = ? ORDER BY tanggal_prediksi DESC LIMIT 1";
        $stmt_hasil = $koneksi->prepare($query_hasil);
        $stmt_hasil->bind_param("i", $id_nasabah);
        $stmt_hasil->execute();
        $result_hasil = $stmt_hasil->get_result();
        $data_hasil = $result_hasil->fetch_assoc();
        $stmt_hasil->close();

        // Ambil data dari nasabah.kelayakan
        $query_nasabah = "SELECT kelayakan FROM nasabah WHERE id_nasabah = ?";
        $stmt_nasabah = $koneksi->prepare($query_nasabah);
        $stmt_nasabah->bind_param("i", $id_nasabah);
        $stmt_nasabah->execute();
        $result_nasabah = $stmt_nasabah->get_result();
        $data_nasabah = $result_nasabah->fetch_assoc();
        $stmt_nasabah->close();

        // Jika ada data di hasil_prediksi tapi tidak di nasabah.kelayakan, update nasabah
        if ($data_hasil && (!$data_nasabah['kelayakan'] || $data_nasabah['kelayakan'] != $data_hasil['hasil_prediksi'])) {
            $query_update_nasabah = "UPDATE nasabah SET kelayakan = ? WHERE id_nasabah = ?";
            $stmt_update = $koneksi->prepare($query_update_nasabah);
            $stmt_update->bind_param("si", $data_hasil['hasil_prediksi'], $id_nasabah);
            $stmt_update->execute();
            $stmt_update->close();

            error_log("Konsistensi diperbaiki: nasabah ID $id_nasabah kelayakan diupdate ke " . $data_hasil['hasil_prediksi']);
        }

        // Jika ada data di nasabah.kelayakan tapi tidak di hasil_prediksi, buat entry baru
        if ($data_nasabah['kelayakan'] && !$data_hasil) {
            $hasil_prediksi = [
                'hasil_prediksi' => $data_nasabah['kelayakan'],
                'probabilitas' => $data_nasabah['kelayakan'] == 'Layak' ? 0.75 : 0.25,
                'keterangan' => 'Data disinkronisasi dari kolom kelayakan nasabah'
            ];

            $query_insert = "INSERT INTO hasil_prediksi (id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                            VALUES (?, ?, ?, ?, NOW())";
            $stmt_insert = $koneksi->prepare($query_insert);
            $stmt_insert->bind_param("isds",
                $id_nasabah,
                $hasil_prediksi['hasil_prediksi'],
                $hasil_prediksi['probabilitas'],
                $hasil_prediksi['keterangan']
            );
            $stmt_insert->execute();
            $stmt_insert->close();

            error_log("Konsistensi diperbaiki: hasil_prediksi dibuat untuk nasabah ID $id_nasabah");
        }

        return true;

    } catch (Exception $e) {
        error_log("Error dalam pastikan konsistensi prediksi: " . $e->getMessage());
        return false;
    }
}

// Jalankan sinkronisasi otomatis saat file dimuat
if (php_sapi_name() !== 'cli') {
    $synced_count = sinkronisasi_data_prediksi();
    if ($synced_count > 0) {
        error_log("Sinkronisasi data prediksi: $synced_count data berhasil disinkronisasi");
    }
}