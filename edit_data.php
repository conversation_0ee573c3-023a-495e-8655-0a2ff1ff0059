<?php
include 'koneksi.php';
include 'cek_session.php';

// Cek akses berdasarkan tabel yang akan diedit
if (isset($_GET['table'])) {
    $table = $_GET['table'];
    if ($table == 'pengguna') {
        // <PERSON>ya admin yang boleh mengedit data pengguna
        cek_akses(['admin']);
    } elseif ($table == 'nasabah') {
        // Admin dan analis boleh mengedit data nasabah
        cek_akses(['admin', 'analis']);
    }
}

// Fungsi untuk sanitasi input
function sanitize_input($data) {
    global $koneksi;
    return mysqli_real_escape_string($koneksi, htmlspecialchars(trim($data)));
}

// Inisialisasi variabel
$table = '';
$id_field = '';
$id_value = '';
$redirect_page = '';
$success_message = '';
$error_message = '';
$data = [];
$title = '';

// Cek parameter tabel dan ID
if (isset($_GET['table']) && isset($_GET['id'])) {
    $table = sanitize_input($_GET['table']);
    $id_value = sanitize_input($_GET['id']);

    // Tentukan field ID, judul, dan halaman redirect berdasarkan tabel
    switch ($table) {
        case 'nasabah':
            $id_field = 'id_nasabah';
            $redirect_page = ($_SESSION['level'] == 'admin') ? 'data_nasabah.php' : 'data_nasabah_analis.php';
            $success_message = 'Data nasabah berhasil diperbarui';
            $title = 'Edit Data Nasabah';
            break;
        case 'pengguna':
            $id_field = 'id_pengguna';
            $redirect_page = 'data_user.php';
            $success_message = 'Data pengguna berhasil diperbarui';
            $title = 'Edit Data Pengguna';
            break;
        default:
            // Tabel tidak valid
            $error_message = 'Tabel tidak valid';
            break;
    }

    // Jika tabel valid, ambil data yang akan diedit
    if (!empty($id_field)) {
        $query = "SELECT * FROM $table WHERE $id_field = ?";
        $stmt = $koneksi->prepare($query);
        $stmt->bind_param("i", $id_value);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $data = $result->fetch_assoc();
        } else {
            $error_message = 'Data tidak ditemukan';
        }
    }
}

// Proses form submit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit'])) {
    $table = sanitize_input($_POST['table']);
    $id_field = sanitize_input($_POST['id_field']);
    $id_value = sanitize_input($_POST['id_value']);

    // Tentukan halaman redirect berdasarkan tabel
    switch ($table) {
        case 'nasabah':
            $redirect_page = ($_SESSION['level'] == 'admin') ? 'data_nasabah.php' : 'data_nasabah_analis.php';
            $success_message = 'Data nasabah berhasil diperbarui';
            break;
        case 'pengguna':
            $redirect_page = 'data_user.php';
            $success_message = 'Data pengguna berhasil diperbarui';
            break;
        default:
            // Tabel tidak valid
            $error_message = 'Tabel tidak valid';
            break;
    }

    // Proses update berdasarkan tabel
    if ($table == 'nasabah') {
        // Sanitasi input
        $nama_nasabah = sanitize_input($_POST['nama_nasabah']);
        $umur = sanitize_input($_POST['umur']);
        $jenis_kelamin = sanitize_input($_POST['jenis_kelamin']);
        $status_perkawinan = sanitize_input($_POST['status_perkawinan']);
        $pekerjaan = sanitize_input($_POST['pekerjaan']);

        // Konversi penghasilan dari format rupiah ke angka
        $penghasilan_input = sanitize_input($_POST['penghasilan']);
        $penghasilan = str_replace('.', '', $penghasilan_input); // Hapus titik sebagai pemisah ribuan

        // Konversi jumlah pinjaman dari format rupiah ke angka
        $jumlah_pinjaman_input = sanitize_input($_POST['jumlah_pinjaman']);
        $jumlah_pinjaman = str_replace('.', '', $jumlah_pinjaman_input); // Hapus titik sebagai pemisah ribuan

        $kepemilikan_rumah = sanitize_input($_POST['kepemilikan_rumah']);
        $jaminan = sanitize_input($_POST['jaminan']);
        $tujuan_pinjaman = sanitize_input($_POST['tujuan_pinjaman']);

        // Update data nasabah
        $update_sql = "UPDATE nasabah SET
                      nama_nasabah = ?,
                      umur = ?,
                      jenis_kelamin = ?,
                      status_perkawinan = ?,
                      pekerjaan = ?,
                      penghasilan = ?,
                      jumlah_pinjaman = ?,
                      kepemilikan_rumah = ?,
                      jaminan = ?,
                      tujuan_pinjaman = ?
                      WHERE id_nasabah = ?";

        $update_stmt = $koneksi->prepare($update_sql);
        $update_stmt->bind_param("sisssddsssi",
                               $nama_nasabah,
                               $umur,
                               $jenis_kelamin,
                               $status_perkawinan,
                               $pekerjaan,
                               $penghasilan,
                               $jumlah_pinjaman,
                               $kepemilikan_rumah,
                               $jaminan,
                               $tujuan_pinjaman,
                               $id_value);

        if ($update_stmt->execute()) {
            header("Location: $redirect_page?pesan=edit_sukses");
            exit;
        } else {
            $error_message = "Gagal memperbarui data: " . $koneksi->error;
        }
    } elseif ($table == 'pengguna') {
        // Sanitasi input
        $username = sanitize_input($_POST['username']);
        $level = sanitize_input($_POST['level']);

        // Cek apakah password diubah
        if (!empty($_POST['password'])) {
            $password = password_hash($_POST['password'], PASSWORD_DEFAULT);

            // Update data pengguna dengan password baru
            $update_sql = "UPDATE pengguna SET username = ?, password = ?, level = ? WHERE id_pengguna = ?";
            $update_stmt = $koneksi->prepare($update_sql);
            $update_stmt->bind_param("sssi", $username, $password, $level, $id_value);
        } else {
            // Update data pengguna tanpa mengubah password
            $update_sql = "UPDATE pengguna SET username = ?, level = ? WHERE id_pengguna = ?";
            $update_stmt = $koneksi->prepare($update_sql);
            $update_stmt->bind_param("ssi", $username, $level, $id_value);
        }

        if ($update_stmt->execute()) {
            header("Location: $redirect_page?pesan=edit_sukses");
            exit;
        } else {
            $error_message = "Gagal memperbarui data: " . $koneksi->error;
        }
    }
}

// Jika ada error, tampilkan pesan
if (!empty($error_message)) {
    echo "<script>alert('$error_message');</script>";
}

// Tentukan layout berdasarkan level pengguna
$nav_file = ($_SESSION['level'] == 'admin') ? 'nav.php' : 'nav_analis.php';
require_once $nav_file;
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header"><?php echo $title; ?></h1>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-edit"></i> Form Edit Data</h3>
                    </div>
                    <div class="panel-body">
                        <?php if ($table == 'nasabah' && !empty($data)): ?>
                        <form method="POST" action="edit_data.php" class="form-horizontal">
                            <input type="hidden" name="table" value="<?php echo $table; ?>">
                            <input type="hidden" name="id_field" value="<?php echo $id_field; ?>">
                            <input type="hidden" name="id_value" value="<?php echo $id_value; ?>">

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Nama Nasabah</label>
                                <div class="col-sm-9">
                                    <input type="text" name="nama_nasabah" class="form-control" value="<?php echo htmlspecialchars($data['nama_nasabah']); ?>" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Umur</label>
                                <div class="col-sm-9">
                                    <input type="number" name="umur" class="form-control" value="<?php echo $data['umur']; ?>" required min="17" max="80">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Jenis Kelamin</label>
                                <div class="col-sm-9">
                                    <select name="jenis_kelamin" class="form-control" required>
                                        <option value="Laki-laki" <?php echo ($data['jenis_kelamin'] == 'Laki-laki') ? 'selected' : ''; ?>>Laki-laki</option>
                                        <option value="Perempuan" <?php echo ($data['jenis_kelamin'] == 'Perempuan') ? 'selected' : ''; ?>>Perempuan</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Status Perkawinan</label>
                                <div class="col-sm-9">
                                    <select name="status_perkawinan" class="form-control" required>
                                        <option value="Menikah" <?php echo ($data['status_perkawinan'] == 'Menikah') ? 'selected' : ''; ?>>Menikah</option>
                                        <option value="Belum Menikah" <?php echo ($data['status_perkawinan'] == 'Belum Menikah') ? 'selected' : ''; ?>>Belum Menikah</option>
                                        <option value="Cerai" <?php echo ($data['status_perkawinan'] == 'Cerai') ? 'selected' : ''; ?>>Cerai</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Pekerjaan</label>
                                <div class="col-sm-9">
                                    <select name="pekerjaan" class="form-control" required>
                                        <option value="PNS" <?php echo ($data['pekerjaan'] == 'PNS') ? 'selected' : ''; ?>>PNS</option>
                                        <option value="Swasta" <?php echo ($data['pekerjaan'] == 'Swasta') ? 'selected' : ''; ?>>Swasta</option>
                                        <option value="Wiraswasta" <?php echo ($data['pekerjaan'] == 'Wiraswasta') ? 'selected' : ''; ?>>Wiraswasta</option>
                                        <option value="Profesional" <?php echo ($data['pekerjaan'] == 'Profesional') ? 'selected' : ''; ?>>Profesional</option>
                                        <option value="Lainnya" <?php echo ($data['pekerjaan'] == 'Lainnya') ? 'selected' : ''; ?>>Lainnya</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Penghasilan</label>
                                <div class="col-sm-9">
                                    <input type="text" name="penghasilan" class="form-control" value="<?php echo formatRupiah($data['penghasilan'], false); ?>" required min="0">
                                    <p class="help-block">Format: 1.000.000 (tanpa Rp)</p>
                                </div>
                            </div>

                             <div class="form-group">
                                <label class="col-sm-3 control-label">Jumlah Tanggungan</label>
                                <div class="col-sm-9">
                                    <input type="number" name="jumlah_tanggungan" class="form-control" value="<?php echo $data['jumlah_tanggungan']; ?>" required min="0">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Jumlah Pinjaman</label>
                                <div class="col-sm-9">
                                    <input type="text" name="jumlah_pinjaman" class="form-control" value="<?php echo formatRupiah($data['jumlah_pinjaman'], false); ?>" required min="0">
                                    <p class="help-block">Format: 1.000.000 (tanpa Rp)</p>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Kepemilikan Rumah</label>
                                <div class="col-sm-9">
                                    <select name="kepemilikan_rumah" class="form-control" required>
                                        <option value="Milik Sendiri" <?php echo ($data['kepemilikan_rumah'] == 'Milik Sendiri') ? 'selected' : ''; ?>>Milik Sendiri</option>
                                        <option value="Sewa" <?php echo ($data['kepemilikan_rumah'] == 'Sewa') ? 'selected' : ''; ?>>Sewa</option>
                                        <option value="Milik Keluarga" <?php echo ($data['kepemilikan_rumah'] == 'Milik Keluarga') ? 'selected' : ''; ?>>Milik Keluarga</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Jaminan</label>
                                <div class="col-sm-9">
                                    <select name="jaminan" class="form-control" required>
                                        <option value="Rumah" <?php echo ($data['jaminan'] == 'Rumah') ? 'selected' : ''; ?>>Rumah</option>
                                        <option value="Mobil" <?php echo ($data['jaminan'] == 'Mobil') ? 'selected' : ''; ?>>Mobil</option>
                                        <option value="Motor" <?php echo ($data['jaminan'] == 'Motor') ? 'selected' : ''; ?>>Motor</option>
                                        <option value="Sertifikat" <?php echo ($data['jaminan'] == 'Sertifikat') ? 'selected' : ''; ?>>Sertifikat</option>
                                        <option value="Tidak Ada" <?php echo ($data['jaminan'] == 'Tidak Ada') ? 'selected' : ''; ?>>Tidak Ada</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Tujuan Pinjaman</label>
                                <div class="col-sm-9">
                                    <textarea name="tujuan_pinjaman" class="form-control" rows="3"><?php echo htmlspecialchars($data['tujuan_pinjaman']); ?></textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-offset-3 col-sm-9">
                                    <button type="submit" name="submit" class="btn btn-primary"><i class="fa fa-save"></i> Simpan Perubahan</button>
                                    <a href="<?php echo $redirect_page; ?>" class="btn btn-default"><i class="fa fa-arrow-left"></i> Kembali</a>
                                </div>
                            </div>
                        </form>

                        <?php elseif ($table == 'pengguna' && !empty($data)): ?>
                        <form method="POST" action="edit_data.php" class="form-horizontal">
                            <input type="hidden" name="table" value="<?php echo $table; ?>">
                            <input type="hidden" name="id_field" value="<?php echo $id_field; ?>">
                            <input type="hidden" name="id_value" value="<?php echo $id_value; ?>">

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Username</label>
                                <div class="col-sm-9">
                                    <input type="text" name="username" class="form-control" value="<?php echo htmlspecialchars($data['username']); ?>" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Password</label>
                                <div class="col-sm-9">
                                    <input type="password" name="password" class="form-control" placeholder="Kosongkan jika tidak ingin mengubah password">
                                    <p class="help-block">Kosongkan jika tidak ingin mengubah password</p>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Level</label>
                                <div class="col-sm-9">
                                    <select name="level" class="form-control" required>
                                        <option value="admin" <?php echo ($data['level'] == 'admin') ? 'selected' : ''; ?>>Admin</option>
                                        <option value="analis" <?php echo ($data['level'] == 'analis') ? 'selected' : ''; ?>>Analis Kredit</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-offset-3 col-sm-9">
                                    <button type="submit" name="submit" class="btn btn-primary"><i class="fa fa-save"></i> Simpan Perubahan</button>
                                    <a href="<?php echo $redirect_page; ?>" class="btn btn-default"><i class="fa fa-arrow-left"></i> Kembali</a>
                                </div>
                            </div>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> Data tidak ditemukan atau tabel tidak valid.
                        </div>
                        <a href="<?php echo $redirect_page; ?>" class="btn btn-default"><i class="fa fa-arrow-left"></i> Kembali</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'foot.php'; ?>
