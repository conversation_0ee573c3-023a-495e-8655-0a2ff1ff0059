import numpy as np
import pandas as pd
import pickle
import os
import re
from datetime import datetime
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.neural_network import MLPClassifier

# Fungsi untuk memformat nilai Rupiah
def format_rupiah(value):
    """Mengubah string Rupiah menjadi nilai numerik"""
    if isinstance(value, str):
        # Hapus 'Rp', titik, dan spasi
        value = value.replace('Rp', '').replace('.', '').replace(' ', '')
        # Hapus karakter non-numerik
        value = re.sub(r'[^\d]', '', value)
        if value:
            return float(value)
    return float(value) if value else 0

# Fungsi untuk preprocessing dataset
def preprocess_dataset(df):
    """Preprocessing dataset"""
    # Buat salinan dataframe
    df_processed = df.copy()
    
    # Konversi nilai Rupiah ke numerik
    if 'penghasilan' in df_processed.columns:
        df_processed['penghasilan'] = df_processed['penghasilan'].apply(format_rupiah)
    
    if 'jumlah_pinjaman' in df_processed.columns:
        df_processed['jumlah_pinjaman'] = df_processed['jumlah_pinjaman'].apply(format_rupiah)
    
    # Konversi waktu pengembalian ke numerik
    if 'waktu_pengembalian' in df_processed.columns:
        df_processed['waktu_pengembalian'] = pd.to_numeric(df_processed['waktu_pengembalian'], errors='coerce')
    
    # Konversi tahun kendaraan ke numerik
    if 'tahun_kendaraan' in df_processed.columns:
        df_processed['tahun_kendaraan'] = pd.to_numeric(df_processed['tahun_kendaraan'], errors='coerce')
    
    # Tambahkan fitur turunan
    if 'penghasilan' in df_processed.columns and 'jumlah_tanggungan' in df_processed.columns:
        df_processed['jumlah_tanggungan'] = pd.to_numeric(df_processed['jumlah_tanggungan'], errors='coerce').fillna(1)
        df_processed['penghasilan_per_kapita'] = df_processed['penghasilan'] / df_processed['jumlah_tanggungan'].clip(lower=1)
    
    if 'penghasilan' in df_processed.columns and 'jumlah_pinjaman' in df_processed.columns:
        df_processed['rasio_pinjaman'] = df_processed['jumlah_pinjaman'] / (df_processed['penghasilan'] * 12)
    
    if 'penghasilan' in df_processed.columns and 'waktu_pengembalian' in df_processed.columns and 'jumlah_pinjaman' in df_processed.columns:
        # Estimasi angsuran bulanan (dengan bunga flat 1% per bulan)
        bunga_bulanan = 0.01  # 1% per bulan
        df_processed['waktu_pengembalian'] = df_processed['waktu_pengembalian'].fillna(12)
        df_processed['total_bunga'] = df_processed['jumlah_pinjaman'] * bunga_bulanan * df_processed['waktu_pengembalian']
        df_processed['total_pembayaran'] = df_processed['jumlah_pinjaman'] + df_processed['total_bunga']
        df_processed['angsuran_bulanan'] = df_processed['total_pembayaran'] / df_processed['waktu_pengembalian']
        df_processed['rasio_angsuran'] = df_processed['angsuran_bulanan'] / df_processed['penghasilan']
    
    # Tambahkan fitur umur kendaraan
    current_year = datetime.now().year
    if 'tahun_kendaraan' in df_processed.columns:
        df_processed['umur_kendaraan'] = current_year - df_processed['tahun_kendaraan']
    
    return df_processed

# Fungsi untuk membuat data sampel
def create_sample_data(num_samples=5):
    """
    Membuat data sampel untuk demonstrasi proses perhitungan prediksi
    """
    # Buat data sampel dengan variasi nilai
    np.random.seed(42)  # Untuk reproduksibilitas
    
    # Definisikan rentang nilai untuk setiap fitur
    umur_range = (25, 60)
    penghasilan_range = (3000000, 15000000)
    jumlah_pinjaman_range = (5000000, 50000000)
    waktu_pengembalian_range = (12, 60)
    jumlah_tanggungan_range = (0, 5)
    tahun_kendaraan_range = (2010, 2023)
    
    # Buat data sampel
    data = []
    for i in range(num_samples):
        # Variasi nilai untuk setiap sampel
        umur = np.random.randint(umur_range[0], umur_range[1])
        jenis_kelamin = np.random.choice(['Laki-laki', 'Perempuan'])
        status_pernikahan = np.random.choice(['Menikah', 'Belum Menikah', 'Cerai'])
        pekerjaan = np.random.choice(['PNS', 'Karyawan Swasta', 'Wiraswasta', 'Profesional', 'Lainnya'])
        penghasilan = np.random.randint(penghasilan_range[0], penghasilan_range[1])
        jumlah_pinjaman = np.random.randint(jumlah_pinjaman_range[0], jumlah_pinjaman_range[1])
        kepemilikan_rumah = np.random.choice(['Milik Sendiri', 'Sewa', 'Milik Keluarga'])
        jenis_agunan = np.random.choice(['BPKB Mobil', 'BPKB Motor'])
        tahun_kendaraan = np.random.randint(tahun_kendaraan_range[0], tahun_kendaraan_range[1])
        status_pajak = np.random.choice(['Aktif', 'Tidak Aktif'])
        jumlah_tanggungan = np.random.randint(jumlah_tanggungan_range[0], jumlah_tanggungan_range[1])
        waktu_pengembalian = np.random.randint(waktu_pengembalian_range[0], waktu_pengembalian_range[1])
        
        # Tambahkan ke data
        sample = {
            'umur': umur,
            'jenis_kelamin': jenis_kelamin,
            'status_pernikahan': status_pernikahan,
            'pekerjaan': pekerjaan,
            'penghasilan': penghasilan,
            'jumlah_pinjaman': jumlah_pinjaman,
            'kepemilikan_rumah': kepemilikan_rumah,
            'jenis_agunan': jenis_agunan,
            'tahun_kendaraan': tahun_kendaraan,
            'status_pajak': status_pajak,
            'jumlah_tanggungan': jumlah_tanggungan,
            'waktu_pengembalian': waktu_pengembalian
        }
        data.append(sample)
    
    # Buat DataFrame
    df_sample = pd.DataFrame(data)
    
    return df_sample

# Fungsi untuk menunjukkan proses perhitungan prediksi
def demonstrate_prediction_process(model, sample_data, processed_data):
    """
    Menunjukkan proses perhitungan prediksi secara detail
    """
    print("\n" + "="*50)
    print("PROSES PERHITUNGAN PREDIKSI")
    print("="*50)
    
    # Ambil preprocessor dan classifier dari pipeline
    preprocessor = model.named_steps['preprocessor']
    classifier = model.named_steps['classifier']
    
    # Preprocessing data
    print("\n1. Preprocessing Data:")
    print("-" * 30)
    
    # Tampilkan data asli
    print("\nData Asli:")
    print(sample_data)
    
    # Transformasi data menggunakan preprocessor
    X_transformed = preprocessor.transform(sample_data)
    
    # Tampilkan informasi tentang transformasi
    print("\nHasil Transformasi:")
    print(f"Dimensi data setelah transformasi: {X_transformed.shape}")
    
    # Prediksi menggunakan model
    y_pred = model.predict(sample_data)
    y_pred_proba = model.predict_proba(sample_data)
    
    # Tampilkan hasil untuk setiap sampel
    print("\n2. Hasil Probabilitas dan Prediksi:")
    print("-" * 30)
    
    # Dapatkan label kelas
    classes = classifier.classes_
    
    for i in range(len(sample_data)):
        print(f"\nSampel #{i+1}:")
        print(f"Probabilitas kelas {classes[0]}: {y_pred_proba[i][0]:.4f}")
        print(f"Probabilitas kelas {classes[1]}: {y_pred_proba[i][1]:.4f}")
        print(f"Prediksi: {y_pred[i]}")
        
        # Tampilkan detail sampel
        print("\nDetail Sampel:")
        for col in sample_data.columns:
            print(f"- {col}: {sample_data.iloc[i][col]}")
        
        # Tampilkan fitur turunan yang dihitung
        if 'rasio_pinjaman' in processed_data.columns:
            print(f"- rasio_pinjaman: {processed_data.iloc[i]['rasio_pinjaman']:.4f}")
        if 'penghasilan_per_kapita' in processed_data.columns:
            print(f"- penghasilan_per_kapita: {processed_data.iloc[i]['penghasilan_per_kapita']:.2f}")
        if 'rasio_angsuran' in processed_data.columns:
            print(f"- rasio_angsuran: {processed_data.iloc[i]['rasio_angsuran']:.4f}")
        if 'angsuran_bulanan' in processed_data.columns:
            print(f"- angsuran_bulanan: {processed_data.iloc[i]['angsuran_bulanan']:.2f}")
        
        # Berikan penjelasan tentang keputusan
        print("\nPenjelasan Keputusan:")
        if y_pred[i] == 'Layak':
            print("Nasabah dinyatakan LAYAK untuk kredit karena:")
            if 'rasio_angsuran' in processed_data.columns and processed_data.iloc[i]['rasio_angsuran'] < 0.3:
                print(f"- Rasio angsuran terhadap penghasilan ({processed_data.iloc[i]['rasio_angsuran']:.2f}) < 0.3 (baik)")
            if 'rasio_pinjaman' in processed_data.columns and processed_data.iloc[i]['rasio_pinjaman'] < 1.0:
                print(f"- Rasio pinjaman terhadap penghasilan tahunan ({processed_data.iloc[i]['rasio_pinjaman']:.2f}) < 1.0 (baik)")
            if 'penghasilan_per_kapita' in processed_data.columns and processed_data.iloc[i]['penghasilan_per_kapita'] > 3000000:
                print(f"- Penghasilan per kapita (Rp {processed_data.iloc[i]['penghasilan_per_kapita']:,.2f}) cukup tinggi")
        else:
            print("Nasabah dinyatakan TIDAK LAYAK untuk kredit karena:")
            if 'rasio_angsuran' in processed_data.columns and processed_data.iloc[i]['rasio_angsuran'] > 0.3:
                print(f"- Rasio angsuran terhadap penghasilan ({processed_data.iloc[i]['rasio_angsuran']:.2f}) > 0.3 (terlalu tinggi)")
            if 'rasio_pinjaman' in processed_data.columns and processed_data.iloc[i]['rasio_pinjaman'] > 1.0:
                print(f"- Rasio pinjaman terhadap penghasilan tahunan ({processed_data.iloc[i]['rasio_pinjaman']:.2f}) > 1.0 (terlalu tinggi)")
            if 'penghasilan_per_kapita' in processed_data.columns and processed_data.iloc[i]['penghasilan_per_kapita'] < 3000000:
                print(f"- Penghasilan per kapita (Rp {processed_data.iloc[i]['penghasilan_per_kapita']:,.2f}) terlalu rendah")
        
        print("-" * 50)

# Program utama
if __name__ == "__main__":
    print("="*50)
    print("DEMONSTRASI PROSES PERHITUNGAN PREDIKSI DENGAN DATA SAMPEL")
    print("="*50)
    
    # Buat data sampel
    num_samples = 5  # Jumlah sampel yang akan dibuat
    df_sample = create_sample_data(num_samples)
    
    # Tampilkan data sampel
    print("\nData Sampel yang Dibuat:")
    print(df_sample)
    
    # Preprocessing data sampel
    df_sample_processed = preprocess_dataset(df_sample)
    
    # Tampilkan data sampel setelah preprocessing
    print("\nData Sampel Setelah Preprocessing:")
    print(df_sample_processed)
    
    # Muat model yang telah disimpan
    try:
        with open('model/backpropagation_model.pkl', 'rb') as file:
            loaded_model = pickle.load(file)
        print("\nModel berhasil dimuat dari file model/backpropagation_model.pkl")
    except Exception as e:
        print(f"\nError saat memuat model: {e}")
        print("Model tidak ditemukan. Membuat model sederhana untuk demonstrasi...")
        
        # Buat model sederhana untuk demonstrasi
        numeric_features = df_sample.select_dtypes(include=['int64', 'float64']).columns.tolist()
        categorical_features = df_sample.select_dtypes(include=['object']).columns.tolist()
        
        # Buat preprocessor
        preprocessor = ColumnTransformer(
            transformers=[
                ('num', StandardScaler(), numeric_features),
                ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
            ]
        )
        
        # Buat pipeline dengan preprocessor dan model
        loaded_model = Pipeline([
            ('preprocessor', preprocessor),
            ('classifier', MLPClassifier(
                hidden_layer_sizes=(10, 5),
                activation='relu',
                solver='adam',
                random_state=42
            ))
        ])
        
        # Buat data target dummy untuk pelatihan
        y_dummy = np.random.choice(['Tidak Layak', 'Layak'], size=len(df_sample))
        
        # Latih model dengan data dummy
        loaded_model.fit(df_sample, y_dummy)
        print("Model sederhana telah dibuat untuk demonstrasi")
    
    # Demonstrasikan proses perhitungan prediksi
    demonstrate_prediction_process(loaded_model, df_sample, df_sample_processed)
    
    # Simpan data sampel ke file CSV
    df_sample.to_csv('model/data_sampel.csv', index=False)
    print("\nData sampel telah disimpan ke model/data_sampel.csv")
    
    # Simpan hasil prediksi data sampel ke file
    df_sample_results = df_sample.copy()
    df_sample_results['prediksi'] = loaded_model.predict(df_sample)
    df_sample_results['probabilitas_layak'] = loaded_model.predict_proba(df_sample)[:, 1]
    df_sample_results.to_csv('model/hasil_prediksi_sampel.csv', index=False)
    print("Hasil prediksi data sampel telah disimpan ke model/hasil_prediksi_sampel.csv")
    
    print("\n" + "="*50)
    print("SELESAI")
    print("="*50)
