<?php
// File untuk testing hasil prediksi
include 'koneksi.php';

echo "<h2>Testing Hasil Prediksi</h2>";

// Test 1: <PERSON>k tabel yang ada
echo "<h3>1. Cek Tabel Database</h3>";
$tables = ['nasabah', 'hasil_prediksi', 'prediksi_detail', 'laporan_prediksi'];
foreach ($tables as $table) {
    $check = mysqli_query($koneksi, "SHOW TABLES LIKE '$table'");
    $exists = mysqli_num_rows($check) > 0 ? "✓ Ada" : "✗ Tidak ada";
    echo "$table: $exists<br>";
}

// Test 2: Cek data nasabah
echo "<h3>2. Data Nasabah</h3>";
$query_nasabah = "SELECT COUNT(*) as total FROM nasabah";
$result = mysqli_query($koneksi, $query_nasabah);
$data = mysqli_fetch_assoc($result);
echo "Total nasabah: " . $data['total'] . "<br>";

// Test 3: Cek data hasil_prediksi
echo "<h3>3. Data Hasil Prediksi</h3>";
$query_hasil = "SELECT COUNT(*) as total FROM hasil_prediksi";
$result = mysqli_query($koneksi, $query_hasil);
if ($result) {
    $data = mysqli_fetch_assoc($result);
    echo "Total hasil prediksi: " . $data['total'] . "<br>";
} else {
    echo "Error: " . mysqli_error($koneksi) . "<br>";
}

// Test 4: Cek data prediksi_detail
echo "<h3>4. Data Prediksi Detail</h3>";
$query_detail = "SELECT COUNT(*) as total FROM prediksi_detail";
$result = mysqli_query($koneksi, $query_detail);
if ($result) {
    $data = mysqli_fetch_assoc($result);
    echo "Total prediksi detail: " . $data['total'] . "<br>";
} else {
    echo "Error: " . mysqli_error($koneksi) . "<br>";
}

// Test 5: Cek nasabah terbaru
echo "<h3>5. Nasabah Terbaru (5 terakhir)</h3>";
$query_latest = "SELECT id_nasabah, nama_nasabah, created_at FROM nasabah ORDER BY created_at DESC LIMIT 5";
$result = mysqli_query($koneksi, $query_latest);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Nama</th><th>Tanggal Dibuat</th><th>Test Link</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id_nasabah'] . "</td>";
        echo "<td>" . $row['nama_nasabah'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "<td><a href='hasil_prediksi.php?id_nasabah=" . $row['id_nasabah'] . "' target='_blank'>Test</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Tidak ada data nasabah.<br>";
}

// Test 6: Cek prediksi terbaru
echo "<h3>6. Prediksi Terbaru (5 terakhir)</h3>";
$query_prediksi = "SELECT hp.id_prediksi, hp.id_nasabah, n.nama_nasabah, hp.hasil_prediksi, hp.tanggal_prediksi 
                   FROM hasil_prediksi hp 
                   JOIN nasabah n ON hp.id_nasabah = n.id_nasabah 
                   ORDER BY hp.tanggal_prediksi DESC LIMIT 5";
$result = mysqli_query($koneksi, $query_prediksi);
if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID Prediksi</th><th>Nama Nasabah</th><th>Hasil</th><th>Tanggal</th><th>Test Link</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id_prediksi'] . "</td>";
        echo "<td>" . $row['nama_nasabah'] . "</td>";
        echo "<td>" . $row['hasil_prediksi'] . "</td>";
        echo "<td>" . $row['tanggal_prediksi'] . "</td>";
        echo "<td><a href='hasil_prediksi.php?id_nasabah=" . $row['id_nasabah'] . "&id_prediksi=" . $row['id_prediksi'] . "' target='_blank'>Test</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Tidak ada data prediksi.<br>";
}

echo "<br><a href='laporan.php'>Lihat Laporan</a> | <a href='prediksi_baru.php'>Prediksi Baru</a>";
?>
