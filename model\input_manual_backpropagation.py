import numpy as np
import pandas as pd
import pickle
import os
import re
from datetime import datetime
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.neural_network import MLPClassifier

# Fungsi untuk memformat nilai Rupiah
def format_rupiah(value):
    """Mengubah string Rupiah menjadi nilai numerik"""
    if isinstance(value, str):
        # Hapus 'Rp', titik, dan spasi
        value = value.replace('Rp', '').replace('.', '').replace(' ', '')
        # Hapus karakter non-numerik
        value = re.sub(r'[^\d]', '', value)
        if value:
            return float(value)
    return float(value) if value else 0

# Fungsi untuk preprocessing data input
def preprocess_input(data):
    """Preprocessing data input"""
    # Buat salinan data
    data_processed = data.copy()

    # Konversi nilai Rupiah ke numerik
    if 'penghasilan' in data_processed:
        data_processed['penghasilan'] = format_rupiah(data_processed['penghasilan'])

    if 'jumlah_pinjaman' in data_processed:
        data_processed['jumlah_pinjaman'] = format_rupiah(data_processed['jumlah_pinjaman'])

    # Tambahkan fitur turunan
    if 'penghasilan' in data_processed and 'jumlah_tanggungan' in data_processed:
        data_processed['jumlah_tanggungan'] = int(data_processed['jumlah_tanggungan'])
        data_processed['penghasilan_per_kapita'] = data_processed['penghasilan'] / max(1, data_processed['jumlah_tanggungan'])

    if 'penghasilan' in data_processed and 'jumlah_pinjaman' in data_processed:
        data_processed['rasio_pinjaman'] = data_processed['jumlah_pinjaman'] / (data_processed['penghasilan'] * 12)

    if 'penghasilan' in data_processed and 'waktu_pengembalian' in data_processed and 'jumlah_pinjaman' in data_processed:
        # Estimasi angsuran bulanan (dengan bunga flat 1% per bulan)
        bunga_bulanan = 0.01  # 1% per bulan
        data_processed['waktu_pengembalian'] = int(data_processed['waktu_pengembalian'])
        data_processed['total_bunga'] = data_processed['jumlah_pinjaman'] * bunga_bulanan * data_processed['waktu_pengembalian']
        data_processed['total_pembayaran'] = data_processed['jumlah_pinjaman'] + data_processed['total_bunga']
        data_processed['angsuran_bulanan'] = data_processed['total_pembayaran'] / data_processed['waktu_pengembalian']
        data_processed['rasio_angsuran'] = data_processed['angsuran_bulanan'] / data_processed['penghasilan']

    # Tambahkan fitur umur kendaraan
    current_year = datetime.now().year
    if 'tahun_kendaraan' in data_processed:
        data_processed['tahun_kendaraan'] = int(data_processed['tahun_kendaraan'])
        data_processed['umur_kendaraan'] = current_year - data_processed['tahun_kendaraan']

    return data_processed

# Fungsi untuk menampilkan proses perhitungan backpropagation
def tampilkan_perhitungan_backpropagation(model, data_input, data_processed):
    """
    Menampilkan proses perhitungan backpropagation

    Parameters:
    -----------
    model : Pipeline
        Model backpropagation yang akan digunakan
    data_input : dict
        Data input asli
    data_processed : dict
        Data yang telah diproses
    """
    print("\n" + "="*50)
    print("PROSES PERHITUNGAN BACKPROPAGATION")
    print("="*50)

    # Ambil preprocessor dan classifier dari pipeline
    preprocessor = model.named_steps['preprocessor']
    classifier = model.named_steps['classifier']

    # Buat DataFrame dari data input
    df_input = pd.DataFrame([data_input])

    # Preprocessing data
    print("\n1. Preprocessing Data:")
    print("-" * 30)

    # Tampilkan data asli
    print("\nData Asli:")
    for key, value in data_input.items():
        print(f"- {key}: {value}")

    # Tampilkan data yang telah diproses
    print("\nData Setelah Preprocessing:")
    for key, value in data_processed.items():
        if key in ['penghasilan', 'jumlah_pinjaman', 'penghasilan_per_kapita', 'total_bunga', 'total_pembayaran', 'angsuran_bulanan']:
            print(f"- {key}: Rp {value:,.2f}")
        elif key in ['rasio_pinjaman', 'rasio_angsuran']:
            print(f"- {key}: {value:.4f}")
        else:
            print(f"- {key}: {value}")

    # Transformasi data menggunakan preprocessor
    X_transformed = preprocessor.transform(df_input)

    # Tampilkan informasi tentang transformasi
    print("\n2. Encoding & Normalisasi Data:")
    print("-" * 30)
    print(f"Dimensi data setelah transformasi: {X_transformed.shape}")

    # Tampilkan contoh nilai setelah transformasi
    print("\nNilai setelah transformasi (5 pertama):")
    for i in range(min(5, X_transformed.shape[1])):
        print(f"Feature {i+1}: {X_transformed[0, i]:.4f}")

    # Jika model menggunakan MLP Classifier, tampilkan detail perhitungan
    if isinstance(classifier, MLPClassifier):
        print("\n3. Forward Propagation:")
        print("-" * 30)

        # Dapatkan parameter model
        coefs = classifier.coefs_
        intercepts = classifier.intercepts_

        print(f"\nArsitektur Jaringan: Input({X_transformed.shape[1]}) -> ", end="")
        for i, layer_size in enumerate(classifier.hidden_layer_sizes):
            print(f"Hidden{i+1}({layer_size}) -> ", end="")
        print(f"Output({coefs[-1].shape[1]})")

        # Tampilkan beberapa bobot untuk ilustrasi
        print("\nContoh Bobot (Weight) dari Input ke Hidden Layer 1:")
        for i in range(min(3, X_transformed.shape[1])):
            for j in range(min(3, coefs[0].shape[1])):
                print(f"W1_{i+1}{j+1} = {coefs[0][i, j]:.4f}")

        print("\nContoh Bias Hidden Layer 1:")
        for i in range(min(3, intercepts[0].shape[0])):
            print(f"b1_{i+1} = {intercepts[0][i]:.4f}")

        # Hitung aktivasi untuk setiap layer
        print("\nPerhitungan Aktivasi Layer:")

        # Input layer ke hidden layer pertama
        print("\nInput -> Hidden Layer 1:")
        hidden1_input = np.dot(X_transformed, coefs[0]) + intercepts[0]
        hidden1_output = np.maximum(0, hidden1_input)  # ReLU activation

        # Tampilkan perhitungan detail untuk beberapa neuron
        print("\nPerhitungan detail untuk 3 neuron pertama di Hidden Layer 1:")
        for j in range(min(3, hidden1_input.shape[1])):
            print(f"z1_{j+1} = ", end="")
            for i in range(min(5, X_transformed.shape[1])):
                if i > 0:
                    print(" + ", end="")
                print(f"({X_transformed[0, i]:.4f} × {coefs[0][i, j]:.4f})", end="")
            print(f" + {intercepts[0][j]:.4f} = {hidden1_input[0, j]:.4f}")
            print(f"h1_{j+1} = ReLU({hidden1_input[0, j]:.4f}) = max(0, {hidden1_input[0, j]:.4f}) = {hidden1_output[0, j]:.4f}")

        # Jika ada lebih dari satu hidden layer
        current_input = hidden1_output
        for i in range(1, len(coefs) - 1):
            print(f"\nHidden Layer {i} -> Hidden Layer {i+1}:")
            hidden_input = np.dot(current_input, coefs[i]) + intercepts[i]
            hidden_output = np.maximum(0, hidden_input)  # ReLU activation

            # Tampilkan perhitungan detail untuk beberapa neuron
            print(f"\nPerhitungan detail untuk 2 neuron pertama di Hidden Layer {i+1}:")
            for j in range(min(2, hidden_input.shape[1])):
                print(f"z{i+1}_{j+1} = ", end="")
                for k in range(min(3, current_input.shape[1])):
                    if k > 0:
                        print(" + ", end="")
                    print(f"({current_input[0, k]:.4f} × {coefs[i][k, j]:.4f})", end="")
                print(f" + {intercepts[i][j]:.4f} = {hidden_input[0, j]:.4f}")
                print(f"h{i+1}_{j+1} = ReLU({hidden_input[0, j]:.4f}) = max(0, {hidden_input[0, j]:.4f}) = {hidden_output[0, j]:.4f}")

            current_input = hidden_output

        # Hidden layer terakhir ke output layer
        print(f"\nHidden Layer {len(coefs) - 1} -> Output Layer:")
        output_input = np.dot(current_input, coefs[-1]) + intercepts[-1]

        # Fungsi softmax untuk output layer
        def softmax(x):
            exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
            return exp_x / np.sum(exp_x, axis=1, keepdims=True)

        output_probs = softmax(output_input)

        # Tampilkan perhitungan detail untuk output layer
        print("\nPerhitungan detail untuk Output Layer:")
        for j in range(min(2, output_input.shape[1])):
            print(f"z{len(coefs)}_{j+1} = ", end="")
            for k in range(min(3, current_input.shape[1])):
                if k > 0:
                    print(" + ", end="")
                print(f"({current_input[0, k]:.4f} × {coefs[-1][k, j]:.4f})", end="")
            print(f" + {intercepts[-1][j]:.4f} = {output_input[0, j]:.4f}")

        # Tampilkan perhitungan softmax
        print("\nPerhitungan Softmax untuk Output Layer:")
        sum_exp = np.sum(np.exp(output_input), axis=1)[0]
        for j in range(min(2, output_input.shape[1])):
            print(f"exp(z{len(coefs)}_{j+1}) = exp({output_input[0, j]:.4f}) = {np.exp(output_input[0, j]):.4f}")
        print(f"sum_exp = {sum_exp:.4f}")

        for j in range(min(2, output_input.shape[1])):
            print(f"softmax(z{len(coefs)}_{j+1}) = exp({output_input[0, j]:.4f}) / {sum_exp:.4f} = {output_probs[0, j]:.4f}")

        # Tampilkan probabilitas
        print("\n4. Hasil Probabilitas dan Prediksi:")
        print("-" * 30)

        # Dapatkan label kelas
        classes = classifier.classes_

        # Prediksi menggunakan model
        y_pred = model.predict(df_input)
        y_pred_proba = model.predict_proba(df_input)

        print(f"Probabilitas kelas '{classes[0]}': {y_pred_proba[0][0]:.4f}")
        print(f"Probabilitas kelas '{classes[1]}': {y_pred_proba[0][1]:.4f}")
        print(f"Prediksi: {y_pred[0]}")

        # Simulasi backward propagation
        print("\n5. Backward Propagation (Simulasi):")
        print("-" * 30)

        # Asumsikan target adalah kelas positif (1)
        target = 1 if y_pred[0] == classes[1] else 0

        # Hitung error
        error = 0.5 * (target - y_pred_proba[0][1])**2
        print(f"Target: {target} (kelas '{classes[1]}')")
        print(f"Error (MSE): 0.5 × ({target} - {y_pred_proba[0][1]:.4f})² = {error:.4f}")

        # Hitung gradien output
        delta_output = y_pred_proba[0][1] - target
        print(f"Gradien Output (δ_output): y_pred - target = {y_pred_proba[0][1]:.4f} - {target} = {delta_output:.4f}")

        # Simulasi update bobot
        print("\nSimulasi Update Bobot (dengan learning rate η = 0.1):")

        # Update untuk beberapa bobot output layer
        print("\nUpdate bobot Output Layer:")
        for i in range(min(2, coefs[-1].shape[0])):
            for j in range(min(1, coefs[-1].shape[1])):
                delta_w = 0.1 * delta_output * current_input[0, i]
                print(f"ΔW{len(coefs)}_{i+1}{j+1} = η × δ_output × h{len(coefs)-1}_{i+1} = 0.1 × {delta_output:.4f} × {current_input[0, i]:.4f} = {delta_w:.6f}")
                print(f"W{len(coefs)}_{i+1}{j+1}_new = W{len(coefs)}_{i+1}{j+1} - ΔW{len(coefs)}_{i+1}{j+1} = {coefs[-1][i, j]:.4f} - {delta_w:.6f} = {coefs[-1][i, j] - delta_w:.4f}")

        # Berikan penjelasan tentang keputusan
        print("\n6. Penjelasan Keputusan:")
        print("-" * 30)
        if y_pred[0] == 'Layak':
            print("Nasabah dinyatakan LAYAK untuk kredit karena:")
            if 'rasio_angsuran' in data_processed and data_processed['rasio_angsuran'] < 0.3:
                print(f"- Rasio angsuran terhadap penghasilan ({data_processed['rasio_angsuran']:.2f}) < 0.3 (baik)")
            if 'rasio_pinjaman' in data_processed and data_processed['rasio_pinjaman'] < 1.0:
                print(f"- Rasio pinjaman terhadap penghasilan tahunan ({data_processed['rasio_pinjaman']:.2f}) < 1.0 (baik)")
            if 'penghasilan_per_kapita' in data_processed and data_processed['penghasilan_per_kapita'] > 3000000:
                print(f"- Penghasilan per kapita (Rp {data_processed['penghasilan_per_kapita']:,.2f}) cukup tinggi")
        else:
            print("Nasabah dinyatakan TIDAK LAYAK untuk kredit karena:")
            if 'rasio_angsuran' in data_processed and data_processed['rasio_angsuran'] > 0.3:
                print(f"- Rasio angsuran terhadap penghasilan ({data_processed['rasio_angsuran']:.2f}) > 0.3 (terlalu tinggi)")
            if 'rasio_pinjaman' in data_processed and data_processed['rasio_pinjaman'] > 1.0:
                print(f"- Rasio pinjaman terhadap penghasilan tahunan ({data_processed['rasio_pinjaman']:.2f}) > 1.0 (terlalu tinggi)")
            if 'penghasilan_per_kapita' in data_processed and data_processed['penghasilan_per_kapita'] < 3000000:
                print(f"- Penghasilan per kapita (Rp {data_processed['penghasilan_per_kapita']:,.2f}) terlalu rendah")

# Fungsi untuk input data manual
def input_data_manual():
    """
    Meminta input data nasabah secara manual

    Returns:
    --------
    dict
        Dictionary berisi data nasabah
    """
    print("\n" + "="*50)
    print("INPUT DATA NASABAH")
    print("="*50)

    data = {}

    # Input data nasabah
    data['umur'] = input("Umur: ")
    data['jenis_kelamin'] = input("Jenis Kelamin (Laki-laki/Perempuan): ")
    data['status_pernikahan'] = input("Status Pernikahan (Menikah/Belum Menikah/Cerai): ")
    data['pekerjaan'] = input("Pekerjaan (PNS/Karyawan Swasta/Wiraswasta/Profesional/Lainnya): ")
    data['penghasilan'] = input("Penghasilan (Rp): ")
    data['jumlah_pinjaman'] = input("Jumlah Pinjaman (Rp): ")
    data['kepemilikan_rumah'] = input("Kepemilikan Rumah (Milik Sendiri/Sewa/Milik Keluarga): ")
    data['jenis_agunan'] = input("Jenis Agunan (BPKB Mobil/BPKB Motor): ")
    data['tahun_kendaraan'] = input("Tahun Kendaraan: ")
    data['status_pajak'] = input("Status Pajak (Aktif/Tidak Aktif): ")
    data['jumlah_tanggungan'] = input("Jumlah Tanggungan: ")
    data['waktu_pengembalian'] = input("Waktu Pengembalian (bulan): ")

    return data

# Program utama
if __name__ == "__main__":
    print("="*50)
    print("SISTEM PREDIKSI KREDIT DENGAN BACKPROPAGATION")
    print("="*50)

    # Muat model yang telah disimpan
    try:
        with open('model/backpropagation_model.pkl', 'rb') as file:
            model = pickle.load(file)
        print("\nModel berhasil dimuat dari file model/backpropagation_model.pkl")
    except Exception as e:
        print(f"\nError saat memuat model: {e}")
        print("Model tidak ditemukan. Membuat model sederhana untuk demonstrasi...")

        # Buat model sederhana untuk demonstrasi
        # Definisikan fitur numerik dan kategorikal
        numeric_features = ['umur', 'penghasilan', 'jumlah_pinjaman', 'tahun_kendaraan', 'jumlah_tanggungan', 'waktu_pengembalian']
        categorical_features = ['jenis_kelamin', 'status_pernikahan', 'pekerjaan', 'kepemilikan_rumah', 'jenis_agunan', 'status_pajak']

        # Buat preprocessor
        preprocessor = ColumnTransformer(
            transformers=[
                ('num', StandardScaler(), numeric_features),
                ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
            ]
        )

        # Buat pipeline dengan preprocessor dan model
        model = Pipeline([
            ('preprocessor', preprocessor),
            ('classifier', MLPClassifier(
                hidden_layer_sizes=(10, 5),
                activation='relu',
                solver='adam',
                random_state=42
            ))
        ])

        # Buat data dummy untuk pelatihan
        dummy_data = {
            'umur': [30, 40, 50, 35, 45],
            'jenis_kelamin': ['Laki-laki', 'Perempuan', 'Laki-laki', 'Perempuan', 'Laki-laki'],
            'status_pernikahan': ['Menikah', 'Menikah', 'Cerai', 'Belum Menikah', 'Menikah'],
            'pekerjaan': ['PNS', 'Karyawan Swasta', 'Wiraswasta', 'Profesional', 'PNS'],
            'penghasilan': [5000000, 7000000, 10000000, 8000000, 6000000],
            'jumlah_pinjaman': [20000000, 30000000, 50000000, 40000000, 25000000],
            'kepemilikan_rumah': ['Milik Sendiri', 'Sewa', 'Milik Sendiri', 'Milik Keluarga', 'Sewa'],
            'jenis_agunan': ['BPKB Mobil', 'BPKB Motor', 'BPKB Mobil', 'BPKB Motor', 'BPKB Mobil'],
            'tahun_kendaraan': [2015, 2018, 2010, 2020, 2017],
            'status_pajak': ['Aktif', 'Aktif', 'Tidak Aktif', 'Aktif', 'Tidak Aktif'],
            'jumlah_tanggungan': [2, 1, 3, 0, 2],
            'waktu_pengembalian': [24, 36, 48, 12, 24]
        }
        df_dummy = pd.DataFrame(dummy_data)
        y_dummy = ['Layak', 'Layak', 'Tidak Layak', 'Layak', 'Tidak Layak']

        # Latih model dengan data dummy
        model.fit(df_dummy, y_dummy)
        print("Model sederhana telah dibuat untuk demonstrasi")

    while True:
        # Input data manual
        data_input = input_data_manual()

        # Preprocessing data input
        data_processed = preprocess_input(data_input)

        # Buat DataFrame dari data input
        df_input = pd.DataFrame([data_input])

        # Tampilkan proses perhitungan backpropagation
        tampilkan_perhitungan_backpropagation(model, data_input, data_processed)

        # Tanya apakah ingin melanjutkan
        lanjut = input("\nApakah ingin memasukkan data lagi? (y/n): ")
        if lanjut.lower() != 'y':
            break

    print("\n" + "="*50)
    print("TERIMA KASIH TELAH MENGGUNAKAN SISTEM PREDIKSI KREDIT")
    print("="*50)
