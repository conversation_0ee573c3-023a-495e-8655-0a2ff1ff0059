<?php
// Test file untuk memastikan sistem prediksi bekerja
require_once 'config.php';

// Test data nasabah
$test_nasabah = [
    'id_nasabah' => 60,
    'nama_nasabah' => 'Test Nasabah',
    'umur' => 35,
    'jenis_kelamin' => 'Laki-laki',
    'status_perkawinan' => 'Menikah',
    'pekerjaan' => 'Swasta',
    'penghasilan' => 5000000,
    'jumlah_tanggungan' => 2,
    'jumlah_pinjaman' => 25000000,
    'jangka_waktu' => 12,
    'kepemilikan_rumah' => 'Milik Sendiri',
    'jaminan' => 'BPKB Motor',
    'tahun_kendaraan' => 2020,
    'status_pajak' => 'aktif',
    'tujuan_pinjaman' => 'Kebutuhan Pribadi'
];

echo "<h2>Test Sistem Prediksi Kredit</h2>";

// Test 1: Cek koneksi database
echo "<h3>1. Test Koneksi Database</h3>";
try {
    $query = "SELECT COUNT(*) as total FROM nasabah";
    $result = $koneksi->query($query);
    $row = $result->fetch_assoc();
    echo "✅ Database terhubung. Total nasabah: " . $row['total'] . "<br>";
} catch (Exception $e) {
    echo "❌ Error database: " . $e->getMessage() . "<br>";
}

// Test 2: Cek apakah nasabah test ada
echo "<h3>2. Test Data Nasabah</h3>";
try {
    $query = "SELECT * FROM nasabah WHERE id_nasabah = ?";
    $stmt = $koneksi->prepare($query);
    $stmt->bind_param("i", $test_nasabah['id_nasabah']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $nasabah = $result->fetch_assoc();
        echo "✅ Nasabah ditemukan: " . $nasabah['nama_nasabah'] . "<br>";
        echo "   Penghasilan: Rp " . number_format($nasabah['penghasilan'], 0, ',', '.') . "<br>";
        echo "   Jumlah Pinjaman: Rp " . number_format($nasabah['jumlah_pinjaman'], 0, ',', '.') . "<br>";
    } else {
        echo "❌ Nasabah dengan ID " . $test_nasabah['id_nasabah'] . " tidak ditemukan<br>";
    }
    $stmt->close();
} catch (Exception $e) {
    echo "❌ Error mengambil data nasabah: " . $e->getMessage() . "<br>";
}

// Test 3: Test API Python
echo "<h3>3. Test API Python</h3>";
$api_url = 'http://localhost:5000/api/predict';
$data_untuk_api = ['id_nasabah' => $test_nasabah['id_nasabah']];
$json_data = json_encode($data_untuk_api);

$ch = curl_init($api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($json_data)
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "❌ Server Python tidak tersedia: " . $curl_error . "<br>";
    echo "   Pastikan server Python berjalan dengan menjalankan: start_model_server.bat<br>";
} elseif ($http_code !== 200) {
    echo "❌ API Python error (HTTP $http_code): " . $response . "<br>";
} else {
    $hasil_api = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "✅ API Python berjalan dengan baik<br>";
        echo "   Hasil: " . $hasil_api['hasil_prediksi'] . "<br>";
        echo "   Probabilitas: " . number_format($hasil_api['probabilitas'] * 100, 2) . "%<br>";
        echo "   ID Prediksi: " . ($hasil_api['id_prediksi'] ?? 'Tidak ada') . "<br>";
    } else {
        echo "❌ Response API tidak valid: " . $response . "<br>";
    }
}

// Test 4: Cek tabel hasil_prediksi
echo "<h3>4. Test Tabel Hasil Prediksi</h3>";
try {
    $query = "SELECT * FROM hasil_prediksi WHERE id_nasabah = ? ORDER BY tanggal_prediksi DESC LIMIT 1";
    $stmt = $koneksi->prepare($query);
    $stmt->bind_param("i", $test_nasabah['id_nasabah']);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $prediksi = $result->fetch_assoc();
        echo "✅ Hasil prediksi ditemukan di database<br>";
        echo "   Hasil: " . $prediksi['hasil_prediksi'] . "<br>";
        echo "   Probabilitas: " . number_format($prediksi['probabilitas'] * 100, 2) . "%<br>";
        echo "   Tanggal: " . $prediksi['tanggal_prediksi'] . "<br>";
        echo "   Keterangan: " . substr($prediksi['keterangan'], 0, 100) . "...<br>";
    } else {
        echo "❌ Tidak ada hasil prediksi untuk nasabah ini<br>";
    }
    $stmt->close();
} catch (Exception $e) {
    echo "❌ Error mengambil hasil prediksi: " . $e->getMessage() . "<br>";
}

// Test 5: Test manual prediksi
echo "<h3>5. Test Manual Prediksi</h3>";
echo "<form method='POST'>";
echo "<button type='submit' name='test_predict' value='1'>Jalankan Prediksi Test</button>";
echo "</form>";

if (isset($_POST['test_predict'])) {
    echo "<h4>Hasil Test Prediksi:</h4>";
    
    // Panggil API predict
    $api_url = 'http://localhost/ngasal/api_predict.php';
    $data_untuk_api = ['id_nasabah' => $test_nasabah['id_nasabah']];
    $json_data = json_encode($data_untuk_api);

    $ch = curl_init($api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($json_data)
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($response === false) {
        echo "❌ Error memanggil API predict<br>";
    } else {
        $hasil = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE && !isset($hasil['error'])) {
            echo "✅ Prediksi berhasil!<br>";
            echo "   Hasil: " . $hasil['hasil_prediksi'] . "<br>";
            echo "   Probabilitas: " . number_format($hasil['probabilitas'] * 100, 2) . "%<br>";
            echo "   ID Prediksi: " . $hasil['id_prediksi'] . "<br>";
            echo "<br><strong>Sekarang cek halaman:</strong><br>";
            echo "- <a href='hasil_prediksi.php?id_nasabah=" . $test_nasabah['id_nasabah'] . "' target='_blank'>Hasil Prediksi</a><br>";
            echo "- <a href='daftar_kelayakan.php' target='_blank'>Daftar Kelayakan</a><br>";
            echo "- <a href='laporan.php' target='_blank'>Laporan</a><br>";
        } else {
            echo "❌ Error dalam prediksi: " . ($hasil['error'] ?? 'Unknown error') . "<br>";
            echo "Response: " . $response . "<br>";
        }
    }
}

echo "<br><hr>";
echo "<p><strong>Instruksi:</strong></p>";
echo "<ol>";
echo "<li>Pastikan server Python berjalan dengan menjalankan <code>start_model_server.bat</code></li>";
echo "<li>Jika semua test ✅, sistem siap digunakan</li>";
echo "<li>Jika ada ❌, perbaiki masalah tersebut terlebih dahulu</li>";
echo "</ol>";
?>
