<?php
include 'koneksi.php';
include 'cek_session.php';

// Cek akses berdasarkan tabel yang akan dihapus
if (isset($_GET['table'])) {
    $table = $_GET['table'];
    if ($table == 'pengguna' || $table == 'users' || $table == 'user' || $table == 'model_ml' || $table == 'dataset') {
        // Hanya admin yang boleh menghapus data pengguna, model, dan dataset
        cek_akses(['admin']);
    } elseif ($table == 'nasabah') {
        // Admin dan analis boleh menghapus data nasabah
        cek_akses(['admin', 'analis']);
    } elseif ($table == 'laporan_prediksi') {
        // Hanya analis yang boleh menghapus laporan prediksi
        cek_akses(['analis']);
    }
}

// Fungsi untuk sanitasi input
function sanitize_input($data) {
    global $koneksi;
    return mysqli_real_escape_string($koneksi, htmlspecialchars(trim($data)));
}

// Inisialisasi variabel
$table = '';
$id_field = '';
$id_value = '';
$redirect_page = '';
$success_message = '';
$error_message = '';

// Cek parameter tabel dan ID
if (isset($_GET['table']) && isset($_GET['id'])) {
    $table = sanitize_input($_GET['table']);
    $id_value = sanitize_input($_GET['id']);

    // Tentukan field ID dan halaman redirect berdasarkan tabel
    switch ($table) {
        case 'nasabah':
            $id_field = 'id_nasabah';
            $redirect_page = ($_SESSION['level'] == 'admin') ? 'data_nasabah.php' : 'data_nasabah_analis.php';
            $success_message = 'Data nasabah berhasil dihapus';
            break;
        case 'pengguna':
        case 'users':
        case 'user':
            $id_field = 'id_user';
            $table = 'users'; // Standardisasi nama tabel
            $redirect_page = 'data_user.php';
            $success_message = 'Data pengguna berhasil dihapus';
            break;
        case 'model_ml':
            $id_field = 'id_model';
            $redirect_page = 'model_dataset.php';
            $success_message = 'Data model berhasil dihapus';
            break;
        case 'dataset':
            $id_field = 'id_dataset';
            $redirect_page = 'model_dataset.php';
            $success_message = 'Data dataset berhasil dihapus';
            break;
        case 'laporan_prediksi':
            $id_field = 'id_laporan';
            $redirect_page = 'laporan.php';
            $success_message = 'Data laporan berhasil dihapus';
            break;
        default:
            // Tabel tidak valid
            $error_message = 'Tabel tidak valid';
            break;
    }

    // Jika tabel valid, lakukan penghapusan
    if (!empty($id_field)) {
        // Cek apakah ada konfirmasi
        if (isset($_GET['confirm']) && $_GET['confirm'] == 'yes') {
            // Lakukan penghapusan berdasarkan tabel
            switch ($table) {
                case 'nasabah':
                    // Cek apakah nasabah memiliki prediksi
                    $check_sql = "SELECT COUNT(*) as count FROM nasabah WHERE id_nasabah = ?";
                    $check_stmt = $koneksi->prepare($check_sql);
                    $check_stmt->bind_param("i", $id_value);
                    $check_stmt->execute();
                    $result = $check_stmt->get_result();
                    $count = $result->fetch_assoc()['count'];

                    if ($count > 0) {
                        // Hapus prediksi detail terlebih dahulu
                        $delete_pred_sql = "DELETE FROM nasabah WHERE id_nasabah = ?";
                        $delete_pred_stmt = $koneksi->prepare($delete_pred_sql);
                        $delete_pred_stmt->bind_param("i", $id_value);
                        $delete_pred_stmt->execute();
                    }

                    // Hapus nasabah
                    $delete_sql = "DELETE FROM nasabah WHERE id_nasabah = ?";
                    $delete_stmt = $koneksi->prepare($delete_sql);
                    $delete_stmt->bind_param("i", $id_value);
                    $delete_stmt->execute();
                    break;

                case 'users':
                case 'user':
                case 'pengguna':
                    // Cek apakah pengguna yang akan dihapus adalah diri sendiri
                    $check_sql = "SELECT username FROM users WHERE id_user = ?";
                    $check_stmt = $koneksi->prepare($check_sql);
                    $check_stmt->bind_param("i", $id_value);
                    $check_stmt->execute();
                    $result = $check_stmt->get_result();
                    $user = $result->fetch_assoc();

                    if ($user && $user['username'] == $_SESSION['username']) {
                        $error_message = 'Anda tidak dapat menghapus akun yang sedang digunakan';
                    } else {
                        $delete_sql = "DELETE FROM users WHERE id_user = ?";
                        $delete_stmt = $koneksi->prepare($delete_sql);
                        $delete_stmt->bind_param("i", $id_value);
                        $delete_stmt->execute();
                    }
                    break;

                case 'model_ml':
                    // Cek apakah model sedang aktif
                    $check_sql = "SELECT is_active FROM model_ml WHERE id_model = ?";
                    $check_stmt = $koneksi->prepare($check_sql);
                    $check_stmt->bind_param("i", $id_value);
                    $check_stmt->execute();
                    $result = $check_stmt->get_result();
                    $model = $result->fetch_assoc();

                    if ($model['is_active']) {
                        $error_message = 'Model yang sedang aktif tidak dapat dihapus';
                    } else {
                        // Hapus data terkait
                        $tables = ['training_history', 'fitur_model', 'normalisasi', 'kategori_fitur', 'model_ml'];

                        foreach ($tables as $related_table) {
                            $delete_sql = "DELETE FROM $related_table WHERE id_model = ?";
                            $delete_stmt = $koneksi->prepare($delete_sql);
                            $delete_stmt->bind_param("i", $id_value);
                            $delete_stmt->execute();
                        }
                    }
                    break;

                case 'dataset':
                    // Cek apakah dataset digunakan oleh model
                    $check_sql = "SELECT COUNT(*) as count FROM training_history WHERE id_dataset = ?";
                    $check_stmt = $koneksi->prepare($check_sql);
                    $check_stmt->bind_param("i", $id_value);
                    $check_stmt->execute();
                    $result = $check_stmt->get_result();
                    $count = $result->fetch_assoc()['count'];

                    if ($count > 0) {
                        $error_message = 'Dataset tidak dapat dihapus karena digunakan oleh model';
                    } else {
                        $delete_sql = "DELETE FROM dataset WHERE id_dataset = ?";
                        $delete_stmt = $koneksi->prepare($delete_sql);
                        $delete_stmt->bind_param("i", $id_value);
                        $delete_stmt->execute();
                    }
                    break;

                case 'laporan_prediksi':
                    // Hapus prediksi detail terlebih dahulu
                    $delete_pred_sql = "DELETE FROM prediksi_detail WHERE id_laporan = ?";
                    $delete_pred_stmt = $koneksi->prepare($delete_pred_sql);
                    $delete_pred_stmt->bind_param("i", $id_value);
                    $delete_pred_stmt->execute();

                    // Hapus laporan
                    $delete_sql = "DELETE FROM laporan_prediksi WHERE id_laporan = ?";
                    $delete_stmt = $koneksi->prepare($delete_sql);
                    $delete_stmt->bind_param("i", $id_value);
                    $delete_stmt->execute();
                    break;
            }

            // Redirect ke halaman yang sesuai dengan pesan
            if (empty($error_message)) {
                header("Location: $redirect_page?pesan=hapus_sukses");
                exit;
            }
        }
    }
}

// Jika ada error, redirect ke halaman sebelumnya dengan pesan error
if (!empty($error_message)) {
    header("Location: $redirect_page?pesan=hapus_gagal&error=" . urlencode($error_message));
    exit;
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Konfirmasi Hapus Data</title>

    <!-- Bootstrap Core CSS -->
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/startmin.css" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="assets/css/font-awesome.min.css" rel="stylesheet" type="text/css">
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-md-6 col-md-offset-3">
                <div class="panel panel-danger" style="margin-top: 100px;">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-warning"></i> Konfirmasi Hapus Data</h3>
                    </div>
                    <div class="panel-body">
                        <p>Apakah Anda yakin ingin menghapus data ini? Tindakan ini tidak dapat dibatalkan.</p>
                        <div class="text-center">
                            <a href="hapus_data.php?table=<?php echo $table; ?>&id=<?php echo $id_value; ?>&confirm=yes" class="btn btn-danger">
                                <i class="fa fa-trash"></i> Ya, Hapus Data
                            </a>
                            <a href="<?php echo $redirect_page; ?>" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> Batal
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="assets/js/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="assets/js/bootstrap.min.js"></script>
</body>
</html>
