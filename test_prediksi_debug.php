<?php
// Test debug untuk prediksi

error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'koneksi.php';
include 'cek_session.php';
cek_akses(['analis']);

echo "<h2>🔍 Debug Test Prediksi</h2>\n";

if (isset($_POST['submit'])) {
    echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #ccc; margin: 10px 0;'>";
    echo "<h3>📋 Data Form yang Diterima:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    // Test 1: Simpan data ke database
    echo "<h3>💾 Test 1: Simpan Data ke Database</h3>";
    try {
        $sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, pekerjaan, penghasilan, jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak, kepemilikan_rumah, umur, tujuan_pinjaman, sumber_data) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'manual')";
        
        $penghasilan = (float)str_replace(['.', ','], '', $_POST['penghasilan']);
        $jumlah_pinjaman = (float)str_replace(['.', ','], '', $_POST['jumlah_pinjaman']);
        
        $stmt = mysqli_prepare($koneksi, $sql);
        mysqli_stmt_bind_param($stmt, "ssssdidissssis",
            $_POST['nama_nasabah'],
            $_POST['jenis_kelamin'],
            $_POST['status_perkawinan'],
            $_POST['pekerjaan'],
            $penghasilan,
            (int)$_POST['jumlah_tanggungan'],
            $jumlah_pinjaman,
            (int)$_POST['jangka_waktu'],
            $_POST['jaminan'],
            $_POST['tahun_kendaraan'],
            $_POST['status_pajak'],
            $_POST['kepemilikan_rumah'],
            (int)$_POST['umur'],
            $_POST['tujuan_pinjaman'] ?? 'Kebutuhan Pribadi'
        );
        
        if (mysqli_stmt_execute($stmt)) {
            $id_nasabah = mysqli_insert_id($koneksi);
            echo "✅ Data berhasil disimpan dengan ID: $id_nasabah<br>";
            
            // Test 2: Ambil data nasabah
            echo "<h3>📖 Test 2: Ambil Data Nasabah</h3>";
            $query = "SELECT * FROM nasabah WHERE id_nasabah = ?";
            $stmt2 = mysqli_prepare($koneksi, $query);
            mysqli_stmt_bind_param($stmt2, "i", $id_nasabah);
            mysqli_stmt_execute($stmt2);
            $result = mysqli_stmt_get_result($stmt2);
            $nasabah_data = mysqli_fetch_assoc($result);
            
            if ($nasabah_data) {
                echo "✅ Data nasabah berhasil diambil<br>";
                echo "<pre>" . print_r($nasabah_data, true) . "</pre>";
                
                // Test 3: Test koneksi ke server Python
                echo "<h3>🐍 Test 3: Koneksi ke Server Python</h3>";
                $python_url = 'http://localhost:5000/api/predict';
                $test_data = ['id_nasabah' => $id_nasabah];
                
                $ch = curl_init($python_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curl_error = curl_error($ch);
                curl_close($ch);
                
                echo "HTTP Code: $http_code<br>";
                echo "Response: " . htmlspecialchars($response) . "<br>";
                if ($curl_error) {
                    echo "cURL Error: $curl_error<br>";
                }
                
                // Test 4: Test prediksi menggunakan api_predict.php
                echo "<h3>🔮 Test 4: Prediksi menggunakan api_predict.php</h3>";
                require_once 'api_predict.php';
                
                try {
                    $hasil_prediksi = prediksi_dengan_backpropagation($nasabah_data);
                    echo "✅ Prediksi berhasil!<br>";
                    echo "<pre>" . print_r($hasil_prediksi, true) . "</pre>";
                    
                    // Test 5: Simpan hasil prediksi
                    echo "<h3>💾 Test 5: Simpan Hasil Prediksi</h3>";
                    $id_prediksi = simpan_hasil_prediksi($nasabah_data, $hasil_prediksi);
                    
                    if ($id_prediksi) {
                        echo "✅ Hasil prediksi berhasil disimpan dengan ID: $id_prediksi<br>";
                        
                        // Test 6: Tampilkan hasil prediksi
                        echo "<h3>🎯 Test 6: Hasil Prediksi Final</h3>";
                        echo "<div style='background: " . ($hasil_prediksi['hasil_prediksi'] == 'Layak' ? '#d4edda' : '#f8d7da') . "; padding: 15px; border: 1px solid " . ($hasil_prediksi['hasil_prediksi'] == 'Layak' ? '#c3e6cb' : '#f5c6cb') . "; margin: 10px 0;'>";
                        echo "<h4>" . ($hasil_prediksi['hasil_prediksi'] == 'Layak' ? '✅' : '❌') . " " . $hasil_prediksi['hasil_prediksi'] . "</h4>";
                        echo "<p><strong>Probabilitas:</strong> " . number_format($hasil_prediksi['probabilitas'] * 100, 2) . "%</p>";
                        echo "<p><strong>Keterangan:</strong> " . htmlspecialchars($hasil_prediksi['keterangan']) . "</p>";
                        echo "<p><a href='hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi'>Lihat Detail Lengkap</a></p>";
                        echo "</div>";
                        
                    } else {
                        echo "❌ Gagal menyimpan hasil prediksi<br>";
                    }
                    
                } catch (Exception $e) {
                    echo "❌ Error dalam prediksi: " . $e->getMessage() . "<br>";
                    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
                }
                
            } else {
                echo "❌ Data nasabah tidak ditemukan setelah insert<br>";
            }
            
        } else {
            echo "❌ Gagal menyimpan data: " . mysqli_error($koneksi) . "<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "<br>";
    }
    
    echo "</div>";
}
?>

<style>
.form-container {
    max-width: 800px;
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #ddd;
    background: #f9f9f9;
}
.form-group {
    margin: 10px 0;
}
.form-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}
.form-group input, .form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}
.btn {
    padding: 10px 20px;
    background: #007cba;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}
.btn:hover {
    background: #005a8b;
}
</style>

<div class="form-container">
    <h3>📝 Form Test Debug Prediksi</h3>
    <form method="post">
        <div style="display: flex; gap: 20px;">
            <div style="flex: 1;">
                <div class="form-group">
                    <label>Nama Nasabah:</label>
                    <input type="text" name="nama_nasabah" value="Test Debug <?php echo date('H:i:s'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Jenis Kelamin:</label>
                    <select name="jenis_kelamin" required>
                        <option value="Laki-laki">Laki-laki</option>
                        <option value="Perempuan">Perempuan</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Status Perkawinan:</label>
                    <select name="status_perkawinan" required>
                        <option value="Menikah">Menikah</option>
                        <option value="Belum Menikah">Belum Menikah</option>
                        <option value="Cerai">Cerai</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Pekerjaan:</label>
                    <select name="pekerjaan" required>
                        <option value="PNS">PNS</option>
                        <option value="Swasta">Swasta</option>
                        <option value="Wiraswasta">Wiraswasta</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Umur:</label>
                    <input type="number" name="umur" value="30" min="20" max="60" required>
                </div>
            </div>
            
            <div style="flex: 1;">
                <div class="form-group">
                    <label>Penghasilan (tanpa titik):</label>
                    <input type="number" name="penghasilan" value="5000000" required>
                </div>
                
                <div class="form-group">
                    <label>Jumlah Tanggungan:</label>
                    <input type="number" name="jumlah_tanggungan" value="2" min="0" max="10" required>
                </div>
                
                <div class="form-group">
                    <label>Jumlah Pinjaman (tanpa titik):</label>
                    <input type="number" name="jumlah_pinjaman" value="20000000" required>
                </div>
                
                <div class="form-group">
                    <label>Jangka Waktu (bulan):</label>
                    <input type="number" name="jangka_waktu" value="12" min="6" max="60" required>
                </div>
                
                <div class="form-group">
                    <label>Jaminan:</label>
                    <select name="jaminan" required>
                        <option value="BPKB Motor">BPKB Motor</option>
                        <option value="BPKB Mobil">BPKB Mobil</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Tahun Kendaraan:</label>
                    <input type="number" name="tahun_kendaraan" value="2020" min="1990" max="<?php echo date('Y'); ?>" required>
                </div>
                
                <div class="form-group">
                    <label>Status Pajak:</label>
                    <select name="status_pajak" required>
                        <option value="Aktif">Aktif</option>
                        <option value="Tidak Aktif">Tidak Aktif</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Kepemilikan Rumah:</label>
                    <select name="kepemilikan_rumah" required>
                        <option value="Milik Sendiri">Milik Sendiri</option>
                        <option value="Kontrak">Kontrak</option>
                        <option value="Orang Tua">Orang Tua</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Tujuan Pinjaman:</label>
                    <input type="text" name="tujuan_pinjaman" value="Test Debug">
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <button type="submit" name="submit" class="btn">
                🚀 Test Debug Prediksi
            </button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <h3>🔗 Link Navigasi:</h3>
    <a href="prediksi_baru.php">📝 Form Prediksi Asli</a> | 
    <a href="daftar_kelayakan.php">📊 Daftar Kelayakan</a> | 
    <a href="hasil_prediksi.php">📋 Hasil Prediksi</a>
</div>
