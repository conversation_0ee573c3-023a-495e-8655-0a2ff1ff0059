<?php
include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);

// Cek apakah tabel nasabah ada
$check_table = "SHOW TABLES LIKE 'nasabah'";
$table_exists = mysqli_query($koneksi, $check_table);

// Include navigation setelah pengecekan database berhasil
require_once 'nav_analis.php';

// Cek apakah tabel nasabah ada dan memiliki kolom yang diperlukan
$check_nasabah_table = "SHOW TABLES LIKE 'nasabah'";
$table_nasabah_exists = mysqli_query($koneksi, $check_nasabah_table);

if (!$table_nasabah_exists || mysqli_num_rows($table_nasabah_exists) == 0) {
    // Jika tabel nasabah tidak ada, tampilkan error dan solusi
    echo '<div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="alert alert-danger">
                            <h4><i class="fa fa-exclamation-triangle"></i> Error: Tabel Database Tidak Ditemukan</h4>
                            <p>Tabel <code>nasabah</code> tidak ditemukan di database.</p>
                            <hr>
                            <h5>Cara memperbaiki:</h5>
                            <ol>
                                <li>Jalankan: <code>fix_laporan_error.bat</code></li>
                                <li>Atau jalankan: <code>php create_tables.php</code></li>
                                <li>Atau import: <code>database_setup.sql</code></li>
                            </ol>
                            <p><a href="javascript:history.back()" class="btn btn-default">Kembali</a></p>
                        </div>
                    </div>
                </div>
            </div>
          </div>';
    require_once 'foot.php';
    exit;
}

// Cek apakah kolom kelayakan ada di tabel nasabah
$check_column = "SHOW COLUMNS FROM nasabah LIKE 'kelayakan'";
$column_exists = mysqli_query($koneksi, $check_column);

if (!$column_exists || mysqli_num_rows($column_exists) == 0) {
    // Jika kolom kelayakan tidak ada, tambahkan kolom
    $add_column = "ALTER TABLE nasabah ADD COLUMN kelayakan ENUM('Layak', 'Tidak Layak') NULL DEFAULT NULL";
    $result_add = mysqli_query($koneksi, $add_column);
    if (!$result_add) {
        error_log("Error adding kelayakan column: " . mysqli_error($koneksi));
    }
}

// Ambil daftar nasabah dengan kelayakan
// Prioritas: hasil_prediksi > nasabah.kelayakan
$sql = "SELECT n.*,
        COALESCE(hp.hasil_prediksi, n.kelayakan) as kelayakan,
        COALESCE(hp.probabilitas, 0.5) as probabilitas,
        COALESCE(hp.tanggal_prediksi, n.created_at) as tanggal_prediksi,
        COALESCE(hp.id_prediksi, n.id_nasabah) as id_prediksi
       FROM nasabah n
       LEFT JOIN (
           SELECT hp1.*
           FROM hasil_prediksi hp1
           INNER JOIN (
               SELECT id_nasabah, MAX(tanggal_prediksi) as max_tanggal
               FROM hasil_prediksi
               GROUP BY id_nasabah
           ) hp2 ON hp1.id_nasabah = hp2.id_nasabah AND hp1.tanggal_prediksi = hp2.max_tanggal
       ) hp ON n.id_nasabah = hp.id_nasabah
       WHERE (hp.hasil_prediksi IS NOT NULL OR n.kelayakan IS NOT NULL)
       ORDER BY COALESCE(hp.tanggal_prediksi, n.created_at) DESC";

// Ambil data prediksi terpisah untuk digunakan nanti (dengan error handling)
$prediksi_data = [];

// Cek apakah tabel prediksi_detail ada sebelum query
$check_prediksi_table = "SHOW TABLES LIKE 'prediksi_detail'";
$table_prediksi_exists = mysqli_query($koneksi, $check_prediksi_table);

if ($table_prediksi_exists && mysqli_num_rows($table_prediksi_exists) > 0) {
    $sql_prediksi = "SELECT pd.id_nasabah, pd.id_prediksi, pd.tanggal_prediksi, pd.id_laporan
                    FROM prediksi_detail pd
                    INNER JOIN (
                        SELECT id_nasabah, MAX(tanggal_prediksi) as max_tanggal
                        FROM prediksi_detail
                        GROUP BY id_nasabah
                    ) latest ON pd.id_nasabah = latest.id_nasabah AND pd.tanggal_prediksi = latest.max_tanggal";

    $result_prediksi = mysqli_query($koneksi, $sql_prediksi);
    if ($result_prediksi) {
        while ($row = mysqli_fetch_assoc($result_prediksi)) {
            $prediksi_data[$row['id_nasabah']] = [
                'id_prediksi' => $row['id_prediksi'],
                'tanggal_prediksi' => $row['tanggal_prediksi'],
                'id_laporan' => $row['id_laporan']
            ];
        }
    } else {
        // Log error jika query gagal
        error_log("Error in prediksi query: " . mysqli_error($koneksi));
    }
}

// Eksekusi query utama dengan error handling
$result = mysqli_query($koneksi, $sql);
if (!$result) {
    require_once 'nav_analis.php';
    echo '<div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="alert alert-danger">
                            <h4><i class="fa fa-exclamation-triangle"></i> Error Database</h4>
                            <p>Gagal mengambil data nasabah: ' . htmlspecialchars(mysqli_error($koneksi)) . '</p>
                            <hr>
                            <h5>Solusi:</h5>
                            <ol>
                                <li>Pastikan tabel nasabah sudah dibuat</li>
                                <li>Jalankan: <code>fix_laporan_error.bat</code></li>
                                <li>Atau jalankan: <code>php create_tables.php</code></li>
                            </ol>
                            <p><a href="javascript:history.back()" class="btn btn-default">Kembali</a></p>
                        </div>
                    </div>
                </div>
            </div>
          </div>';
    require_once 'foot.php';
    exit;
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Daftar Kelayakan Nasabah</h1>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-md-8">
                                <h4>Daftar Nasabah dengan Status Kelayakan
                                    <?php
                                    // Hitung jumlah data - gunakan query yang sama dengan tabel utama
                                    $count_query = "SELECT COUNT(DISTINCT n.id_nasabah) as total
                                                   FROM nasabah n
                                                   LEFT JOIN (
                                                       SELECT hp1.*
                                                       FROM hasil_prediksi hp1
                                                       INNER JOIN (
                                                           SELECT id_nasabah, MAX(tanggal_prediksi) as max_tanggal
                                                           FROM hasil_prediksi
                                                           GROUP BY id_nasabah
                                                       ) hp2 ON hp1.id_nasabah = hp2.id_nasabah AND hp1.tanggal_prediksi = hp2.max_tanggal
                                                   ) hp ON n.id_nasabah = hp.id_nasabah
                                                   WHERE (hp.hasil_prediksi IS NOT NULL OR n.kelayakan IS NOT NULL)";
                                    $count_result = mysqli_query($koneksi, $count_query);
                                    $count_data = mysqli_fetch_assoc($count_result);
                                    echo "(" . $count_data['total'] . " nasabah)";
                                    ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="btn-group">
                                    <a href="export_kelayakan.php" class="btn btn-success btn-sm" target="_blank">
                                        <i class="fa fa-file-excel-o"></i> Export Semua
                                    </a>
                                    <a href="export_kelayakan.php?kelayakan=Layak" class="btn btn-primary btn-sm" target="_blank">
                                        <i class="fa fa-file-excel-o"></i> Export Layak
                                    </a>
                                    <a href="export_kelayakan.php?kelayakan=Tidak+Layak" class="btn btn-danger btn-sm" target="_blank">
                                        <i class="fa fa-file-excel-o"></i> Export Tidak Layak
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover" id="dataTables-kelayakan">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Nama Nasabah</th>
                                        <th>Umur</th>
                                        <th>Jenis Kelamin</th>
                                        <th>Pekerjaan</th>
                                        <th>Penghasilan</th>
                                        <th>Jumlah Tanggungan</th>
                                        <th>Jumlah Pinjaman</th>
                                        <th>Kepemilikan Rumah</th>
                                        <th>Jaminan</th>
                                        <th>Tahun Kendaraan</th>
                                        <th>Status Pajak</th>
                                        <th>Tujuan Pinjaman</th>
                                        <th>Status Kelayakan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    if (mysqli_num_rows($result) > 0) {
                                        $no = 1;
                                        while ($row = mysqli_fetch_assoc($result)) {
                                    ?>
                                    <tr>
                                        <td><?php echo $no++; ?></td>
                                        <td><?php echo htmlspecialchars($row['nama_nasabah']); ?></td>
                                        <td><?php echo $row['umur']; ?> tahun</td>
                                        <td><?php echo htmlspecialchars($row['jenis_kelamin']); ?></td>
                                        <td><?php echo htmlspecialchars($row['pekerjaan']); ?></td>
                                        <td>Rp <?php echo number_format($row['penghasilan'], 0, ',', '.'); ?></td>
                                        <td><?php echo htmlspecialchars($row['jumlah_tanggungan'] ?? '-'); ?></td>
                                        <td>Rp <?php echo number_format($row['jumlah_pinjaman'], 0, ',', '.'); ?></td>
                                        <td><?php echo htmlspecialchars($row['kepemilikan_rumah']); ?></td>
                                        <td><?php echo htmlspecialchars($row['jaminan']); ?></td>
                                        <td><?php echo htmlspecialchars($row['tahun_kendaraan'] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($row['status_pajak'] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($row['tujuan_pinjaman'] ?? 'Kebutuhan Pribadi'); ?></td>
                                        <td>
                                            <span class="label label-<?php echo $row['kelayakan'] == 'Layak' ? 'success' : 'danger'; ?>">
                                                <?php echo htmlspecialchars($row['kelayakan']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($row['id_prediksi']): ?>
                                                <a href="hasil_prediksi.php?id_nasabah=<?php echo htmlspecialchars($row['id_nasabah']); ?>&id_prediksi=<?php echo htmlspecialchars($row['id_prediksi']); ?>"
                                                   class="btn btn-info btn-sm" title="Lihat Detail Prediksi">
                                                    <i class="fa fa-eye"></i> Detail
                                                </a>
                                            <?php else: ?>
                                                <a href="prediksi_nasabah.php?prediksi=<?php echo htmlspecialchars($row['id_nasabah']); ?>"
                                                   class="btn btn-warning btn-sm" title="Lakukan Prediksi">
                                                    <i class="fa fa-calculator"></i> Prediksi
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php
                                        }
                                    } else {
                                    ?>
                                    <tr>
                                        <td colspan="15" class="text-center">Tidak ada data nasabah dengan status kelayakan</td>
                                    </tr>
                                    <?php
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h4>Statistik Kelayakan</h4>
                    </div>
                    <div class="panel-body">
                        <?php
                        // Hitung statistik - gunakan data yang sama dengan tabel utama
                        $sql_stat = "SELECT
                                        COALESCE(hp.hasil_prediksi, n.kelayakan) as kelayakan,
                                        COUNT(*) as jumlah
                                    FROM nasabah n
                                    LEFT JOIN (
                                        SELECT hp1.*
                                        FROM hasil_prediksi hp1
                                        INNER JOIN (
                                            SELECT id_nasabah, MAX(tanggal_prediksi) as max_tanggal
                                            FROM hasil_prediksi
                                            GROUP BY id_nasabah
                                        ) hp2 ON hp1.id_nasabah = hp2.id_nasabah AND hp1.tanggal_prediksi = hp2.max_tanggal
                                    ) hp ON n.id_nasabah = hp.id_nasabah
                                    WHERE (hp.hasil_prediksi IS NOT NULL OR n.kelayakan IS NOT NULL)
                                    GROUP BY COALESCE(hp.hasil_prediksi, n.kelayakan)";
                        $result_stat = mysqli_query($koneksi, $sql_stat);

                        $layak = 0;
                        $tidak_layak = 0;

                        if ($result_stat) {
                            while ($row = mysqli_fetch_assoc($result_stat)) {
                                if ($row['kelayakan'] == 'Layak') {
                                    $layak = $row['jumlah'];
                                } else {
                                    $tidak_layak = $row['jumlah'];
                                }
                            }
                        } else {
                            error_log("Error in statistics query: " . mysqli_error($koneksi));
                        }

                        $total = $layak + $tidak_layak;
                        $persen_layak = $total > 0 ? round(($layak / $total) * 100, 2) : 0;
                        $persen_tidak_layak = $total > 0 ? round(($tidak_layak / $total) * 100, 2) : 0;
                        ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="panel panel-success">
                                    <div class="panel-heading">
                                        <h4>Nasabah Layak</h4>
                                    </div>
                                    <div class="panel-body">
                                        <h3 class="text-center"><?php echo $layak; ?> Nasabah (<?php echo $persen_layak; ?>%)</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="panel panel-danger">
                                    <div class="panel-heading">
                                        <h4>Nasabah Tidak Layak</h4>
                                    </div>
                                    <div class="panel-body">
                                        <h3 class="text-center"><?php echo $tidak_layak; ?> Nasabah (<?php echo $persen_tidak_layak; ?>%)</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="progress">
                            <div class="progress-bar progress-bar-success" style="width: <?php echo $persen_layak; ?>%">
                                <?php echo $persen_layak; ?>% Layak
                            </div>
                            <div class="progress-bar progress-bar-danger" style="width: <?php echo $persen_tidak_layak; ?>%">
                                <?php echo $persen_tidak_layak; ?>% Tidak Layak
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#dataTables-kelayakan').DataTable({
        responsive: true,
        "language": {
            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
            "zeroRecords": "Tidak ada data yang ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total entri)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "pageLength": 25,
        "order": [[ 0, "desc" ]]
    });
});
</script>

<?php
require_once 'foot.php';
?>
