<?php
include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);

// Inisialisasi variabel
$error_message = '';

// Cek apakah form telah disubmit
if (isset($_POST['predict_leasing']) && isset($_POST['id_nasabah'])) {
    $id_nasabah = $_POST['id_nasabah'];

    // Validasi ID nasabah
    if (empty($id_nasabah)) {
        $error_message = "Silakan pilih nasabah dari daftar terlebih dahulu.";
    } else {
        // Cek apakah nasabah ada di database
        $sql_check = "SELECT * FROM nasabah WHERE id_nasabah = ?";
        $stmt_check = mysqli_prepare($koneksi, $sql_check);
        mysqli_stmt_bind_param($stmt_check, "i", $id_nasabah);
        mysqli_stmt_execute($stmt_check);
        $result_check = mysqli_stmt_get_result($stmt_check);

        if (mysqli_num_rows($result_check) == 0) {
            $error_message = "Nasabah tidak ditemukan.";
        } else {
            // Lakukan prediksi langsung tanpa menggunakan API
            error_log("Melakukan prediksi langsung untuk nasabah ID: " . $id_nasabah);

            // Ambil data nasabah
            $query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
            $stmt_nasabah = mysqli_prepare($koneksi, $query_nasabah);
            mysqli_stmt_bind_param($stmt_nasabah, "i", $id_nasabah);
            mysqli_stmt_execute($stmt_nasabah);
            $result_nasabah = mysqli_stmt_get_result($stmt_nasabah);
            $nasabah_data = mysqli_fetch_assoc($result_nasabah);

            error_log("Data nasabah: " . ($nasabah_data ? "Ditemukan" : "Tidak ditemukan"));
            if ($nasabah_data) {
                error_log("Nama nasabah: " . $nasabah_data['nama_nasabah']);

                // Cek apakah kolom sumber_data sudah ada di tabel nasabah
                $check_column = mysqli_query($koneksi, "SHOW COLUMNS FROM nasabah LIKE 'sumber_data'");

                if (mysqli_num_rows($check_column) == 0) {
                    // Kolom sumber_data belum ada, tambahkan kolom tersebut
                    $add_column = "ALTER TABLE nasabah ADD COLUMN sumber_data VARCHAR(20) DEFAULT NULL";
                    mysqli_query($koneksi, $add_column);
                    error_log("Kolom sumber_data ditambahkan ke tabel nasabah");
                }

                // Update sumber_data menjadi 'leasing'
                $update_query = "UPDATE nasabah SET sumber_data = 'leasing' WHERE id_nasabah = ?";
                $stmt_update = mysqli_prepare($koneksi, $update_query);
                mysqli_stmt_bind_param($stmt_update, "i", $id_nasabah);
                mysqli_stmt_execute($stmt_update);
                error_log("Sumber data nasabah diupdate menjadi 'leasing'");

                // Validasi data sebelum prediksi
                $validation_errors = [];

                // Validasi data yang diperlukan untuk model
                if (empty($nasabah_data['umur']) || $nasabah_data['umur'] < 18 || $nasabah_data['umur'] > 70) {
                    $validation_errors[] = "Umur harus antara 18-70 tahun";
                }
                if (empty($nasabah_data['jenis_kelamin'])) {
                    $validation_errors[] = "Jenis kelamin harus diisi";
                }
                if (empty($nasabah_data['pekerjaan'])) {
                    $validation_errors[] = "Pekerjaan harus diisi";
                }
                if (empty($nasabah_data['penghasilan']) || $nasabah_data['penghasilan'] < 1000000) {
                    $validation_errors[] = "Penghasilan minimal Rp 1.000.000";
                }
                if (!isset($nasabah_data['jumlah_tanggungan']) || $nasabah_data['jumlah_tanggungan'] < 0) {
                    $validation_errors[] = "Jumlah tanggungan harus diisi dengan nilai minimal 0";
                }
                if (empty($nasabah_data['jumlah_pinjaman']) || $nasabah_data['jumlah_pinjaman'] < 5000000) {
                    $validation_errors[] = "Jumlah pinjaman minimal Rp 5.000.000";
                }
                if (empty($nasabah_data['jangka_waktu']) || $nasabah_data['jangka_waktu'] < 6) {
                    $validation_errors[] = "Jangka waktu minimal 6 bulan";
                }
                if (empty($nasabah_data['jaminan'])) {
                    $validation_errors[] = "Jaminan harus diisi";
                }
                if (empty($nasabah_data['kepemilikan_rumah'])) {
                    $validation_errors[] = "Status kepemilikan rumah harus diisi";
                }

                // Validasi khusus untuk jaminan kendaraan
                $jaminan = $nasabah_data['jaminan'];
                if (strpos($jaminan, 'BPKB') !== false || strpos($jaminan, 'Motor') !== false || strpos($jaminan, 'Mobil') !== false) {
                    if (empty($nasabah_data['tahun_kendaraan']) || $nasabah_data['tahun_kendaraan'] < 1990) {
                        $validation_errors[] = "Tahun kendaraan harus diisi dan minimal tahun 1990";
                    }
                    if (empty($nasabah_data['status_pajak'])) {
                        $validation_errors[] = "Status pajak kendaraan harus diisi";
                    }
                }

                // Jika ada error validasi, kembalikan ke halaman form
                if (!empty($validation_errors)) {
                    $error_message = "Data tidak valid untuk prediksi:\n" . implode("\n", $validation_errors);
                    error_log("Validasi gagal: " . $error_message);
                    header("Location: prediksi_nasabah.php?error=" . urlencode($error_message));
                    exit;
                }

                // Lakukan prediksi langsung dengan model backpropagation
                require_once 'api_predict.php';
                try {
                    error_log("Memulai prediksi untuk nasabah: " . $nasabah_data['nama_nasabah']);
                    $hasil_prediksi = prediksi_dengan_backpropagation($nasabah_data);
                    error_log("Hasil prediksi: " . print_r($hasil_prediksi, true));

                    // Validasi hasil prediksi
                    if (!isset($hasil_prediksi['hasil_prediksi']) || !isset($hasil_prediksi['probabilitas'])) {
                        throw new Exception("Format hasil prediksi tidak valid");
                    }

                } catch (Exception $e) {
                    error_log("Error saat melakukan prediksi: " . $e->getMessage());

                    // Berikan pesan error yang lebih spesifik
                    $error_detail = $e->getMessage();
                    if (strpos($error_detail, 'features') !== false) {
                        $error_message = "Error pada model prediksi: Ketidaksesuaian fitur data. Pastikan semua data nasabah lengkap dan valid.";
                    } elseif (strpos($error_detail, 'Connection') !== false || strpos($error_detail, 'cURL') !== false) {
                        $error_message = "Error koneksi ke server model prediksi. Pastikan server Python berjalan di localhost:5000.";
                    } else {
                        $error_message = "Gagal melakukan prediksi: " . $error_detail;
                    }

                    header("Location: prediksi_nasabah.php?error=" . urlencode($error_message));
                    exit;
                }

                // Simpan hasil prediksi
                try {
                    $id_prediksi = simpan_hasil_prediksi($nasabah_data, $hasil_prediksi);
                    error_log("Prediksi berhasil disimpan. ID Prediksi: $id_prediksi");

                    if (!$id_prediksi) {
                        throw new Exception("Gagal menyimpan hasil prediksi ke database");
                    }

                    // Update status kelayakan di tabel nasabah
                    $update_kelayakan = "UPDATE nasabah SET kelayakan = ? WHERE id_nasabah = ?";
                    $stmt_kelayakan = mysqli_prepare($koneksi, $update_kelayakan);
                    mysqli_stmt_bind_param($stmt_kelayakan, "si", $hasil_prediksi['hasil_prediksi'], $id_nasabah);

                    if (!mysqli_stmt_execute($stmt_kelayakan)) {
                        error_log("Warning: Gagal update status kelayakan di tabel nasabah");
                    } else {
                        error_log("Status kelayakan nasabah berhasil diupdate: " . $hasil_prediksi['hasil_prediksi']);
                    }

                    // Redirect ke halaman hasil prediksi dengan pesan sukses
                    $success_message = "Prediksi kelayakan berhasil dilakukan untuk nasabah " . $nasabah_data['nama_nasabah'];
                    error_log("Redirect ke hasil_prediksi_website.php dengan id_nasabah=$id_nasabah dan id_prediksi=$id_prediksi");

                    header("Location: hasil_prediksi_website.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi&success=" . urlencode($success_message));
                    exit;

                } catch (Exception $e) {
                    error_log("Error saat menyimpan hasil prediksi: " . $e->getMessage());
                    $error_message = "Prediksi berhasil dilakukan, tetapi gagal menyimpan hasil: " . $e->getMessage();
                    header("Location: prediksi_website.php?error=" . urlencode($error_message));
                    exit;
                }
            } else {
                error_log("Data nasabah tidak ditemukan untuk ID: $id_nasabah");
                $error_message = "Data nasabah tidak ditemukan.";
            }
        }
    }
}

// Jika terjadi error, redirect kembali ke halaman form dengan pesan error
if ($error_message) {
    header("Location: prediksi_website.php?error=" . urlencode($error_message));
    exit;
}
?>
