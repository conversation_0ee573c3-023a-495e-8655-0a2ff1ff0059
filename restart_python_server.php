<?php
// Script untuk restart server Python dan memastikan menggunakan versi terbaru

echo "<h2>Restart Server Python</h2>\n";

// Cek apakah server Python sedang berjalan
echo "<h3>1. Cek Status Server Python</h3>\n";
$python_url = 'http://localhost:5000/api/predict';
$ch = curl_init($python_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['test' => true]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 3);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "<p style='color: orange;'>⚠️ Server Python tidak berjalan: $curl_error</p>\n";
} else {
    echo "<p style='color: green;'>✅ Server Python berjalan (HTTP $http_code)</p>\n";
    echo "<p>Response: " . htmlspecialchars($response) . "</p>\n";
}

echo "<h3>2. Instruksi Restart Server Python</h3>\n";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #ccc; margin: 10px 0;'>";
echo "<h4>📋 Langkah-langkah Restart:</h4>\n";
echo "<ol>\n";
echo "<li><strong>Tutup server Python yang sedang berjalan:</strong><br>";
echo "   - Jika menggunakan Command Prompt/Terminal, tekan <code>Ctrl+C</code><br>";
echo "   - Atau tutup jendela Command Prompt yang menjalankan server Python</li>\n";
echo "<li><strong>Buka Command Prompt baru:</strong><br>";
echo "   - Tekan <code>Windows + R</code>, ketik <code>cmd</code>, tekan Enter</li>\n";
echo "<li><strong>Masuk ke direktori project:</strong><br>";
echo "   - Ketik: <code>cd c:\\laragon\\www\\ngasal</code></li>\n";
echo "<li><strong>Jalankan server Python:</strong><br>";
echo "   - Ketik: <code>python model_wrapper.py</code><br>";
echo "   - Atau double-click file: <code>start_model_server.bat</code></li>\n";
echo "<li><strong>Tunggu hingga muncul pesan:</strong><br>";
echo "   - <code>* Running on http://127.0.0.1:5000</code><br>";
echo "   - <code>* Debug mode: on</code></li>\n";
echo "</ol>\n";
echo "</div>";

echo "<h3>3. Verifikasi Perbaikan</h3>\n";
echo "<div style='background: #f0fff0; padding: 15px; border: 1px solid #90EE90; margin: 10px 0;'>";
echo "<h4>✅ Perbaikan yang Sudah Dilakukan:</h4>\n";
echo "<ul>\n";
echo "<li>✅ Mengubah <code>SELECT id</code> menjadi <code>SELECT id_prediksi</code> di model_wrapper.py</li>\n";
echo "<li>✅ Mengubah <code>WHERE id = ?</code> menjadi <code>WHERE id_prediksi = ?</code> di model_wrapper.py</li>\n";
echo "<li>✅ Menambahkan delay dan retry mechanism di PHP</li>\n";
echo "<li>✅ Memperbaiki error handling dan logging</li>\n";
echo "</ul>\n";
echo "<p><strong>Setelah restart, server Python akan menggunakan kode yang sudah diperbaiki!</strong></p>\n";
echo "</div>";

echo "<h3>4. Test Setelah Restart</h3>\n";
echo "<div style='background: #fff8dc; padding: 15px; border: 1px solid #ddd; margin: 10px 0;'>";
echo "<h4>🧪 Langkah Test:</h4>\n";
echo "<ol>\n";
echo "<li>Restart server Python sesuai instruksi di atas</li>\n";
echo "<li>Refresh halaman ini untuk cek status server</li>\n";
echo "<li>Test dengan <a href='test_form_simple.php'>Form Test Sederhana</a></li>\n";
echo "<li>Jika berhasil, test dengan <a href='prediksi_baru.php'>Form Prediksi Asli</a></li>\n";
echo "</ol>\n";
echo "</div>";

echo "<h3>5. Monitoring</h3>\n";
echo "<p>\n";
echo "<a href='monitor_logs.php' style='margin-right: 10px;'>📊 Monitor Log</a>\n";
echo "<a href='debug_data.php' style='margin-right: 10px;'>🔍 Debug Data</a>\n";
echo "<a href='check_python_server.php' style='margin-right: 10px;'>🐍 Status Python Server</a>\n";
echo "</p>\n";

// Auto refresh setiap 10 detik untuk cek status
echo "<script>\n";
echo "setTimeout(function() {\n";
echo "    window.location.reload();\n";
echo "}, 10000);\n";
echo "</script>\n";

echo "<p><small>Halaman ini akan refresh otomatis setiap 10 detik untuk cek status server.</small></p>\n";
?>
