@echo off
echo ======================================================
echo Memulai Server Model Backpropagation...
echo ======================================================
echo.

echo Memeriksa Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python tidak ditemukan!
    echo Pastikan Python sudah terinstall dan ada di PATH.
    pause
    exit /b 1
)

echo Memeriksa dependencies...
python -c "import flask, sklearn, pandas, numpy" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Dependencies Python tidak lengkap!
    echo Jalankan: pip install -r requirements.txt
    pause
    exit /b 1
)

echo Memeriksa koneksi database...
python -c "import mysql.connector; mysql.connector.connect(host='localhost', user='root', password='', database='sistem_prediksi_backpropagation')" >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Tidak dapat terhubung ke database MySQL.
    echo Pastikan MySQL berjalan dan database sudah dibuat.
    echo Server akan tetap berjalan dalam mode fallback.
)

echo.
echo Memulai server Flask di port 5000...
echo Server akan berjalan di: http://localhost:5000
echo.
echo JANGAN TUTUP JENDELA INI selama sistem digunakan!
echo.
python model_wrapper.py
echo.
echo Server berhenti. Tekan tombol apa saja untuk keluar...
pause