<?php
// Script untuk membersihkan data test yang tidak valid

include 'koneksi.php';

echo "<h2>Cleanup Data Test</h2>\n";

// Hapus data test dengan nama yang tidak valid
$cleanup_queries = [
    "DELETE FROM hasil_prediksi WHERE id_nasabah IN (SELECT id_nasabah FROM nasabah WHERE nama_nasabah LIKE '%yufuduhfjkdsjd%')",
    "DELETE FROM prediksi_detail WHERE id_nasabah IN (SELECT id_nasabah FROM nasabah WHERE nama_nasabah LIKE '%yufuduhfjkdsjd%')",
    "DELETE FROM proses_perhitungan WHERE id_nasabah IN (SELECT id_nasabah FROM nasabah WHERE nama_nasabah LIKE '%yufuduhfjkdsjd%')",
    "DELETE FROM nasabah WHERE nama_nasabah LIKE '%yufuduhfjkdsjd%'"
];

foreach ($cleanup_queries as $query) {
    $result = mysqli_query($koneksi, $query);
    if ($result) {
        $affected = mysqli_affected_rows($koneksi);
        echo "✅ Query berhasil: $affected baris terpengaruh<br>\n";
        echo "Query: " . htmlspecialchars($query) . "<br><br>\n";
    } else {
        echo "❌ Error: " . mysqli_error($koneksi) . "<br>\n";
        echo "Query: " . htmlspecialchars($query) . "<br><br>\n";
    }
}

echo "<hr>\n";
echo "<h3>Status Setelah Cleanup</h3>\n";

// Cek data yang tersisa
$check_queries = [
    "SELECT COUNT(*) as total FROM nasabah" => "Total nasabah",
    "SELECT COUNT(*) as total FROM hasil_prediksi" => "Total hasil prediksi",
    "SELECT COUNT(*) as total FROM nasabah WHERE kelayakan IS NOT NULL" => "Nasabah dengan kelayakan"
];

foreach ($check_queries as $query => $label) {
    $result = mysqli_query($koneksi, $query);
    if ($result) {
        $data = mysqli_fetch_assoc($result);
        echo "$label: " . $data['total'] . "<br>\n";
    }
}

echo "<hr>\n";
echo "<p><a href='debug_data.php'>Lihat Debug Data</a> | ";
echo "<a href='prediksi_baru.php'>Test Prediksi Baru</a></p>\n";
?>
