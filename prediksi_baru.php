<?php
// Aktifkan error reporting untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);
require_once 'nav_analis.php';

// Fungsi untuk memformat angka menjadi format rupiah
function formatRupiah($angka) {
    return "Rp " . number_format($angka, 0, ',', '.');
}

// Inisialisasi variabel
$error_message = '';
$success_message = '';
$show_prediction_result = false;
$prediction_result = null;
$show_prediction_result = false;
$prediction_result = null;

// Log untuk debugging
error_log("Halaman prediksi_baru.php diakses");
$form_data = [
    'nama_nasabah' => '',
    'jenis_kelamin' => 'Laki-laki',
    'status_perkawinan' => 'Menikah',
    'pekerjaan' => 'Swasta',
    'penghasilan' => '5.000.000',
    'jumlah_tanggungan' => '',
    'jumlah_pinjaman' => '10.000.000',
    'jangka_waktu' => '12',
    'jaminan' => 'BPKB Motor',
    'tahun_kendaraan' => date('Y'),
    'status_pajak' => 'Aktif',
    'kepemilikan_rumah' => '',
    'umur' => '',
    'tujuan_pinjaman' => 'Kebutuhan Pribadi'
];

// Cek apakah ada pesan error dari proses_prediksi_leasing.php
if (isset($_GET['error'])) {
    $error_message = $_GET['error'];
}

// Cek apakah ada parameter id_nasabah dari halaman lain
$selected_nasabah = isset($_GET['id_nasabah']) ? $_GET['id_nasabah'] : null;

// Tambahkan default value untuk tahun_kendaraan dan status_pajak
if (empty($form_data['tahun_kendaraan'])) {
    $form_data['tahun_kendaraan'] = '';
}
if (empty($form_data['status_pajak'])) {
    $form_data['status_pajak'] = '';
}

// Proses form jika ada submit
if (isset($_POST['submit'])) {
    // Tambahkan log untuk debugging
    error_log("Form submitted: " . print_r($_POST, true));

    // Ambil data dari form
    $form_data = [
        'nama_nasabah' => $_POST['nama_nasabah'] ?? '',
        'jenis_kelamin' => $_POST['jenis_kelamin'] ?? '',
        'status_perkawinan' => $_POST['status_perkawinan'] ?? '',
        'pekerjaan' => $_POST['pekerjaan'] ?? '',
        'penghasilan' => $_POST['penghasilan'] ?? '',
        'jumlah_tanggungan' => $_POST['jumlah_tanggungan'] ?? '',
        'jumlah_pinjaman' => $_POST['jumlah_pinjaman'] ?? '',
        'jangka_waktu' => $_POST['jangka_waktu'] ?? '',
        'jaminan' => $_POST['jaminan'] ?? 'BPKB Motor',
        'tahun_kendaraan' => $_POST['tahun_kendaraan'] ?? (date('Y')),
        'status_pajak' => $_POST['status_pajak'] ?? '',
        'kepemilikan_rumah' => $_POST['kepemilikan_rumah'] ?? '',
        'umur' => $_POST['umur'] ?? '',
        'tujuan_pinjaman' => $_POST['tujuan_pinjaman'] ?? ''
    ];

    // Validasi input
    if (empty($form_data['nama_nasabah'])) {
        $error_message = "Nama nasabah harus diisi!";
    } elseif (empty($form_data['penghasilan'])) {
        $error_message = "Penghasilan harus diisi!";
    } elseif (empty($form_data['jumlah_tanggungan']) || $form_data['jumlah_tanggungan'] < 0) {
        $error_message = "Jumlah tanggungan harus diisi dengan nilai minimal 0!";
    } elseif (empty($form_data['jumlah_pinjaman'])) {
        $error_message = "Jumlah pinjaman harus diisi!";
    } else {
        // Validasi data untuk tahun kendaraan dan status pajak
        // Pastikan tahun kendaraan diisi dan valid
        if (empty($form_data['tahun_kendaraan'])) {
            $error_message = "Tahun kendaraan harus diisi!";
        }
        elseif ($form_data['tahun_kendaraan'] < 1990 || $form_data['tahun_kendaraan'] > date('Y')) {
            $error_message = "Tahun kendaraan tidak valid! Harus antara 1990 dan " . date('Y');
        }
        // Pastikan status pajak diisi
        elseif (empty($form_data['status_pajak'])) {
            $error_message = "Status pajak kendaraan harus diisi!";
        }

        // Jika tidak ada error message, lanjutkan proses
        if (empty($error_message)) {
            // Simpan data nasabah ke database
            $sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, pekerjaan, penghasilan,
                    jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak,
                    kepemilikan_rumah, umur, tujuan_pinjaman, sumber_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'manual')";

            // Gunakan tujuan pinjaman dari form jika diisi, jika tidak set default
            $tujuan_pinjaman = !empty($form_data['tujuan_pinjaman']) ? $form_data['tujuan_pinjaman'] : 'Kebutuhan Pribadi';

            // Konversi nilai numerik ke tipe yang sesuai
            // Hapus titik sebagai pemisah ribuan pada penghasilan dan jumlah pinjaman
            $penghasilan = (float)str_replace('.', '', $form_data['penghasilan']);
            $jumlah_tanggungan = (int)$form_data['jumlah_tanggungan'];
            $jumlah_pinjaman = (float)str_replace('.', '', $form_data['jumlah_pinjaman']);
            $jangka_waktu = (int)$form_data['jangka_waktu'];
            $umur = (int)$form_data['umur'];
            $tahun_kendaraan = !empty($form_data['tahun_kendaraan']) ? $form_data['tahun_kendaraan'] : null;

            // Tambahkan error handling untuk debugging
            error_log("SQL Query: " . $sql);

            $stmt = mysqli_prepare($koneksi, $sql);
            if (!$stmt) {
                error_log("Error preparing statement: " . mysqli_error($koneksi));
                $error_message = "Gagal menyiapkan query: " . mysqli_error($koneksi);
            } else {
                mysqli_stmt_bind_param($stmt, "ssssdidissssis",
                    $form_data['nama_nasabah'],
                    $form_data['jenis_kelamin'],
                    $form_data['status_perkawinan'],
                    $form_data['pekerjaan'],
                    $penghasilan,
                    $jumlah_tanggungan,
                    $jumlah_pinjaman,
                    $jangka_waktu,
                    $form_data['jaminan'],
                    $tahun_kendaraan,
                    $form_data['status_pajak'],
                    $form_data['kepemilikan_rumah'],
                    $umur,
                    $tujuan_pinjaman
                );
            }

            if (mysqli_stmt_execute($stmt)) {
                $id_nasabah = mysqli_insert_id($koneksi);
                error_log("Nasabah berhasil disimpan dengan ID: " . $id_nasabah);

                // Lakukan prediksi menggunakan API model Python
                error_log("Melakukan prediksi untuk nasabah ID: " . $id_nasabah);

                // Ambil data nasabah
                $query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
                $stmt_nasabah = mysqli_prepare($koneksi, $query_nasabah);
                mysqli_stmt_bind_param($stmt_nasabah, "i", $id_nasabah);
                mysqli_stmt_execute($stmt_nasabah);
                $result_nasabah = mysqli_stmt_get_result($stmt_nasabah);
                $nasabah_data = mysqli_fetch_assoc($result_nasabah);

                if ($nasabah_data) {
                    try {
                        error_log("Data nasabah ditemukan: " . print_r($nasabah_data, true));

                        // Gunakan api_predict.php untuk melakukan prediksi
                        require_once 'api_predict.php';

                        error_log("Memulai prediksi dengan backpropagation...");

                        // Lakukan prediksi dengan model backpropagation
                        $hasil_prediksi = prediksi_dengan_backpropagation($nasabah_data);

                        error_log("Hasil prediksi: " . print_r($hasil_prediksi, true));

                        // Validasi hasil prediksi
                        if (!$hasil_prediksi || !isset($hasil_prediksi['hasil_prediksi'])) {
                            throw new Exception("Hasil prediksi tidak valid atau kosong");
                        }

                        // Simpan hasil prediksi ke database
                        $id_prediksi = simpan_hasil_prediksi($nasabah_data, $hasil_prediksi);

                        error_log("ID prediksi yang disimpan: " . $id_prediksi);

                        if ($id_prediksi) {
                            // Pastikan konsistensi data
                            pastikan_konsistensi_prediksi($id_nasabah);

                            // Verifikasi data tersimpan dengan benar
                            $verify_query = "SELECT * FROM hasil_prediksi WHERE id_nasabah = ? ORDER BY tanggal_prediksi DESC LIMIT 1";
                            $verify_stmt = mysqli_prepare($koneksi, $verify_query);
                            mysqli_stmt_bind_param($verify_stmt, "i", $id_nasabah);
                            mysqli_stmt_execute($verify_stmt);
                            $verify_result = mysqli_stmt_get_result($verify_stmt);
                            $verify_data = mysqli_fetch_assoc($verify_result);
                            mysqli_stmt_close($verify_stmt);

                            // Update id_prediksi dengan yang benar dari database
                            if ($verify_data) {
                                $id_prediksi = $verify_data['id_prediksi'];
                            }

                            if ($verify_data) {
                                error_log("Verifikasi berhasil: Data prediksi tersimpan dengan ID " . $id_prediksi);
                            } else {
                                error_log("WARNING: Data prediksi tidak ditemukan setelah penyimpanan!");
                            }

                            $success_message = "Data nasabah berhasil disimpan dan prediksi berhasil dilakukan.";
                            error_log("Prediksi berhasil, menampilkan hasil di halaman yang sama");

                            // Set flag untuk menampilkan hasil prediksi
                            $show_prediction_result = true;
                            $prediction_result = [
                                'id_nasabah' => $id_nasabah,
                                'id_prediksi' => $id_prediksi,
                                'nama_nasabah' => $nasabah_data['nama_nasabah'],
                                'hasil_prediksi' => $hasil_prediksi['hasil_prediksi'],
                                'probabilitas' => $hasil_prediksi['probabilitas'],
                                'keterangan' => $hasil_prediksi['keterangan'],
                                'penghasilan' => $nasabah_data['penghasilan'],
                                'jumlah_pinjaman' => $nasabah_data['jumlah_pinjaman'],
                                'jangka_waktu' => $nasabah_data['jangka_waktu']
                            ];

                            // Reset form data untuk input baru
                            $form_data = [
                                'nama_nasabah' => '',
                                'jenis_kelamin' => 'Laki-laki',
                                'status_perkawinan' => 'Menikah',
                                'pekerjaan' => 'Swasta',
                                'penghasilan' => '5.000.000',
                                'jumlah_tanggungan' => '',
                                'jumlah_pinjaman' => '10.000.000',
                                'jangka_waktu' => '12',
                                'jaminan' => 'BPKB Motor',
                                'tahun_kendaraan' => date('Y'),
                                'status_pajak' => 'Aktif',
                                'kepemilikan_rumah' => '',
                                'umur' => '',
                                'tujuan_pinjaman' => 'Kebutuhan Pribadi'
                            ];
                        } else {
                            $error_message = "Gagal menyimpan hasil prediksi.";
                            error_log("Gagal menyimpan hasil prediksi");
                        }
                    } catch (Exception $e) {
                        $error_message = "Error saat melakukan prediksi: " . $e->getMessage();
                        error_log("Error prediksi: " . $e->getMessage());
                        error_log("Stack trace: " . $e->getTraceAsString());
                    }
                } else {
                    $error_message = "Data nasabah tidak ditemukan.";
                    error_log("Data nasabah tidak ditemukan setelah insert");
                }
            } else {
                $error_message = "Gagal menyimpan data nasabah: " . mysqli_error($koneksi);
            }
        }
    }
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Prediksi Kelayakan Kredit Nasabah Baru</h1>
            </div>
        </div>

        <!-- Tampilkan pesan error jika ada -->
        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Tampilkan pesan sukses jika ada -->
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <!-- Tampilkan hasil prediksi jika ada -->
        <?php if ($show_prediction_result && $prediction_result): ?>
        <div class="panel panel-success">
            <div class="panel-heading">
                <h4><i class="fa fa-check-circle"></i> Hasil Prediksi Kelayakan Kredit</h4>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5><strong>Data Nasabah:</strong></h5>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Nama:</strong></td>
                                <td><?php echo htmlspecialchars($prediction_result['nama_nasabah']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Penghasilan:</strong></td>
                                <td>Rp <?php echo number_format($prediction_result['penghasilan'], 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Jumlah Pinjaman:</strong></td>
                                <td>Rp <?php echo number_format($prediction_result['jumlah_pinjaman'], 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Jangka Waktu:</strong></td>
                                <td><?php echo $prediction_result['jangka_waktu']; ?> bulan</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5><strong>Hasil Prediksi:</strong></h5>
                        <div class="text-center" style="margin: 20px 0;">
                            <?php if ($prediction_result['hasil_prediksi'] == 'Layak'): ?>
                                <div class="alert alert-success" style="font-size: 18px; font-weight: bold;">
                                    <i class="fa fa-thumbs-up fa-2x"></i><br>
                                    LAYAK MENERIMA KREDIT
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-success" role="progressbar"
                                         style="width: <?php echo ($prediction_result['probabilitas'] * 100); ?>%">
                                        <?php echo number_format($prediction_result['probabilitas'] * 100, 1); ?>%
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger" style="font-size: 18px; font-weight: bold;">
                                    <i class="fa fa-thumbs-down fa-2x"></i><br>
                                    TIDAK LAYAK MENERIMA KREDIT
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-danger" role="progressbar"
                                         style="width: <?php echo ((1 - $prediction_result['probabilitas']) * 100); ?>%">
                                        <?php echo number_format((1 - $prediction_result['probabilitas']) * 100, 1); ?>%
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <h5><strong>Keterangan Detail:</strong></h5>
                        <div class="well">
                            <?php echo nl2br(htmlspecialchars($prediction_result['keterangan'])); ?>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 text-center">
                        <a href="hasil_prediksi.php?id_nasabah=<?php echo $prediction_result['id_nasabah']; ?>&id_prediksi=<?php echo $prediction_result['id_prediksi']; ?>"
                           class="btn btn-info">
                            <i class="fa fa-eye"></i> Lihat Detail Lengkap
                        </a>
                        <a href="daftar_kelayakan.php" class="btn btn-success">
                            <i class="fa fa-list"></i> Lihat Daftar Kelayakan
                        </a>
                        <button type="button" class="btn btn-primary" onclick="scrollToForm()">
                            <i class="fa fa-plus"></i> Prediksi Nasabah Lain
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="panel panel-default" id="form-prediksi">
            <div class="panel-heading">
                <h4>
                    <?php if ($show_prediction_result): ?>
                        Form Prediksi Nasabah Baru
                    <?php else: ?>
                        Form Tambah Nasabah dan Prediksi Kelayakan
                    <?php endif; ?>
                </h4>
            </div>
            <div class="panel-body">
                <form method="post" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nama Nasabah *</label>
                                <input type="text" name="nama_nasabah" class="form-control"
                                       value="<?php echo htmlspecialchars($form_data['nama_nasabah']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label>Jenis Kelamin *</label>
                                <select name="jenis_kelamin" class="form-control" required>
                                    <option value="">-- Pilih Jenis Kelamin --</option>
                                    <option value="Laki-laki" <?php echo $form_data['jenis_kelamin'] == 'Laki-laki' ? '' : ''; ?>>Laki-laki</option>
                                    <option value="Perempuan" <?php echo $form_data['jenis_kelamin'] == 'Perempuan' ? '' : ''; ?>>Perempuan</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Status Perkawinan *</label>
                                <select name="status_perkawinan" class="form-control" required>
                                    <option value="">-- Pilih Status Perkawinan --</option>
                                    <option value="Menikah" <?php echo $form_data['status_perkawinan'] == 'Menikah' ? '' : ''; ?>>Menikah</option>
                                    <option value="Belum Menikah" <?php echo $form_data['status_perkawinan'] == 'Belum Menikah' ? '' : ''; ?>>Belum Menikah</option>
                                    <option value="Cerai" <?php echo $form_data['status_perkawinan'] == 'Cerai' ? '' : ''; ?>>Cerai</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Pekerjaan *</label>
                                <select name="pekerjaan" class="form-control" required>
                                    <option value="">-- Pilih Pekerjaan --</option>
                                    <option value="PNS" <?php echo $form_data['pekerjaan'] == 'PNS' ? '' : ''; ?>>PNS</option>
                                    <option value="Swasta" <?php echo $form_data['pekerjaan'] == 'Swasta' ? '' : ''; ?>>Swasta</option>
                                    <option value="Wiraswasta" <?php echo $form_data['pekerjaan'] == 'Wiraswasta' ? '' : ''; ?>>Wiraswasta</option>
                                    <option value="Lainnya" <?php echo $form_data['pekerjaan'] == 'Lainnya' ? '' : ''; ?>>Lainnya</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Umur *</label>
                                <input type="number" name="umur" class="form-control" min="20" max="60"
                                       value="<?php echo htmlspecialchars($form_data['umur']); ?>" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Penghasilan per Bulan (Rp) *</label>
                                <input type="text" class="form-control currency-input" id="penghasilan" name="penghasilan" required>
                            </div>

                            <div class="form-group">
                                <label>Jumlah Tanggungan *</label>
                                <input type="number" name="jumlah_tanggungan" class="form-control" min="0" max="10"
                                       value="<?php echo htmlspecialchars($form_data['jumlah_tanggungan']); ?>" required>
                                <small class="text-muted">Jumlah anggota keluarga yang menjadi tanggungan</small>
                            </div>

                            <div class="form-group">
                                <label>Jumlah Pinjaman (Rp) *</label>
                                <input type="text" class="form-control currency-input" id="jumlah_pinjaman" name="jumlah_pinjaman" required>
                            </div>

                            <div class="form-group">
                                <label>Jangka Waktu (bulan) *</label>
                                <input type="number" name="jangka_waktu" class="form-control" min="6" max="60"
                                       value="<?php echo htmlspecialchars($form_data['jangka_waktu']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label>Jaminan *</label>
                                <select name="jaminan" class="form-control" id="jaminan-select" required>
                                    <option value="">-- Pilih Jaminan --</option>
                                    <option value="BPKB Motor" <?php echo $form_data['jaminan'] == 'BPKB Motor' ? '' : ''; ?>>BPKB Motor</option>
                                    <option value="BPKB Mobil" <?php echo $form_data['jaminan'] == 'BPKB Mobil' ? '' : ''; ?>>BPKB Mobil</option>
                                </select>
                                <small class="text-muted">Jaminan hanya berupa BPKB kendaraan (mobil/motor)</small>
                            </div>

                            <div class="form-group" id="tahun-kendaraan-group">
                                <label>Tahun Kendaraan *</label>
                                <input type="number" name="tahun_kendaraan" class="form-control" min="1990" max="<?php echo date('Y'); ?>"
                                       value="<?php echo htmlspecialchars($form_data['tahun_kendaraan']); ?>" required>
                                <small class="text-muted">Tahun pembuatan kendaraan sesuai STNK</small>
                            </div>

                            <div class="form-group" id="status-pajak-group">
                                <label>Status Pajak Kendaraan *</label>
                                <select name="status_pajak" class="form-control" required>
                                    <option value="">-- Pilih Status Pajak --</option>
                                    <option value="Aktif" <?php echo $form_data['status_pajak'] == 'Aktif' ? '' : ''; ?>>Aktif</option>
                                    <option value="Tidak Aktif" <?php echo $form_data['status_pajak'] == 'Tidak Aktif' ? '' : ''; ?>>Tidak Aktif</option>
                                </select>
                                <small class="text-muted">Status pajak kendaraan saat ini</small>
                            </div>

                            <div class="form-group">
                                <label>Kepemilikan Rumah *</label>
                                <select name="kepemilikan_rumah" class="form-control" required>
                                    <option value="">-- Pilih Status Kepemilikan Rumah --</option>
                                    <option value="Milik Sendiri" <?php echo $form_data['kepemilikan_rumah'] == 'Milik Sendiri' ? '' : ''; ?>>Milik Sendiri</option>
                                    <option value="Kontrak" <?php echo $form_data['kepemilikan_rumah'] == 'Kontrak' ? '' : ''; ?>>Kontrak</option>
                                    <option value="Orang Tua" <?php echo $form_data['kepemilikan_rumah'] == 'Orang Tua' ? '' : ''; ?>>Orang Tua</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>Tujuan Pinjaman</label>
                                <textarea name="tujuan_pinjaman" class="form-control" rows="2"><?php echo htmlspecialchars($form_data['tujuan_pinjaman'] ?? ''); ?></textarea>
                                <small class="text-muted">Opsional. Jika kosong akan diisi "Kebutuhan Pribadi"</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" name="submit" class="btn btn-primary" id="submit-btn">
                            <i class="fa fa-save"></i> Simpan & Prediksi Kelayakan
                        </button>
                        <a href="data_nasabah_analis.php" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> Kembali
                        </a>
                    </div>

                    <!-- Loading indicator -->
                    <div id="loading" style="display: none; text-align: center; margin: 20px 0;">
                        <i class="fa fa-spinner fa-spin fa-2x"></i>
                        <p>Sedang memproses prediksi, mohon tunggu...</p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<script>
document.addEventListener('DOMContentLoaded', function() {
    // Pastikan field tahun kendaraan dan status pajak selalu required
    var tahunKendaraan = document.getElementsByName('tahun_kendaraan')[0];
    var statusPajak = document.getElementsByName('status_pajak')[0];

    if (tahunKendaraan) tahunKendaraan.setAttribute('required', 'required');
    if (statusPajak) statusPajak.setAttribute('required', 'required');

    // Handle form submission
    var form = document.querySelector('form');
    var submitBtn = document.getElementById('submit-btn');
    var loading = document.getElementById('loading');

    if (form && submitBtn && loading) {
        form.addEventListener('submit', function(e) {
            // Tampilkan loading indicator
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Memproses...';
            loading.style.display = 'block';

            // Scroll ke loading indicator
            loading.scrollIntoView({ behavior: 'smooth' });
        });
    }

    // Auto scroll ke hasil prediksi jika ada
    <?php if ($show_prediction_result): ?>
    setTimeout(function() {
        document.querySelector('.panel-success').scrollIntoView({ behavior: 'smooth' });
    }, 500);
    <?php endif; ?>
});

// Fungsi untuk scroll ke form
function scrollToForm() {
    document.getElementById('form-prediksi').scrollIntoView({ behavior: 'smooth' });
}
</script>

<?php
require_once 'foot.php';
?>
