<?php
    include 'koneksi.php';

    // Mulai session jika belum dimulai
    if (session_status() == PHP_SESSION_NONE) {
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
    }

    // Cek apakah yang mengakses halaman ini sudah login sebagai analis
    if(!isset($_SESSION['level']) || $_SESSION['level'] != "analis"){
        header("location:login.php?pesan=akses_ditolak");
        exit();
    }

    require_once 'nav_analis.php';

    // Error handling untuk query
    try {
        // Query untuk mendapatkan jumlah nasabah layak
        $query_layak = "SELECT COUNT(*) as total_layak FROM nasabah WHERE kelayakan = 'Layak'";
        $result_layak = $koneksi->query($query_layak) or die("Error query layak: " . $koneksi->error);
        $data_layak = $result_layak->fetch_assoc();

        // Query untuk mendapatkan jumlah nasabah tidak layak
        $query_tidak_layak = "SELECT COUNT(*) as total_tidak_layak FROM nasabah WHERE kelayakan = 'Tidak Layak'";
        $result_tidak_layak = $koneksi->query($query_tidak_layak) or die("Error query tidak layak: " . $koneksi->error);
        $data_tidak_layak = $result_tidak_layak->fetch_assoc();

        // Query untuk mendapatkan total nasabah dengan kelayakan
        $query_total = "SELECT COUNT(*) as total_nasabah FROM nasabah WHERE kelayakan IS NOT NULL";
        $result_total = $koneksi->query($query_total) or die("Error query total: " . $koneksi->error);
        $data_total = $result_total->fetch_assoc();

        // Query untuk mendapatkan total semua nasabah
        $query_all = "SELECT COUNT(*) as total_all_nasabah FROM nasabah";
        $result_all = $koneksi->query($query_all) or die("Error query all: " . $koneksi->error);
        $data_all = $result_all->fetch_assoc();

        // Hitung nasabah tanpa kelayakan
        $nasabah_tanpa_kelayakan = $data_all['total_all_nasabah'] - $data_total['total_nasabah'];

        // Hitung persentase
        $total_nasabah = $data_total['total_nasabah'] ?? 0;
        $persen_layak = $total_nasabah > 0 ? round(($data_layak['total_layak'] / $total_nasabah) * 100, 2) : 0;
        $persen_tidak_layak = $total_nasabah > 0 ? round(($data_tidak_layak['total_tidak_layak'] / $total_nasabah) * 100, 2) : 0;

        // Query untuk statistik berdasarkan jenis kelamin
        $query_gender = "SELECT jenis_kelamin, kelayakan, COUNT(*) as jumlah
                        FROM nasabah
                        WHERE kelayakan IS NOT NULL
                        GROUP BY jenis_kelamin, kelayakan
                        ORDER BY jenis_kelamin, kelayakan";
        $result_gender = $koneksi->query($query_gender) or die("Error query gender: " . $koneksi->error);
        $data_gender = [];
        while ($row = $result_gender->fetch_assoc()) {
            $data_gender[] = $row;
        }

        // Query untuk statistik berdasarkan pekerjaan
        $query_pekerjaan = "SELECT pekerjaan, kelayakan, COUNT(*) as jumlah
                            FROM nasabah
                            WHERE kelayakan IS NOT NULL
                            GROUP BY pekerjaan, kelayakan
                            ORDER BY pekerjaan, kelayakan";
        $result_pekerjaan = $koneksi->query($query_pekerjaan) or die("Error query pekerjaan: " . $koneksi->error);
        $data_pekerjaan = [];
        while ($row = $result_pekerjaan->fetch_assoc()) {
            $data_pekerjaan[] = $row;
        }

        // Query untuk statistik berdasarkan jaminan
        $query_jaminan = "SELECT jaminan, kelayakan, COUNT(*) as jumlah
                          FROM nasabah
                          WHERE kelayakan IS NOT NULL
                          GROUP BY jaminan, kelayakan
                          ORDER BY jaminan, kelayakan";
        $result_jaminan = $koneksi->query($query_jaminan) or die("Error query jaminan: " . $koneksi->error);
        $data_jaminan = [];
        while ($row = $result_jaminan->fetch_assoc()) {
            $data_jaminan[] = $row;
        }

    } catch (Exception $e) {
        die("Error: " . $e->getMessage());
    }
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Analis</title>
</head>
<body>
    <div id="wrapper">
        <?php require_once 'nav_analis.php'; ?>

        <div id="page-wrapper">
            <div class="container-fluid">
                <div class="row">
                   <div class="col-lg-12">
                        <h1 class="page-header">Selamat Datang, <?php echo htmlspecialchars($_SESSION['username'] ?? 'Pengguna'); ?></h1>
                    </div>
                </div>

                <!-- Bagian dashboard cards -->
                <div class="row">
                    <!-- Kolom Nasabah Layak -->
                    <div class="col-md-4">
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-check-circle fa-4x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge"><?php echo $data_layak['total_layak'] ?? 0; ?></div>
                                        <div>Nasabah Layak</div>
                                    </div>
                                </div>
                            </div>
                            <a href="daftar_nasabah_layak.php">
                                <div class="panel-footer">
                                    <span class="pull-left">Lihat Detail</span>
                                    <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                    <div class="clearfix"></div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Kolom Nasabah Tidak Layak -->
                    <div class="col-md-4">
                        <div class="panel panel-danger">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-times-circle fa-4x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge"><?php echo $data_tidak_layak['total_tidak_layak'] ?? 0; ?></div>
                                        <div>Nasabah Tidak Layak</div>
                                    </div>
                                </div>
                            </div>
                            <a href="daftar_nasabah_tidak_layak.php">
                                <div class="panel-footer">
                                    <span class="pull-left">Lihat Detail</span>
                                    <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                    <div class="clearfix"></div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Kolom Total Nasabah dengan Kelayakan -->
                    <div class="col-md-4">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-users fa-4x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge"><?php echo $data_total['total_nasabah'] ?? 0; ?></div>
                                        <div>Nasabah dengan Kelayakan</div>
                                    </div>
                                </div>
                            </div>
                            <a href="daftar_kelayakan.php">
                                <div class="panel-footer">
                                    <span class="pull-left">Lihat Detail</span>
                                    <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                    <div class="clearfix"></div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Baris tambahan untuk informasi semua nasabah -->
                <div class="row">
                    <!-- Kolom Total Semua Nasabah -->
                    <div class="col-md-6">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-database fa-4x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge"><?php echo $data_all['total_all_nasabah'] ?? 0; ?></div>
                                        <div>Total Semua Nasabah</div>
                                    </div>
                                </div>
                            </div>
                            <a href="data_nasabah_analis.php">
                                <div class="panel-footer">
                                    <span class="pull-left">Lihat Detail</span>
                                    <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                    <div class="clearfix"></div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Kolom Nasabah Tanpa Kelayakan -->
                    <div class="col-md-6">
                        <div class="panel panel-warning">
                            <div class="panel-heading">
                                <div class="row">
                                    <div class="col-xs-3">
                                        <i class="fa fa-question-circle fa-4x"></i>
                                    </div>
                                    <div class="col-xs-9 text-right">
                                        <div class="huge"><?php echo $nasabah_tanpa_kelayakan; ?></div>
                                        <div>Nasabah Tanpa Kelayakan</div>
                                    </div>
                                </div>
                            </div>
                            <a href="data_nasabah_analis.php">
                                <div class="panel-footer">
                                    <span class="pull-left">Lihat Detail</span>
                                    <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                    <div class="clearfix"></div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Grafik dan Statistik -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <i class="fa fa-pie-chart fa-fw"></i> Persentase Kelayakan Nasabah
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div id="kelayakan-chart" style="height: 250px;"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Status</th>
                                                        <th>Jumlah</th>
                                                        <th>Persentase</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr class="success">
                                                        <td>Layak</td>
                                                        <td><?php echo $data_layak['total_layak'] ?? 0; ?></td>
                                                        <td><?php echo $persen_layak; ?>%</td>
                                                    </tr>
                                                    <tr class="danger">
                                                        <td>Tidak Layak</td>
                                                        <td><?php echo $data_tidak_layak['total_tidak_layak'] ?? 0; ?></td>
                                                        <td><?php echo $persen_tidak_layak; ?>%</td>
                                                    </tr>
                                                    <tr class="info">
                                                        <td><strong>Total Nasabah dengan Kelayakan</strong></td>
                                                        <td><strong><?php echo $data_total['total_nasabah'] ?? 0; ?></strong></td>
                                                        <td><strong>100%</strong></td>
                                                    </tr>
                                                    <tr class="active">
                                                        <td><strong>Total Semua Nasabah</strong></td>
                                                        <td><strong><?php echo $data_all['total_all_nasabah'] ?? 0; ?></strong></td>
                                                        <td><strong>-</strong></td>
                                                    </tr>
                                                    <tr class="warning">
                                                        <td><strong>Nasabah Tanpa Kelayakan</strong></td>
                                                        <td><strong><?php echo $nasabah_tanpa_kelayakan; ?></strong></td>
                                                        <td><strong>-</strong></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: <?php echo $persen_layak; ?>%">
                                                <?php echo $persen_layak; ?>% Layak
                                            </div>
                                            <div class="progress-bar progress-bar-danger" style="width: <?php echo $persen_tidak_layak; ?>%">
                                                <?php echo $persen_tidak_layak; ?>% Tidak Layak
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistik Berdasarkan Kategori -->
                <div class="row">
                    <!-- Statistik Berdasarkan Jenis Kelamin -->
                    <div class="col-lg-4">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <i class="fa fa-venus-mars fa-fw"></i> Berdasarkan Jenis Kelamin
                            </div>
                            <div class="panel-body">
                                <div id="gender-chart" style="height: 200px;"></div>
                                <div class="table-responsive" style="margin-top: 15px;">
                                    <table class="table table-bordered table-hover table-striped">
                                        <thead>
                                            <tr>
                                                <th>Jenis Kelamin</th>
                                                <th>Layak</th>
                                                <th>Tidak Layak</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $gender_data = [];
                                            foreach ($data_gender as $row) {
                                                if (!isset($gender_data[$row['jenis_kelamin']])) {
                                                    $gender_data[$row['jenis_kelamin']] = [
                                                        'Layak' => 0,
                                                        'Tidak Layak' => 0
                                                    ];
                                                }
                                                $gender_data[$row['jenis_kelamin']][$row['kelayakan']] = $row['jumlah'];
                                            }

                                            foreach ($gender_data as $gender => $counts) {
                                                echo "<tr>";
                                                echo "<td>" . htmlspecialchars($gender) . "</td>";
                                                echo "<td>" . ($counts['Layak'] ?? 0) . "</td>";
                                                echo "<td>" . ($counts['Tidak Layak'] ?? 0) . "</td>";
                                                echo "</tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistik Berdasarkan Pekerjaan -->
                    <div class="col-lg-4">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <i class="fa fa-briefcase fa-fw"></i> Berdasarkan Pekerjaan
                            </div>
                            <div class="panel-body">
                                <div id="pekerjaan-chart" style="height: 200px;"></div>
                                <div class="table-responsive" style="margin-top: 15px;">
                                    <table class="table table-bordered table-hover table-striped">
                                        <thead>
                                            <tr>
                                                <th>Pekerjaan</th>
                                                <th>Layak</th>
                                                <th>Tidak Layak</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $pekerjaan_data = [];
                                            foreach ($data_pekerjaan as $row) {
                                                if (!isset($pekerjaan_data[$row['pekerjaan']])) {
                                                    $pekerjaan_data[$row['pekerjaan']] = [
                                                        'Layak' => 0,
                                                        'Tidak Layak' => 0
                                                    ];
                                                }
                                                $pekerjaan_data[$row['pekerjaan']][$row['kelayakan']] = $row['jumlah'];
                                            }

                                            foreach ($pekerjaan_data as $pekerjaan => $counts) {
                                                echo "<tr>";
                                                echo "<td>" . htmlspecialchars($pekerjaan) . "</td>";
                                                echo "<td>" . ($counts['Layak'] ?? 0) . "</td>";
                                                echo "<td>" . ($counts['Tidak Layak'] ?? 0) . "</td>";
                                                echo "</tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statistik Berdasarkan Jaminan -->
                    <div class="col-lg-4">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <i class="fa fa-shield fa-fw"></i> Berdasarkan Jaminan
                            </div>
                            <div class="panel-body">
                                <div id="jaminan-chart" style="height: 200px;"></div>
                                <div class="table-responsive" style="margin-top: 15px;">
                                    <table class="table table-bordered table-hover table-striped">
                                        <thead>
                                            <tr>
                                                <th>Jaminan</th>
                                                <th>Layak</th>
                                                <th>Tidak Layak</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $jaminan_data = [];
                                            foreach ($data_jaminan as $row) {
                                                if (!isset($jaminan_data[$row['jaminan']])) {
                                                    $jaminan_data[$row['jaminan']] = [
                                                        'Layak' => 0,
                                                        'Tidak Layak' => 0
                                                    ];
                                                }
                                                $jaminan_data[$row['jaminan']][$row['kelayakan']] = $row['jumlah'];
                                            }

                                            foreach ($jaminan_data as $jaminan => $counts) {
                                                echo "<tr>";
                                                echo "<td>" . htmlspecialchars($jaminan) . "</td>";
                                                echo "<td>" . ($counts['Layak'] ?? 0) . "</td>";
                                                echo "<td>" . ($counts['Tidak Layak'] ?? 0) . "</td>";
                                                echo "</tr>";
                                            }
                                            ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/raphael.min.js"></script>
    <script src="assets/js/morris.min.js"></script>

    <script>
    $(document).ready(function() {
        // Data untuk grafik kelayakan
        var kelayakanData = [
            {label: "Layak", value: <?php echo $data_layak['total_layak'] ?? 0; ?>},
            {label: "Tidak Layak", value: <?php echo $data_tidak_layak['total_tidak_layak'] ?? 0; ?>}
        ];

        // Inisialisasi grafik setelah dokumen siap
        Morris.Donut({
            element: 'kelayakan-chart',
            data: kelayakanData,
            colors: ['#5cb85c', '#d9534f'],
            resize: true,
            redraw: true
        });

        // Data untuk grafik jenis kelamin
        var genderData = [
            <?php
            foreach ($gender_data as $gender => $counts) {
                echo "{gender: '" . addslashes($gender) . "', layak: " . ($counts['Layak'] ?? 0) . ", tidak_layak: " . ($counts['Tidak Layak'] ?? 0) . "},\n";
            }
            ?>
        ];

        Morris.Bar({
            element: 'gender-chart',
            data: genderData,
            xkey: 'gender',
            ykeys: ['layak', 'tidak_layak'],
            labels: ['Layak', 'Tidak Layak'],
            barColors: ['#5cb85c', '#d9534f'],
            hideHover: 'auto',
            resize: true
        });

        // Data untuk grafik pekerjaan
        var pekerjaanData = [
            <?php
            foreach ($pekerjaan_data as $pekerjaan => $counts) {
                echo "{pekerjaan: '" . addslashes($pekerjaan) . "', layak: " . ($counts['Layak'] ?? 0) . ", tidak_layak: " . ($counts['Tidak Layak'] ?? 0) . "},\n";
            }
            ?>
        ];

        Morris.Bar({
            element: 'pekerjaan-chart',
            data: pekerjaanData,
            xkey: 'pekerjaan',
            ykeys: ['layak', 'tidak_layak'],
            labels: ['Layak', 'Tidak Layak'],
            barColors: ['#5cb85c', '#d9534f'],
            hideHover: 'auto',
            resize: true
        });

        // Data untuk grafik jaminan
        var jaminanData = [
            <?php
            foreach ($jaminan_data as $jaminan => $counts) {
                echo "{jaminan: '" . addslashes($jaminan) . "', layak: " . ($counts['Layak'] ?? 0) . ", tidak_layak: " . ($counts['Tidak Layak'] ?? 0) . "},\n";
            }
            ?>
        ];

        Morris.Bar({
            element: 'jaminan-chart',
            data: jaminanData,
            xkey: 'jaminan',
            ykeys: ['layak', 'tidak_layak'],
            labels: ['Layak', 'Tidak Layak'],
            barColors: ['#5cb85c', '#d9534f'],
            hideHover: 'auto',
            resize: true
        });
    });
    </script>

    <?php require_once 'foot.php'; ?>
</body>
</html>