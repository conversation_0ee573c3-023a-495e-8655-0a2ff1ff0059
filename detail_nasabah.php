<?php
include 'koneksi.php';
include 'cek_session.php';
cek_akses(['admin', 'analis']);

$id_nasabah = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id_nasabah <= 0) {
    echo '<div class="alert alert-danger">ID Nasabah tidak valid</div>';
    exit;
}

// Ambil data nasabah
$sql_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
$stmt = mysqli_prepare($koneksi, $sql_nasabah);
mysqli_stmt_bind_param($stmt, "i", $id_nasabah);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$nasabah = mysqli_fetch_assoc($result);

if (!$nasabah) {
    echo '<div class="alert alert-danger">Data nasabah tidak ditemukan</div>';
    exit;
}

// Ambil data prediksi
$sql_prediksi = "SELECT * FROM hasil_prediksi WHERE id_nasabah = ? ORDER BY tanggal_prediksi DESC LIMIT 1";
$stmt_prediksi = mysqli_prepare($koneksi, $sql_prediksi);
mysqli_stmt_bind_param($stmt_prediksi, "i", $id_nasabah);
mysqli_stmt_execute($stmt_prediksi);
$result_prediksi = mysqli_stmt_get_result($stmt_prediksi);
$prediksi = mysqli_fetch_assoc($result_prediksi);

// Hitung rasio dan analisis
$penghasilan_per_kapita = $nasabah['penghasilan'] / max(1, $nasabah['jumlah_tanggungan']);
$rasio_pinjaman = ($nasabah['jumlah_pinjaman'] / $nasabah['penghasilan']) * 100;
?>

<div class="row">
    <div class="col-md-6">
        <h4><i class="fa fa-user"></i> Data Pribadi</h4>
        <table class="table table-bordered">
            <tr>
                <td><strong>Nama</strong></td>
                <td><?php echo htmlspecialchars($nasabah['nama_nasabah']); ?></td>
            </tr>
            <tr>
                <td><strong>Umur</strong></td>
                <td><?php echo $nasabah['umur']; ?> tahun</td>
            </tr>
            <tr>
                <td><strong>Jenis Kelamin</strong></td>
                <td><?php echo $nasabah['jenis_kelamin']; ?></td>
            </tr>
            <tr>
                <td><strong>Status Perkawinan</strong></td>
                <td><?php echo $nasabah['status_perkawinan']; ?></td>
            </tr>
            <tr>
                <td><strong>Pekerjaan</strong></td>
                <td><?php echo $nasabah['pekerjaan']; ?></td>
            </tr>
            <tr>
                <td><strong>Jumlah Tanggungan</strong></td>
                <td><?php echo $nasabah['jumlah_tanggungan']; ?> orang</td>
            </tr>
        </table>
    </div>
    
    <div class="col-md-6">
        <h4><i class="fa fa-money"></i> Data Finansial</h4>
        <table class="table table-bordered">
            <tr>
                <td><strong>Penghasilan</strong></td>
                <td>Rp <?php echo number_format($nasabah['penghasilan'], 0, ',', '.'); ?></td>
            </tr>
            <tr>
                <td><strong>Penghasilan per Kapita</strong></td>
                <td>Rp <?php echo number_format($penghasilan_per_kapita, 0, ',', '.'); ?></td>
            </tr>
            <tr>
                <td><strong>Jumlah Pinjaman</strong></td>
                <td>Rp <?php echo number_format($nasabah['jumlah_pinjaman'], 0, ',', '.'); ?></td>
            </tr>
            <tr>
                <td><strong>Jangka Waktu</strong></td>
                <td><?php echo $nasabah['jangka_waktu']; ?> bulan</td>
            </tr>
            <tr>
                <td><strong>Rasio Pinjaman</strong></td>
                <td><?php echo number_format($rasio_pinjaman, 1); ?>% dari penghasilan</td>
            </tr>
            <tr>
                <td><strong>Kepemilikan Rumah</strong></td>
                <td><?php echo $nasabah['kepemilikan_rumah']; ?></td>
            </tr>
        </table>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <h4><i class="fa fa-car"></i> Data Jaminan</h4>
        <table class="table table-bordered">
            <tr>
                <td><strong>Jenis Jaminan</strong></td>
                <td><?php echo $nasabah['jaminan']; ?></td>
            </tr>
            <tr>
                <td><strong>Tahun Kendaraan</strong></td>
                <td><?php echo $nasabah['tahun_kendaraan']; ?></td>
            </tr>
            <tr>
                <td><strong>Status Pajak</strong></td>
                <td>
                    <span class="label label-<?php echo $nasabah['status_pajak'] == 'Aktif' ? 'success' : 'warning'; ?>">
                        <?php echo $nasabah['status_pajak']; ?>
                    </span>
                </td>
            </tr>
            <tr>
                <td><strong>Tujuan Pinjaman</strong></td>
                <td><?php echo htmlspecialchars($nasabah['tujuan_pinjaman'] ?: 'Kebutuhan Pribadi'); ?></td>
            </tr>
        </table>
    </div>
    
    <div class="col-md-6">
        <h4><i class="fa fa-chart-line"></i> Hasil Prediksi</h4>
        <?php if ($prediksi): ?>
        <table class="table table-bordered">
            <tr>
                <td><strong>Status Kelayakan</strong></td>
                <td>
                    <span class="label label-<?php echo $prediksi['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?> label-lg">
                        <?php echo $prediksi['hasil_prediksi']; ?>
                    </span>
                </td>
            </tr>
            <tr>
                <td><strong>Probabilitas</strong></td>
                <td><?php echo number_format($prediksi['probabilitas'] * 100, 1); ?>%</td>
            </tr>
            <tr>
                <td><strong>Tanggal Prediksi</strong></td>
                <td><?php echo date('d/m/Y H:i', strtotime($prediksi['tanggal_prediksi'])); ?></td>
            </tr>
        </table>
        
        <div class="alert alert-info">
            <strong>Keterangan:</strong><br>
            <?php echo nl2br(htmlspecialchars($prediksi['keterangan'])); ?>
        </div>
        <?php else: ?>
        <div class="alert alert-warning">
            <i class="fa fa-exclamation-triangle"></i> Belum ada data prediksi untuk nasabah ini.
        </div>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <h4><i class="fa fa-analytics"></i> Analisis Kelayakan</h4>
        <div class="panel panel-default">
            <div class="panel-body">
                <?php
                // Analisis faktor-faktor kelayakan
                $faktor_positif = [];
                $faktor_negatif = [];
                
                // Analisis penghasilan per kapita
                if ($penghasilan_per_kapita >= 5000000) {
                    $faktor_positif[] = "Penghasilan per kapita tinggi (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . ")";
                } elseif ($penghasilan_per_kapita >= 2000000) {
                    $faktor_positif[] = "Penghasilan per kapita cukup (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . ")";
                } else {
                    $faktor_negatif[] = "Penghasilan per kapita rendah (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . ")";
                }
                
                // Analisis rasio pinjaman
                if ($rasio_pinjaman <= 300) {
                    $faktor_positif[] = "Rasio pinjaman terhadap penghasilan rendah (" . number_format($rasio_pinjaman, 1) . "%)";
                } elseif ($rasio_pinjaman <= 500) {
                    $faktor_positif[] = "Rasio pinjaman terhadap penghasilan moderat (" . number_format($rasio_pinjaman, 1) . "%)";
                } else {
                    $faktor_negatif[] = "Rasio pinjaman terhadap penghasilan tinggi (" . number_format($rasio_pinjaman, 1) . "%)";
                }
                
                // Analisis pekerjaan
                if (in_array($nasabah['pekerjaan'], ['PNS', 'Profesional'])) {
                    $faktor_positif[] = "Pekerjaan stabil (" . $nasabah['pekerjaan'] . ")";
                } elseif ($nasabah['pekerjaan'] == 'Swasta') {
                    $faktor_positif[] = "Pekerjaan di sektor swasta";
                } else {
                    $faktor_negatif[] = "Pekerjaan kurang stabil (" . $nasabah['pekerjaan'] . ")";
                }
                
                // Analisis jaminan
                if ($nasabah['jaminan'] == 'BPKB Mobil') {
                    $faktor_positif[] = "Jaminan bernilai tinggi (BPKB Mobil)";
                } else {
                    $faktor_negatif[] = "Jaminan bernilai sedang (BPKB Motor)";
                }
                
                // Analisis kepemilikan rumah
                if ($nasabah['kepemilikan_rumah'] == 'Milik Sendiri') {
                    $faktor_positif[] = "Memiliki rumah sendiri";
                } else {
                    $faktor_negatif[] = "Tidak memiliki rumah sendiri (" . $nasabah['kepemilikan_rumah'] . ")";
                }
                
                // Analisis status pajak
                if ($nasabah['status_pajak'] == 'Aktif') {
                    $faktor_positif[] = "Status pajak kendaraan aktif";
                } else {
                    $faktor_negatif[] = "Status pajak kendaraan tidak aktif";
                }
                ?>
                
                <?php if (!empty($faktor_positif)): ?>
                <div class="alert alert-success">
                    <strong><i class="fa fa-check"></i> Faktor Positif:</strong>
                    <ul class="mb-0">
                        <?php foreach ($faktor_positif as $faktor): ?>
                        <li><?php echo $faktor; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($faktor_negatif)): ?>
                <div class="alert alert-warning">
                    <strong><i class="fa fa-exclamation-triangle"></i> Faktor yang Perlu Diperhatikan:</strong>
                    <ul class="mb-0">
                        <?php foreach ($faktor_negatif as $faktor): ?>
                        <li><?php echo $faktor; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.label-lg {
    font-size: 14px;
    padding: 8px 12px;
}
</style>
