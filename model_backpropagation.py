#!/usr/bin/env python
# coding: utf-8

# # Model Backpropagation untuk Prediksi Kelayakan Kredit
# 
# Model ini menggunakan dataset nasabah untuk memprediksi kelayakan kredit dengan algoritma backpropagation neural network.

# ## Import Library yang Diperlukan

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score, precision_score, recall_score, f1_score
import pickle
import os

# Mengatur seed untuk reproduksibilitas
np.random.seed(42)

# ## Load Dataset

# Membaca dataset
try:
    df = pd.read_csv('dataset_nasabah.csv')
    print(f"Dataset berhasil dimuat dengan {df.shape[0]} baris dan {df.shape[1]} kolom")
except Exception as e:
    print(f"Error saat memuat dataset: {e}")
    # Jika file tidak ditemukan, coba cari di lokasi lain
    try:
        df = pd.read_csv('data/dataset_nasabah.csv')
        print(f"Dataset berhasil dimuat dari folder data/ dengan {df.shape[0]} baris dan {df.shape[1]} kolom")
    except Exception as e2:
        print(f"Error saat memuat dataset dari folder data/: {e2}")
        raise Exception("Dataset tidak ditemukan. Pastikan file dataset_nasabah.csv ada di direktori yang benar.")

# ## Eksplorasi Data

# Menampilkan informasi dataset
print("\nInformasi Dataset:")
print(df.info())

# Menampilkan statistik deskriptif
print("\nStatistik Deskriptif:")
print(df.describe())

# Memeriksa nilai yang hilang
print("\nNilai yang hilang:")
print(df.isnull().sum())

# Memeriksa distribusi kelas target (kelayakan)
print("\nDistribusi Kelas Target (Kelayakan):")
print(df['kelayakan'].value_counts())
print(df['kelayakan'].value_counts(normalize=True) * 100)

# Visualisasi distribusi kelas target
plt.figure(figsize=(8, 6))
sns.countplot(x='kelayakan', data=df)
plt.title('Distribusi Kelas Target (Kelayakan)')
plt.savefig('distribusi_kelayakan.png')
plt.close()

# ## Preprocessing Data

# Memisahkan fitur dan target
X = df.drop('kelayakan', axis=1)
y = df['kelayakan']

# Mengidentifikasi kolom numerik dan kategorikal
numeric_features = X.select_dtypes(include=['int64', 'float64']).columns.tolist()
categorical_features = X.select_dtypes(include=['object']).columns.tolist()

print("\nFitur Numerik:", numeric_features)
print("Fitur Kategorikal:", categorical_features)

# Membuat preprocessor untuk transformasi data
preprocessor = ColumnTransformer(
    transformers=[
        ('num', StandardScaler(), numeric_features),
        ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
    ])

# ## Fungsi untuk Evaluasi Model dengan Berbagai Skenario Split Data

def evaluate_model(X, y, test_size, random_state=42):
    """
    Evaluasi model dengan berbagai skenario pembagian data
    
    Parameters:
    -----------
    X : DataFrame
        Fitur input
    y : Series
        Target output
    test_size : float
        Proporsi data testing (0.0 - 1.0)
    random_state : int
        Seed untuk reproduksibilitas
        
    Returns:
    --------
    dict
        Dictionary berisi hasil evaluasi model
    """
    results = {}
    
    # Jika test_size = 0, gunakan semua data untuk training
    if test_size == 0:
        X_train, X_test = X, X
        y_train, y_test = y, y
        print(f"\n\n{'='*50}")
        print(f"Skenario: 100% data training (Overfitting Test)")
        print(f"{'='*50}")
    else:
        # Split data menjadi training dan testing
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state, stratify=y)
        train_pct = (1 - test_size) * 100
        test_pct = test_size * 100
        print(f"\n\n{'='*50}")
        print(f"Skenario: {train_pct:.0f}% data training dan {test_pct:.0f}% data testing")
        print(f"{'='*50}")
    
    # Membuat pipeline dengan preprocessor dan model
    pipeline = Pipeline([
        ('preprocessor', preprocessor),
        ('classifier', MLPClassifier(
            hidden_layer_sizes=(10, 5),  # Dua hidden layer dengan 10 dan 5 neuron
            activation='relu',           # Fungsi aktivasi ReLU
            solver='adam',               # Optimizer Adam
            alpha=0.0001,                # Regularization parameter
            batch_size='auto',           # Ukuran batch otomatis
            learning_rate='adaptive',    # Learning rate adaptif
            max_iter=1000,               # Maksimum iterasi
            random_state=random_state    # Seed untuk reproduksibilitas
        ))
    ])
    
    # Melatih model
    print("Melatih model...")
    pipeline.fit(X_train, y_train)
    
    # Prediksi pada data training
    y_train_pred = pipeline.predict(X_train)
    train_accuracy = accuracy_score(y_train, y_train_pred)
    print(f"Akurasi pada data training: {train_accuracy:.4f}")
    
    # Prediksi pada data testing
    y_test_pred = pipeline.predict(X_test)
    
    # Jika test_size = 0, gunakan data training sebagai evaluasi
    if test_size == 0:
        print("Menggunakan data training sebagai evaluasi (Overfitting Test)")
        test_accuracy = train_accuracy
    else:
        test_accuracy = accuracy_score(y_test, y_test_pred)
        print(f"Akurasi pada data testing: {test_accuracy:.4f}")
    
    # Menghitung metrik evaluasi
    precision = precision_score(y_test, y_test_pred, pos_label='Layak')
    recall = recall_score(y_test, y_test_pred, pos_label='Layak')
    f1 = f1_score(y_test, y_test_pred, pos_label='Layak')
    
    # Menampilkan confusion matrix
    cm = confusion_matrix(y_test, y_test_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=['Tidak Layak', 'Layak'],
                yticklabels=['Tidak Layak', 'Layak'])
    plt.xlabel('Prediksi')
    plt.ylabel('Aktual')
    plt.title(f'Confusion Matrix - {train_pct:.0f}% Training / {test_pct:.0f}% Testing')
    plt.savefig(f'confusion_matrix_{train_pct:.0f}_{test_pct:.0f}.png')
    plt.close()
    
    # Menampilkan classification report
    print("\nClassification Report:")
    print(classification_report(y_test, y_test_pred))
    
    # Menyimpan hasil evaluasi
    results = {
        'train_size': 1 - test_size,
        'test_size': test_size,
        'train_accuracy': train_accuracy,
        'test_accuracy': test_accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'confusion_matrix': cm,
        'pipeline': pipeline
    }
    
    return results

# ## Evaluasi Model dengan Berbagai Skenario

# Skenario 1: 80% data training dan 20% data testing (standar)
results_80_20 = evaluate_model(X, y, test_size=0.2)

# Skenario 2: 100% data training (overfitting test)
results_100_0 = evaluate_model(X, y, test_size=0)

# Skenario 3: 50% data training dan 50% data testing
results_50_50 = evaluate_model(X, y, test_size=0.5)

# Skenario 4: 20% data training dan 80% data testing
results_20_80 = evaluate_model(X, y, test_size=0.8)

# ## Perbandingan Hasil Evaluasi

# Membuat DataFrame untuk perbandingan hasil
comparison = pd.DataFrame({
    'Skenario': ['80% Training / 20% Testing', '100% Training', '50% Training / 50% Testing', '20% Training / 80% Testing'],
    'Akurasi Training': [results_80_20['train_accuracy'], results_100_0['train_accuracy'], 
                         results_50_50['train_accuracy'], results_20_80['train_accuracy']],
    'Akurasi Testing': [results_80_20['test_accuracy'], results_100_0['test_accuracy'], 
                        results_50_50['test_accuracy'], results_20_80['test_accuracy']],
    'Precision': [results_80_20['precision'], results_100_0['precision'], 
                  results_50_50['precision'], results_20_80['precision']],
    'Recall': [results_80_20['recall'], results_100_0['recall'], 
               results_50_50['recall'], results_20_80['recall']],
    'F1 Score': [results_80_20['f1_score'], results_100_0['f1_score'], 
                 results_50_50['f1_score'], results_20_80['f1_score']]
})

print("\n\nPerbandingan Hasil Evaluasi:")
print(comparison)

# Visualisasi perbandingan metrik
plt.figure(figsize=(12, 8))
metrics = ['Akurasi Training', 'Akurasi Testing', 'Precision', 'Recall', 'F1 Score']
for i, metric in enumerate(metrics):
    plt.subplot(2, 3, i+1)
    sns.barplot(x='Skenario', y=metric, data=comparison)
    plt.xticks(rotation=45, ha='right')
    plt.title(metric)
plt.tight_layout()
plt.savefig('perbandingan_metrik.png')
plt.close()

# ## Simpan Model Terbaik

# Pilih model terbaik (biasanya model dengan skenario 80/20)
best_model = results_80_20['pipeline']

# Simpan model ke file
model_filename = 'model/backpropagation_model.pkl'

# Pastikan direktori model ada
os.makedirs('model', exist_ok=True)

# Simpan model
with open(model_filename, 'wb') as file:
    pickle.dump(best_model, file)

print(f"\nModel terbaik telah disimpan ke {model_filename}")

# Simpan juga scaler untuk normalisasi data
scaler_filename = 'model/scaler.pkl'
with open(scaler_filename, 'wb') as file:
    pickle.dump(preprocessor, file)

print(f"Preprocessor telah disimpan ke {scaler_filename}")

# ## Kesimpulan

print("\n\nKESIMPULAN:")
print("="*50)
print(f"Model terbaik adalah skenario 80% Training / 20% Testing dengan:")
print(f"- Akurasi: {results_80_20['test_accuracy']:.4f}")
print(f"- Precision: {results_80_20['precision']:.4f}")
print(f"- Recall: {results_80_20['recall']:.4f}")
print(f"- F1 Score: {results_80_20['f1_score']:.4f}")
print("="*50)

# Menampilkan informasi tentang model
print("\nInformasi Model:")
print(f"- Arsitektur: MLP dengan hidden layer (10, 5)")
print(f"- Fungsi aktivasi: ReLU")
print(f"- Optimizer: Adam")
print(f"- Learning rate: adaptive")
print(f"- Maksimum iterasi: 1000")
