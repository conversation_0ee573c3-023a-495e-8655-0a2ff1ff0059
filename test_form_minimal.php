<?php
// Form test minimal untuk debugging

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🧪 Test Form Minimal</h2>\n";

if (isset($_POST['submit'])) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
    echo "<h3>✅ Form berhasil di-submit!</h3>";
    echo "<strong>Data yang diterima:</strong><br>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    // Test koneksi database
    echo "<h4>Test Database:</h4>";
    include 'koneksi.php';
    if ($koneksi) {
        echo "✅ Koneksi database berhasil<br>";
        
        // Test insert sederhana
        $test_name = mysqli_real_escape_string($koneksi, $_POST['nama'] ?? 'Test User');
        $test_query = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_per<PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>, pen<PERSON><PERSON><PERSON>, j<PERSON><PERSON>_tan<PERSON><PERSON><PERSON>, jum<PERSON>_pinjaman, jang<PERSON>_waktu, jam<PERSON><PERSON>, tahun_kendaraan, status_pajak, kepemilikan_rumah, umur, tujuan_pinjaman, sumber_data) VALUES ('$test_name', 'Laki-laki', 'Menikah', 'Swasta', 5000000, 2, 20000000, 12, 'BPKB Motor', 2020, 'Aktif', 'Milik Sendiri', 30, 'Test', 'manual')";
        
        if (mysqli_query($koneksi, $test_query)) {
            $id = mysqli_insert_id($koneksi);
            echo "✅ Data berhasil disimpan dengan ID: $id<br>";
            
            // Test prediksi fallback
            echo "<h4>Test Prediksi Fallback:</h4>";
            require_once 'api_predict.php';
            
            $test_data = [
                'nama_nasabah' => $test_name,
                'penghasilan' => 5000000,
                'jumlah_tanggungan' => 2,
                'jumlah_pinjaman' => 20000000,
                'jangka_waktu' => 12,
                'umur' => 30
            ];
            
            try {
                $hasil = prediksi_dengan_backpropagation($test_data);
                echo "✅ Prediksi berhasil: " . $hasil['hasil_prediksi'] . "<br>";
                echo "Probabilitas: " . number_format($hasil['probabilitas'] * 100, 2) . "%<br>";
                echo "Keterangan: " . substr($hasil['keterangan'], 0, 100) . "...<br>";
                
                // Test simpan hasil prediksi
                $id_prediksi = simpan_hasil_prediksi($test_data, $hasil);
                if ($id_prediksi) {
                    echo "✅ Hasil prediksi disimpan dengan ID: $id_prediksi<br>";
                    echo "<a href='hasil_prediksi.php?id_nasabah=$id&id_prediksi=$id_prediksi'>Lihat Hasil Detail</a><br>";
                } else {
                    echo "❌ Gagal menyimpan hasil prediksi<br>";
                }
                
            } catch (Exception $e) {
                echo "❌ Error prediksi: " . $e->getMessage() . "<br>";
            }
            
        } else {
            echo "❌ Gagal menyimpan data: " . mysqli_error($koneksi) . "<br>";
        }
    } else {
        echo "❌ Koneksi database gagal<br>";
    }
    
    echo "</div>";
}
?>

<style>
.form-container {
    max-width: 500px;
    margin: 20px 0;
    padding: 20px;
    border: 1px solid #ddd;
    background: #f9f9f9;
}
.form-group {
    margin: 10px 0;
}
.form-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}
.form-group input, .form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}
.btn {
    padding: 10px 20px;
    background: #007cba;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}
.btn:hover {
    background: #005a8b;
}
</style>

<div class="form-container">
    <h3>📝 Form Test Sederhana</h3>
    <form method="post">
        <div class="form-group">
            <label>Nama:</label>
            <input type="text" name="nama" value="Test User <?php echo date('H:i:s'); ?>" required>
        </div>
        
        <div class="form-group">
            <label>Email:</label>
            <input type="email" name="email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label>Pesan:</label>
            <input type="text" name="pesan" value="Test form submission">
        </div>
        
        <div class="form-group">
            <button type="submit" name="submit" class="btn">
                🚀 Test Submit
            </button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <h3>🔗 Link Navigasi:</h3>
    <a href="check_system_status.php">🔍 Cek Status Sistem</a> | 
    <a href="debug_prediksi_baru.php">🧪 Debug Form Prediksi</a> | 
    <a href="prediksi_baru.php">📝 Form Prediksi Asli</a>
</div>

<script>
// Test JavaScript
console.log("✅ JavaScript berjalan normal");

// Test form submission dengan JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log("✅ DOM loaded");
    
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log("✅ Form submit event triggered");
            console.log("Form data:", new FormData(form));
        });
    }
});
</script>
