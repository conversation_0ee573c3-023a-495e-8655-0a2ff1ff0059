<?php
// Include koneksi database
require_once 'koneksi.php';

// Include navbar
include 'includes/navbar.php';

// Ambil ID nasabah dari parameter URL
$id_nasabah = isset($_GET['id_nasabah']) ? intval($_GET['id_nasabah']) : 0;

// Ambil hasil prediksi dari parameter URL (jika ada)
$prediction_result = isset($_GET['prediction']) ? $_GET['prediction'] : null;
$prediction_probability = isset($_GET['probability']) ? floatval($_GET['probability']) : null;
$prediction_error = isset($_GET['prediction_error']) ? $_GET['prediction_error'] : null;

// Ambil data nasabah dari database
$query = "SELECT * FROM nasabah WHERE id_nasabah = ?";
$stmt = mysqli_prepare($koneksi, $query);
mysqli_stmt_bind_param($stmt, "i", $id_nasabah);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$nasabah = mysqli_fetch_assoc($result);

// Jika data nasabah tidak ditemukan, redirect ke halaman utama
if (!$nasabah) {
    header("Location: index.php");
    exit;
}

// Jika tidak ada hasil prediksi dari parameter, coba ambil dari database
if (!$prediction_result && $nasabah['kelayakan']) {
    $prediction_result = $nasabah['kelayakan'];

    // Ambil detail prediksi dari tabel hasil_prediksi
    $query_prediksi = "SELECT probabilitas, keterangan FROM hasil_prediksi WHERE id_nasabah = ? ORDER BY tanggal_prediksi DESC LIMIT 1";
    $stmt_prediksi = mysqli_prepare($koneksi, $query_prediksi);
    mysqli_stmt_bind_param($stmt_prediksi, "i", $id_nasabah);
    mysqli_stmt_execute($stmt_prediksi);
    $result_prediksi = mysqli_stmt_get_result($stmt_prediksi);
    $prediksi_detail = mysqli_fetch_assoc($result_prediksi);

    if ($prediksi_detail) {
        $prediction_probability = $prediksi_detail['probabilitas'];
        $prediction_description = $prediksi_detail['keterangan'];
    }
}
?>

<!-- Page Header -->
<section class="bg-success text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1>Pengajuan Berhasil!</h1>
                <p class="lead">Terima kasih telah mengajukan kredit di PT. Clipan Finance Indonesia.</p>
            </div>
        </div>
    </div>
</section>

<!-- Success Message Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-body success-message">
                        <div class="success-icon">
                            <i class="fa fa-check-circle"></i>
                        </div>
                        <h2 class="mb-4">Pengajuan Kredit Anda Telah Diterima</h2>
                        <p class="lead mb-4">Nomor Aplikasi: <strong><?php echo sprintf("APP%06d", $id_nasabah); ?></strong></p>
                        <p>Kami telah menerima pengajuan kredit Anda dan telah melakukan analisis kelayakan secara otomatis menggunakan sistem AI.</p>

                        <?php if ($prediction_result): ?>
                        <!-- Hasil Prediksi Otomatis -->
                        <div class="alert <?php echo ($prediction_result == 'Layak') ? 'alert-success' : 'alert-warning'; ?> mt-4">
                            <h5><i class="fa <?php echo ($prediction_result == 'Layak') ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i> Hasil Analisis Kelayakan</h5>
                            <p class="mb-2"><strong>Status: <?php echo $prediction_result; ?></strong></p>
                            <?php if ($prediction_probability !== null): ?>
                            <p class="mb-2">Tingkat Keyakinan: <?php echo number_format($prediction_probability * 100, 1); ?>%</p>
                            <?php endif; ?>

                            <?php if ($prediction_result == 'Layak'): ?>
                            <p class="mb-0">Selamat! Pengajuan kredit Anda memenuhi kriteria kelayakan awal. Tim kami akan segera menghubungi Anda untuk proses verifikasi dan finalisasi.</p>
                            <?php else: ?>
                            <p class="mb-0">Pengajuan kredit Anda memerlukan review lebih lanjut. Tim kami akan menghubungi Anda untuk membahas opsi yang tersedia atau penyesuaian yang diperlukan.</p>
                            <?php endif; ?>
                        </div>

                        <div class="alert alert-info mt-4">
                            <h5>Langkah Selanjutnya:</h5>
                            <ol class="mb-0 text-start">
                                <?php if ($prediction_result == 'Layak'): ?>
                                <li>Tim kami akan menghubungi Anda dalam 1-2 hari kerja untuk verifikasi data</li>
                                <li>Proses verifikasi dokumen dan jaminan</li>
                                <li>Penandatanganan kontrak kredit</li>
                                <li>Pencairan dana ke rekening Anda</li>
                                <?php else: ?>
                                <li>Tim kami akan menghubungi Anda dalam 2-3 hari kerja</li>
                                <li>Diskusi mengenai penyesuaian syarat atau jumlah pinjaman</li>
                                <li>Re-evaluasi kelayakan jika diperlukan</li>
                                <li>Proses lanjutan sesuai hasil evaluasi</li>
                                <?php endif; ?>
                            </ol>
                        </div>

                        <?php elseif ($prediction_error): ?>
                        <!-- Error Prediksi -->
                        <div class="alert alert-warning mt-4">
                            <h5><i class="fa fa-exclamation-triangle"></i> Analisis Kelayakan</h5>
                            <p class="mb-2">Sistem analisis otomatis sedang mengalami gangguan, namun pengajuan Anda telah berhasil disimpan.</p>
                            <p class="mb-0">Tim analis kami akan melakukan review manual dan menghubungi Anda dalam 1-3 hari kerja.</p>
                        </div>

                        <div class="alert alert-info mt-4">
                            <h5>Langkah Selanjutnya:</h5>
                            <ol class="mb-0 text-start">
                                <li>Tim analis akan melakukan review manual terhadap pengajuan Anda</li>
                                <li>Anda akan dihubungi dalam 1-3 hari kerja untuk update status</li>
                                <li>Proses verifikasi data dan dokumen</li>
                                <li>Keputusan final dan proses selanjutnya</li>
                            </ol>
                        </div>

                        <?php else: ?>
                        <!-- Default jika tidak ada prediksi -->
                        <div class="alert alert-info mt-4">
                            <h5>Langkah Selanjutnya:</h5>
                            <ol class="mb-0 text-start">
                                <li>Tim kami akan menghubungi Anda untuk verifikasi data</li>
                                <li>Proses analisa kelayakan kredit akan dilakukan</li>
                                <li>Jika disetujui, Anda akan dihubungi untuk penandatanganan kontrak</li>
                                <li>Dana akan dicairkan ke rekening Anda</li>
                            </ol>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-primary me-2">Kembali ke Beranda</a>
                            <a href="#" class="btn btn-outline-secondary" onclick="window.print()">Cetak Bukti Pengajuan</a>
                        </div>
                    </div>
                </div>
                
                <!-- Ringkasan Pengajuan -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h4 class="mb-0">Ringkasan Pengajuan</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Data Pribadi</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td>Nama</td>
                                        <td>: <?php echo $nasabah['nama_nasabah']; ?></td>
                                    </tr>
                                    <tr>
                                        <td>Umur</td>
                                        <td>: <?php echo $nasabah['umur']; ?> tahun</td>
                                    </tr>
                                    <tr>
                                        <td>Jenis Kelamin</td>
                                        <td>: <?php echo $nasabah['jenis_kelamin']; ?></td>
                                    </tr>
                                    <tr>
                                        <td>Status</td>
                                        <td>: <?php echo $nasabah['status_perkawinan']; ?></td>
                                    </tr>
                                    <tr>
                                        <td>Pekerjaan</td>
                                        <td>: <?php echo $nasabah['pekerjaan']; ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5>Data Pinjaman</h5>
                                <table class="table table-borderless">
                                    <tr>
                                        <td>Jumlah Pinjaman</td>
                                        <td>: Rp <?php echo number_format($nasabah['jumlah_pinjaman'], 0, ',', '.'); ?></td>
                                    </tr>
                                    <tr>
                                        <td>Jangka Waktu</td>
                                        <td>: <?php echo $nasabah['jangka_waktu']; ?> bulan</td>
                                    </tr>
                                    <tr>
                                        <td>Jaminan</td>
                                        <td>: <?php echo $nasabah['jaminan']; ?></td>
                                    </tr>
                                    <?php if ($nasabah['jaminan'] == 'BPKB Motor' || $nasabah['jaminan'] == 'BPKB Mobil'): ?>
                                    <tr>
                                        <td>Tahun Kendaraan</td>
                                        <td>: <?php echo $nasabah['tahun_kendaraan']; ?></td>
                                    </tr>
                                    <tr>
                                        <td>Status Pajak</td>
                                        <td>: <?php echo $nasabah['status_pajak']; ?></td>
                                    </tr>
                                    <?php endif; ?>
                                    <tr>
                                        <td>Tujuan Pinjaman</td>
                                        <td>: <?php echo $nasabah['tujuan_pinjaman'] ?: 'Tidak disebutkan'; ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Informasi Kontak -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h4 class="mb-0">Butuh Bantuan?</h4>
                    </div>
                    <div class="card-body">
                        <p>Jika Anda memiliki pertanyaan atau membutuhkan bantuan, silakan hubungi kami:</p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-phone me-2"></i> (021) 1234-5678</li>
                            <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Include footer
include 'includes/footer.php';
?>
