@echo off
echo ======================================================
echo Setup Database Sistem Prediksi Kelayakan Kredit
echo ======================================================
echo.

echo Pastikan MySQL/MariaDB sudah berjalan!
echo.

echo Mencoba koneksi ke MySQL...
mysql -u root -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Tidak dapat terhubung ke MySQL!
    echo.
    echo Pastikan:
    echo 1. MySQL/MariaDB sudah berjalan
    echo 2. User 'root' dapat diakses tanpa password
    echo    atau edit file database_setup.sql untuk menyesuaikan kredensial
    echo.
    pause
    exit /b 1
)

echo Koneksi MySQL berhasil!
echo.

echo Membuat database dan tabel...
mysql -u root < database_setup.sql
if %errorlevel% neq 0 (
    echo ERROR: Gagal menjalankan setup database!
    echo.
    echo Coba jalankan manual:
    echo mysql -u root -p < database_setup.sql
    echo.
    pause
    exit /b 1
)

echo.
echo ======================================================
echo Database berhasil dibuat!
echo ======================================================
echo.
echo Database: sistem_prediksi_backpropagation
echo Tables: users, nasabah, laporan_prediksi, prediksi_detail, hasil_prediksi, proses_perhitungan
echo.
echo Default users:
echo - admin / password (role: admin)
echo - analis / password (role: analis)
echo.
echo Sample data nasabah telah ditambahkan untuk testing.
echo.
pause
