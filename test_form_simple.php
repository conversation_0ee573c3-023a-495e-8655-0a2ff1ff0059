<?php
// Test form sederhana untuk debug masalah

// Aktifkan error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'koneksi.php';
include 'cek_session.php';
cek_akses(['analis']);

echo "<h2>Test Form Prediksi Sederhana</h2>\n";

// Proses form jika ada submit
if (isset($_POST['submit'])) {
    echo "<h3>Data yang diterima:</h3>\n";
    echo "<pre>" . print_r($_POST, true) . "</pre>\n";
    
    // Validasi sederhana
    $nama = $_POST['nama_nasabah'] ?? '';
    $penghasilan = $_POST['penghasilan'] ?? '';
    $pinjaman = $_POST['jumlah_pinjaman'] ?? '';
    $umur = $_POST['umur'] ?? '';
    $tanggungan = $_POST['jumlah_tanggungan'] ?? '';
    
    if (empty($nama) || empty($penghasilan) || empty($pinjaman) || empty($umur) || empty($tanggungan)) {
        echo "<div style='color: red;'>❌ Data tidak lengkap!</div>\n";
    } else {
        echo "<div style='color: green;'>✅ Data lengkap, mencoba menyimpan...</div>\n";
        
        try {
            // Konversi data
            $penghasilan_num = (float)str_replace(['.', ','], '', $penghasilan);
            $pinjaman_num = (float)str_replace(['.', ','], '', $pinjaman);
            $umur_num = (int)$umur;
            $tanggungan_num = (int)$tanggungan;
            
            echo "<p>Penghasilan: $penghasilan_num</p>\n";
            echo "<p>Pinjaman: $pinjaman_num</p>\n";
            echo "<p>Umur: $umur_num</p>\n";
            echo "<p>Tanggungan: $tanggungan_num</p>\n";
            
            // Test insert ke database
            $sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, pekerjaan, penghasilan, jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak, kepemilikan_rumah, umur, tujuan_pinjaman) VALUES (?, 'Laki-laki', 'Menikah', 'Swasta', ?, ?, ?, 24, 'BPKB Motor', 2020, 'Aktif', 'Milik Sendiri', ?, 'Test')";
            
            $stmt = mysqli_prepare($koneksi, $sql);
            if (!$stmt) {
                throw new Exception("Error prepare: " . mysqli_error($koneksi));
            }
            
            mysqli_stmt_bind_param($stmt, "sdidi", $nama, $penghasilan_num, $tanggungan_num, $pinjaman_num, $umur_num);
            
            if (mysqli_stmt_execute($stmt)) {
                $id_nasabah = mysqli_insert_id($koneksi);
                echo "<div style='color: green;'>✅ Data berhasil disimpan dengan ID: $id_nasabah</div>\n";
                
                // Test prediksi sederhana
                echo "<h4>Test Prediksi:</h4>\n";
                require_once 'api_predict.php';
                
                // Ambil data yang baru disimpan
                $query = "SELECT * FROM nasabah WHERE id_nasabah = ?";
                $stmt2 = mysqli_prepare($koneksi, $query);
                mysqli_stmt_bind_param($stmt2, "i", $id_nasabah);
                mysqli_stmt_execute($stmt2);
                $result = mysqli_stmt_get_result($stmt2);
                $nasabah_data = mysqli_fetch_assoc($result);
                
                if ($nasabah_data) {
                    echo "<p>✅ Data nasabah berhasil diambil</p>\n";
                    
                    $hasil_prediksi = prediksi_dengan_backpropagation($nasabah_data);
                    echo "<p>✅ Prediksi berhasil: " . $hasil_prediksi['hasil_prediksi'] . "</p>\n";
                    echo "<p>Probabilitas: " . number_format($hasil_prediksi['probabilitas'] * 100, 2) . "%</p>\n";
                    
                    // Simpan hasil prediksi
                    $id_prediksi = simpan_hasil_prediksi($nasabah_data, $hasil_prediksi);
                    if ($id_prediksi) {
                        echo "<p>✅ Hasil prediksi berhasil disimpan dengan ID: $id_prediksi</p>\n";
                        echo "<p><a href='hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi'>Lihat Hasil Prediksi</a></p>\n";
                    } else {
                        echo "<p style='color: red;'>❌ Gagal menyimpan hasil prediksi</p>\n";
                    }
                } else {
                    echo "<p style='color: red;'>❌ Data nasabah tidak ditemukan setelah insert</p>\n";
                }
                
            } else {
                throw new Exception("Error execute: " . mysqli_error($koneksi));
            }
            
        } catch (Exception $e) {
            echo "<div style='color: red;'>❌ Error: " . $e->getMessage() . "</div>\n";
        }
    }
    
    echo "<hr>\n";
}
?>

<form method="post" style="max-width: 600px;">
    <h3>Form Test Sederhana</h3>
    
    <div style="margin: 10px 0;">
        <label>Nama Nasabah:</label><br>
        <input type="text" name="nama_nasabah" required style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>Umur:</label><br>
        <input type="number" name="umur" min="20" max="60" required style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>Penghasilan (tanpa titik):</label><br>
        <input type="number" name="penghasilan" required style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>Jumlah Tanggungan:</label><br>
        <input type="number" name="jumlah_tanggungan" min="0" max="10" required style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin: 10px 0;">
        <label>Jumlah Pinjaman (tanpa titik):</label><br>
        <input type="number" name="jumlah_pinjaman" required style="width: 100%; padding: 5px;">
    </div>
    
    <div style="margin: 20px 0;">
        <button type="submit" name="submit" style="padding: 10px 20px; background: #007cba; color: white; border: none;">
            Test Simpan & Prediksi
        </button>
    </div>
</form>

<p><a href="prediksi_baru.php">Kembali ke Form Asli</a></p>
