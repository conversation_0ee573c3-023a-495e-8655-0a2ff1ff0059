<?php
// Set header untuk JSON response
header('Content-Type: application/json');

// Izinkan akses dari domain manapun (untuk pengembangan)
// Untuk produksi, batasi domain yang diizinkan
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Jika request method adalah OPTIONS, kembalikan header saja
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Waktu mulai untuk menghitung durasi request
$start_time = microtime(true);

// Fungsi untuk menangani error
function handleError($message, $code = 400) {
    global $start_time, $koneksi;

    // Hitung durasi request
    $duration_ms = round((microtime(true) - $start_time) * 1000);

    // Log API request
    logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'],
                  file_get_contents('php://input'), json_encode(['error' => $message]),
                  $code, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $duration_ms);

    http_response_code($code);
    echo json_encode(['error' => $message]);
    exit;
}

// Fungsi untuk mencatat log API
function logApiRequest($endpoint, $method, $request_data, $response_data, $status_code, $ip_address, $user_agent, $duration_ms) {
    global $koneksi;

    // Cek apakah logging diaktifkan
    $stmt = $koneksi->prepare("SELECT nilai_konfigurasi FROM konfigurasi_api WHERE nama_konfigurasi = 'log_requests'");
    $stmt->execute();
    $result = $stmt->get_result();
    $log_enabled = $result->fetch_assoc()['nilai_konfigurasi'] === 'true';

    if (!$log_enabled) {
        return;
    }

    // Simpan log ke database
    $stmt = $koneksi->prepare("INSERT INTO api_log (endpoint, method, request_data, response_data, status_code, ip_address, user_agent, durasi_ms)
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssissi", $endpoint, $method, $request_data, $response_data, $status_code, $ip_address, $user_agent, $duration_ms);
    $stmt->execute();
}

// Fungsi untuk mendapatkan konfigurasi API
function getApiConfig($name, $default = null) {
    global $koneksi;

    $stmt = $koneksi->prepare("SELECT nilai_konfigurasi FROM konfigurasi_api WHERE nama_konfigurasi = ?");
    $stmt->bind_param("s", $name);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        return $default;
    }

    return $result->fetch_assoc()['nilai_konfigurasi'];
}

// Ambil path dari URL
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/api/', '', $path);
$path = trim($path, '/');

// Ambil method request
$method = $_SERVER['REQUEST_METHOD'];

// Koneksi ke database
require_once '../koneksi.php';

// Endpoint untuk prediksi
if ($path === 'predict' && $method === 'POST') {
    // Ambil data dari request body
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);

    if (!$data) {
        handleError('Invalid JSON data');
    }

    // Validasi data yang diperlukan
    $required_fields = ['id_nasabah'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field])) {
            handleError("Field '$field' is required");
        }
    }

    // Ambil data nasabah dari database
    $id_nasabah = $data['id_nasabah'];
    $sql = "SELECT * FROM nasabah WHERE id_nasabah = ?";
    $stmt = $koneksi->prepare($sql);
    $stmt->bind_param("i", $id_nasabah);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        handleError("Nasabah dengan ID $id_nasabah tidak ditemukan", 404);
    }

    $nasabah = $result->fetch_assoc();

    // Siapkan data untuk dikirim ke model Python
    $model_data = [
        'id_nasabah' => $nasabah['id_nasabah'] // Kirim ID nasabah saja, model akan mengambil data dari database
    ];

    // Ambil URL server model dari konfigurasi
    $model_url = getApiConfig('model_server_url', 'http://localhost:5000');
    $model_url .= '/api/predict';

    // Ambil timeout dari konfigurasi
    $timeout = intval(getApiConfig('api_timeout', '30'));

    // Kirim data ke model Python menggunakan cURL
    $ch = curl_init($model_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($model_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code !== 200) {
        handleError('Failed to get prediction from model', 500);
    }

    $prediction = json_decode($response, true);

    // Update kolom kelayakan di tabel nasabah
    $hasil_prediksi = $prediction['hasil_prediksi'];
    $sql_update = "UPDATE nasabah SET kelayakan = ? WHERE id_nasabah = ?";
    $stmt_update = $koneksi->prepare($sql_update);
    $stmt_update->bind_param("si", $hasil_prediksi, $id_nasabah);
    $stmt_update->execute();

    // Tambahkan informasi update ke response
    $prediction['kelayakan_updated'] = true;
    $response = json_encode($prediction);

    // Hitung durasi request
    $duration_ms = round((microtime(true) - $start_time) * 1000);

    // Log API request
    logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'],
                  $json_data, $response,
                  200, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $duration_ms);

    // Kembalikan hasil prediksi
    echo $response;
    exit;
}

// Endpoint untuk mendapatkan informasi model
else if ($path === 'model-info' && $method === 'GET') {
    // Ambil URL server model dari konfigurasi
    $model_url = getApiConfig('model_server_url', 'http://localhost:5000');
    $model_url .= '/api/model-info';

    // Ambil timeout dari konfigurasi
    $timeout = intval(getApiConfig('api_timeout', '30'));

    // Kirim request ke model Python
    $ch = curl_init($model_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code !== 200) {
        handleError('Failed to get model information', 500);
    }

    // Hitung durasi request
    $duration_ms = round((microtime(true) - $start_time) * 1000);

    // Log API request
    logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'],
                  null, $response,
                  200, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $duration_ms);

    echo $response;
    exit;
}

// Endpoint untuk mendapatkan daftar nasabah
else if ($path === 'nasabah' && $method === 'GET') {
    $sql = "SELECT * FROM nasabah ORDER BY nama_nasabah";
    $result = $koneksi->query($sql);

    $nasabah = [];
    while ($row = $result->fetch_assoc()) {
        $nasabah[] = $row;
    }

    $response = json_encode(['data' => $nasabah]);

    // Hitung durasi request
    $duration_ms = round((microtime(true) - $start_time) * 1000);

    // Log API request
    logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'],
                  null, $response,
                  200, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $duration_ms);

    echo $response;
    exit;
}

// Endpoint untuk mendapatkan detail nasabah
else if (preg_match('/^nasabah\/(\d+)$/', $path, $matches) && $method === 'GET') {
    $id_nasabah = $matches[1];

    $sql = "SELECT * FROM nasabah WHERE id_nasabah = ?";
    $stmt = $koneksi->prepare($sql);
    $stmt->bind_param("i", $id_nasabah);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        handleError("Nasabah dengan ID $id_nasabah tidak ditemukan", 404);
    }

    $nasabah = $result->fetch_assoc();
    $response = json_encode($nasabah);

    // Hitung durasi request
    $duration_ms = round((microtime(true) - $start_time) * 1000);

    // Log API request
    logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'],
                  null, $response,
                  200, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $duration_ms);

    echo $response;
    exit;
}

// Endpoint untuk mendapatkan riwayat prediksi
else if ($path === 'prediksi/riwayat' && $method === 'GET') {
    $sql = "SELECT p.id_prediksi, p.id_nasabah, n.nama_nasabah, p.hasil_prediksi, p.probabilitas,
                  p.tanggal_prediksi, l.id_laporan, m.nama_model, m.akurasi as akurasi_model
           FROM prediksi_detail p
           JOIN nasabah n ON p.id_nasabah = n.id_nasabah
           JOIN laporan_prediksi l ON p.id_laporan = l.id_laporan
           JOIN model_ml m ON l.id_model = m.id_model
           ORDER BY p.tanggal_prediksi DESC";
    $result = $koneksi->query($sql);

    $prediksi = [];
    while ($row = $result->fetch_assoc()) {
        $prediksi[] = $row;
    }

    $response = json_encode(['data' => $prediksi]);

    // Hitung durasi request
    $duration_ms = round((microtime(true) - $start_time) * 1000);

    // Log API request
    logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'],
                  null, $response,
                  200, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $duration_ms);

    echo $response;
    exit;
}

// Endpoint untuk mendapatkan detail proses perhitungan
else if (preg_match('/^prediksi\/proses\/(\d+)$/', $path, $matches) && $method === 'GET') {
    $id_prediksi = $matches[1];

    $sql = "SELECT * FROM proses_perhitungan WHERE id_prediksi = ? ORDER BY waktu_proses";
    $stmt = $koneksi->prepare($sql);
    $stmt->bind_param("i", $id_prediksi);
    $stmt->execute();
    $result = $stmt->get_result();

    $proses = [];
    while ($row = $result->fetch_assoc()) {
        $proses[] = $row;
    }

    $response = json_encode(['data' => $proses]);

    // Hitung durasi request
    $duration_ms = round((microtime(true) - $start_time) * 1000);

    // Log API request
    logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'],
                  null, $response,
                  200, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $duration_ms);

    echo $response;
    exit;
}

// Endpoint untuk mendapatkan daftar nasabah dengan kelayakan
else if ($path === 'nasabah/kelayakan' && $method === 'GET') {
    $sql = "SELECT id_nasabah, nama_nasabah, umur, jenis_kelamin, pekerjaan, penghasilan,
                  jumlah_pinjaman, kepemilikan_rumah, jaminan, kelayakan
           FROM nasabah
           WHERE kelayakan IS NOT NULL
           ORDER BY nama_nasabah";
    $result = $koneksi->query($sql);

    $nasabah = [];
    while ($row = $result->fetch_assoc()) {
        $nasabah[] = $row;
    }

    $response = json_encode(['data' => $nasabah]);

    // Hitung durasi request
    $duration_ms = round((microtime(true) - $start_time) * 1000);

    // Log API request
    logApiRequest($_SERVER['REQUEST_URI'], $_SERVER['REQUEST_METHOD'],
                  null, $response,
                  200, $_SERVER['REMOTE_ADDR'], $_SERVER['HTTP_USER_AGENT'], $duration_ms);

    echo $response;
    exit;
}

// Endpoint tidak ditemukan
else {
    handleError('Endpoint not found', 404);
}
?>
