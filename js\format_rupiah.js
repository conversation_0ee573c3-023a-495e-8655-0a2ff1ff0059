document.addEventListener('DOMContentLoaded', function() {
    // <PERSON>i semua input dengan class 'format-rupiah'
    var rupiahInputs = document.querySelectorAll('input[name="pengh<PERSON><PERSON>"], input[name="jum<PERSON>_pinjaman"]');
    
    // Tambahkan event listener untuk setiap input
    rupiahInputs.forEach(function(input) {
        // Tambahkan class untuk styling
        input.classList.add('format-rupiah');
        
        // Format nilai awal jika ada
        if (input.value) {
            input.value = formatRupiah(input.value);
        }
        
        // Event saat input berubah
        input.addEventListener('input', function(e) {
            // Ambil nilai input
            var value = e.target.value;
            
            // Hapus semua karakter selain angka
            value = value.replace(/[^\d]/g, '');
            
            // Format nilai menjadi format rupiah
            e.target.value = formatRupiah(value);
        });
        
        // Event saat input kehilangan fokus
        input.addEventListener('blur', function(e) {
            // <PERSON><PERSON> nilai input
            var value = e.target.value;
            
            // Hapus semua karakter selain angka
            value = value.replace(/[^\d]/g, '');
            
            // Format nilai menjadi format rupiah
            e.target.value = formatRupiah(value);
        });
        
        // Event saat input mendapatkan fokus
        input.addEventListener('focus', function(e) {
            // Tidak perlu melakukan apa-apa, biarkan format rupiah tetap ada
        });
    });
});

function formatRupiah(angka) {
    var number_string = angka.toString(),
        split = number_string.split(','),
        sisa = split[0].length % 3,
        rupiah = split[0].substr(0, sisa),
        ribuan = split[0].substr(sisa).match(/\d{3}/gi);
        
    // Tambahkan titik jika ada ribuan
    if (ribuan) {
        separator = sisa ? '.' : '';
        rupiah += separator + ribuan.join('.');
    }
    
    rupiah = split[1] != undefined ? rupiah + ',' + split[1] : rupiah;
    return rupiah;
}
