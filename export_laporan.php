<?php
include 'koneksi.php';
include 'cek_session.php';

// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);

// Set header untuk file Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="Laporan_Prediksi_Kelayakan_' . date('Y-m-d') . '.xls"');
header('Cache-Control: max-age=0');

// Filter berdasarkan kelayakan jika ada
$filter = "";
if (isset($_GET['kelayakan']) && in_array($_GET['kelayakan'], ['Layak', 'Tidak Layak'])) {
    $kelayakan = $_GET['kelayakan'];
    $filter = " AND n.kelayakan = '$kelayakan'";
}

// Query untuk mendapatkan data laporan prediksi
$sql = "SELECT p.id_prediksi, n.nama_nasabah, n.pengh<PERSON>, n.j<PERSON><PERSON>_pin<PERSON><PERSON>, n.jaminan, 
               n.kelayakan, p.tanggal_prediksi
        FROM prediksi_detail p
        JOIN nasabah n ON p.id_nasabah = n.id_nasabah
        WHERE n.kelayakan IS NOT NULL $filter
        ORDER BY p.tanggal_prediksi DESC";

$result = $koneksi->query($sql);

// Mulai output Excel
echo '<!DOCTYPE html>';
echo '<html>';
echo '<head>';
echo '<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />';
echo '<title>Export Laporan Prediksi Kelayakan</title>';
echo '</head>';
echo '<body>';

// Judul laporan
echo '<h2>Laporan Prediksi Kelayakan Nasabah</h2>';
echo '<p>Tanggal Export: ' . date('d-m-Y H:i:s') . '</p>';

if (isset($_GET['kelayakan'])) {
    echo '<p>Filter: Nasabah dengan status ' . $_GET['kelayakan'] . '</p>';
}

// Tabel data
echo '<table border="1">';
echo '<thead>';
echo '<tr>';
echo '<th>No</th>';
echo '<th>Nama Nasabah</th>';
echo '<th>Penghasilan</th>';
echo '<th>Jumlah Pinjaman</th>';
echo '<th>Jaminan</th>';
echo '<th>Status Kelayakan</th>';
echo '<th>Tanggal Prediksi</th>';
echo '</tr>';
echo '</thead>';
echo '<tbody>';

if ($result->num_rows > 0) {
    $no = 1;
    while ($row = $result->fetch_assoc()) {
        echo '<tr>';
        echo '<td>' . $no++ . '</td>';
        echo '<td>' . $row['nama_nasabah'] . '</td>';
        echo '<td>Rp ' . number_format($row['penghasilan'], 0, ',', '.') . '</td>';
        echo '<td>Rp ' . number_format($row['jumlah_pinjaman'], 0, ',', '.') . '</td>';
        echo '<td>' . $row['jaminan'] . '</td>';
        echo '<td>' . $row['kelayakan'] . '</td>';
        echo '<td>' . date('d-m-Y H:i:s', strtotime($row['tanggal_prediksi'])) . '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr>';
    echo '<td colspan="7" align="center">Tidak ada data laporan prediksi</td>';
    echo '</tr>';
}

echo '</tbody>';
echo '</table>';

// Statistik
$sql_stat = "SELECT n.kelayakan, COUNT(*) as jumlah 
             FROM prediksi_detail p
             JOIN nasabah n ON p.id_nasabah = n.id_nasabah
             WHERE n.kelayakan IS NOT NULL $filter
             GROUP BY n.kelayakan";
$result_stat = $koneksi->query($sql_stat);

$layak = 0;
$tidak_layak = 0;

while ($row = $result_stat->fetch_assoc()) {
    if ($row['kelayakan'] == 'Layak') {
        $layak = $row['jumlah'];
    } else {
        $tidak_layak = $row['jumlah'];
    }
}

$total = $layak + $tidak_layak;
$persen_layak = $total > 0 ? round(($layak / $total) * 100, 2) : 0;
$persen_tidak_layak = $total > 0 ? round(($tidak_layak / $total) * 100, 2) : 0;

echo '<br><br>';
echo '<h3>Statistik Kelayakan</h3>';
echo '<table border="1">';
echo '<thead>';
echo '<tr>';
echo '<th>Status</th>';
echo '<th>Jumlah</th>';
echo '<th>Persentase</th>';
echo '</tr>';
echo '</thead>';
echo '<tbody>';
echo '<tr>';
echo '<td>Layak</td>';
echo '<td>' . $layak . '</td>';
echo '<td>' . $persen_layak . '%</td>';
echo '</tr>';
echo '<tr>';
echo '<td>Tidak Layak</td>';
echo '<td>' . $tidak_layak . '</td>';
echo '<td>' . $persen_tidak_layak . '%</td>';
echo '</tr>';
echo '<tr>';
echo '<td><strong>Total</strong></td>';
echo '<td><strong>' . $total . '</strong></td>';
echo '<td><strong>100%</strong></td>';
echo '</tr>';
echo '</tbody>';
echo '</table>';

echo '</body>';
echo '</html>';
?>
