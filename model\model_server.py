from flask import Flask, request, jsonify
import numpy as np
import pandas as pd
import pickle
import logging
from datetime import datetime
from sklearn.preprocessing import StandardScaler
import os

# Setup logging
logging.basicConfig(
    filename='model_server.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

app = Flask(__name__)

# Load model dan scaler
try:
    model_path = os.path.join(os.path.dirname(__file__), 'backpropagation_model.pkl')
    scaler_path = os.path.join(os.path.dirname(__file__), 'scaler.pkl')
    
    with open(model_path, 'rb') as f:
        model = pickle.load(f)
    with open(scaler_path, 'rb') as f:
        scaler = pickle.load(f)
    
    logging.info("Model dan scaler berhasil dimuat")
except Exception as e:
    logging.error(f"Error saat memuat model: {str(e)}")
    raise

@app.route('/api/predict', methods=['POST'])
def predict():
    try:
        # Terima data JSON
        data = request.json
        logging.info(f"Menerima request prediksi: {data}")
        
        # Konversi data ke format yang sesuai untuk model
        features = pd.DataFrame([data])
        
        # Encoding untuk fitur kategorikal
        features['jenis_kelamin'] = features['jenis_kelamin'].map({'Laki-laki': 1, 'Perempuan': 0})
        features['status_pajak'] = features['status_pajak'].map({'Aktif': 1, 'Tidak Aktif': 0})
        
        # One-hot encoding untuk fitur kategorikal lainnya
        features = pd.get_dummies(features, columns=['pekerjaan', 'kepemilikan_rumah', 'jaminan'])
        
        # Normalisasi fitur numerik
        numeric_features = ['umur', 'penghasilan', 'jumlah_tanggungan', 'jumlah_pinjaman', 
                          'jangka_waktu', 'umur_kendaraan', 'rasio_pinjaman_penghasilan',
                          'penghasilan_per_tanggungan']
        
        features[numeric_features] = scaler.transform(features[numeric_features])
        
        # Lakukan prediksi
        prediction_proba = model.predict_proba(features)
        prediction = model.predict(features)
        
        # Format hasil
        hasil = {
            'hasil_prediksi': 'Layak' if prediction[0] == 1 else 'Tidak Layak',
            'probabilitas': float(prediction_proba[0][1]),
            'keterangan': 'Nasabah diprediksi layak menerima kredit' if prediction[0] == 1 
                         else 'Nasabah diprediksi tidak layak menerima kredit'
        }
        
        logging.info(f"Hasil prediksi: {hasil}")
        return jsonify(hasil)
        
    except Exception as e:
        logging.error(f"Error saat memproses prediksi: {str(e)}")
        return jsonify({'error': str(e)}), 400

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
