<?php

// Header untuk API
header('Content-Type: application/json');

// Informasi model
$model_info = [
    'model_type' => 'Backpropagation Neural Network',
    'features' => [
        'umur',
        'jenis_kelamin',
        'status_perkawinan',
        'pekerjaan',
        'penghasilan',
        'jumlah_pinjaman',
        'kepemilikan_rumah',
        'jaminan',
        'tahun_kendaraan',
        'status_pajak',
        'jumlah_tanggungan',
        'jangka_waktu'
    ],
    'parameter' => 'Backpropagation Neural Network (MLP Classifier)',
    'akurasi' => 87.5,
    'deskripsi' => 'Model ini menggunakan algoritma Backpropagation Neural Network untuk memprediksi kelayakan kredit nasabah. Model mempertimbangkan berbagai faktor seperti rasio pinjaman terhadap penghasilan, jumlah tanggung<PERSON>, jeni<PERSON> jaminan (BPKB mobil/motor dengan tahun kendaraan dan status pajak), dan kemampuan finansial nasabah. Hasil prediksi didasarkan pada nilai sigmoid dari perhitungan backpropagation.'
];

// Kirim informasi model
echo json_encode($model_info);
?>
