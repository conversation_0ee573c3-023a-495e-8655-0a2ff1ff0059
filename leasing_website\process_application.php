<?php
// Include koneksi database
require_once 'koneksi.php';

// Inisialisasi variabel error
$error = false;
$error_message = '';

function is_model_server_running() {
    $api_url = 'http://localhost:5000/api/info';

    // Inisialisasi cURL dengan timeout singkat
    $ch = curl_init($api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3); // Timeout 3 detik
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2); // Connection timeout 2 detik
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Eksekusi request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);

    // Tutup cURL
    curl_close($ch);

    // Server dianggap berjalan jika response berhasil dan HTTP code 200
    return ($response !== false && empty($curl_error) && $http_code === 200);
}

function start_model_server() {
    try {
        error_log("Memulai server model Python...");

        // Path ke file batch (naik satu level dari leasing_website)
        $batch_file = dirname(__DIR__) . DIRECTORY_SEPARATOR . 'start_model_server.bat';

        // Periksa apakah file batch ada
        if (!file_exists($batch_file)) {
            error_log("File start_model_server.bat tidak ditemukan di: " . $batch_file);
            return false;
        }

        // Jalankan batch file di background menggunakan start command
        $command = 'start /B "" "' . $batch_file . '"';

        error_log("Menjalankan command: " . $command);

        // Eksekusi command
        $output = [];
        $return_var = 0;
        exec($command, $output, $return_var);

        error_log("Command executed with return code: " . $return_var);

        // Tunggu beberapa detik untuk server startup
        sleep(3);

        // Periksa apakah server sudah berjalan dengan mencoba beberapa kali
        $max_attempts = 10;
        $attempt = 0;

        while ($attempt < $max_attempts) {
            if (is_model_server_running()) {
                error_log("Server model Python berhasil dimulai setelah " . ($attempt + 1) . " percobaan");
                return true;
            }

            $attempt++;
            sleep(2); // Tunggu 2 detik sebelum mencoba lagi
            error_log("Mencoba memeriksa server, percobaan ke-" . $attempt);
        }

        error_log("Server model Python gagal dimulai setelah " . $max_attempts . " percobaan");
        return false;

    } catch (Exception $e) {
        error_log("Error saat memulai server model: " . $e->getMessage());
        return false;
    }
}

function ensure_model_server_running() {
    // Periksa apakah server sudah berjalan
    if (is_model_server_running()) {
        error_log("Server model Python sudah berjalan");
        return true;
    }

    error_log("Server model Python tidak berjalan, mencoba memulai...");

    // Coba mulai server
    if (start_model_server()) {
        error_log("Server model Python berhasil dimulai");
        return true;
    } else {
        error_log("Gagal memulai server model Python");
        return false;
    }
}

// Cek apakah form telah disubmit
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Log data yang diterima untuk debugging
    error_log("Data form yang diterima: " . print_r($_POST, true));

    // Ambil data dari form
    $nama_nasabah = mysqli_real_escape_string($koneksi, $_POST['nama_nasabah']);
    $umur = mysqli_real_escape_string($koneksi, $_POST['umur']);
    $jenis_kelamin = mysqli_real_escape_string($koneksi, $_POST['jenis_kelamin']);
    $status_perkawinan = mysqli_real_escape_string($koneksi, $_POST['status_perkawinan']);
    $pekerjaan = mysqli_real_escape_string($koneksi, $_POST['pekerjaan']);
    $kepemilikan_rumah = mysqli_real_escape_string($koneksi, $_POST['kepemilikan_rumah']);

    // Data finansial
    // Pastikan untuk menghapus semua titik sebagai pemisah ribuan
    $penghasilan_raw = mysqli_real_escape_string($koneksi, $_POST['penghasilan']);
    $penghasilan = str_replace('.', '', $penghasilan_raw);
    $jumlah_tanggungan = mysqli_real_escape_string($koneksi, $_POST['jumlah_tanggungan']);

    // Data pinjaman
    // Pastikan untuk menghapus semua titik sebagai pemisah ribuan
    $jumlah_pinjaman_raw = mysqli_real_escape_string($koneksi, $_POST['jumlah_pinjaman']);
    $jumlah_pinjaman = str_replace('.', '', $jumlah_pinjaman_raw);
    $jangka_waktu = mysqli_real_escape_string($koneksi, $_POST['jangka_waktu']);
    $jaminan = mysqli_real_escape_string($koneksi, $_POST['jaminan']);
    $tujuan_pinjaman = mysqli_real_escape_string($koneksi, $_POST['tujuan_pinjaman'] ?? '');

    // Data kendaraan (jika jaminan adalah BPKB)
    $tahun_kendaraan = '';
    $status_pajak = '';

    if ($jaminan == 'BPKB Motor' || $jaminan == 'BPKB Mobil') {
        // Hanya ambil tahun kendaraan dan status pajak
        $tahun_kendaraan = isset($_POST['tahun_kendaraan']) ? mysqli_real_escape_string($koneksi, $_POST['tahun_kendaraan']) : '';
        $status_pajak = isset($_POST['status_pajak']) ? mysqli_real_escape_string($koneksi, $_POST['status_pajak']) : '';
    }

    // Validasi data
    if (empty($nama_nasabah) || empty($umur) || empty($jenis_kelamin) || empty($status_perkawinan) ||
        empty($pekerjaan) || empty($kepemilikan_rumah) || empty($penghasilan) ||
        empty($jumlah_pinjaman) || empty($jangka_waktu) || empty($jaminan)) {

        $error = true;
        $error_message = "Semua field yang bertanda * harus diisi.";
    }
    else if ($umur < 20 || $umur > 70) {
        $error = true;
        $error_message = "Umur harus antara 20 hingga 70 tahun.";
    }
    else if ($penghasilan < 1000000) {
        $error = true;
        $error_message = "Penghasilan minimal Rp 1.000.000. (Nilai yang diinput: Rp " . number_format($penghasilan, 0, ',', '.') . ")";
        // Tambahkan log untuk debugging
        error_log("Validasi penghasilan gagal. Nilai input: " . $penghasilan_raw . ", Nilai setelah diproses: " . $penghasilan);
    }
    else if ($jumlah_pinjaman < 5000000) {
        $error = true;
        $error_message = "Jumlah pinjaman minimal Rp 5.000.000. (Nilai yang diinput: Rp " . number_format($jumlah_pinjaman, 0, ',', '.') . ")";
        // Tambahkan log untuk debugging
        error_log("Validasi jumlah pinjaman gagal. Nilai input: " . $jumlah_pinjaman_raw . ", Nilai setelah diproses: " . $jumlah_pinjaman);
    }
    else if (($jaminan == 'BPKB Motor' || $jaminan == 'BPKB Mobil') &&
            (empty($tahun_kendaraan) || empty($status_pajak))) {
        $error = true;
        $error_message = "Tahun kendaraan dan status pajak harus diisi jika jaminan berupa BPKB.";

        // Tambahkan log untuk debugging
        error_log("Validasi data kendaraan gagal. Jaminan: " . $jaminan);
        error_log("Tahun kendaraan: " . ($tahun_kendaraan ?? 'kosong'));
        error_log("Status pajak: " . ($status_pajak ?? 'kosong'));
    }

    // Jika tidak ada error, simpan data ke database
    if (!$error) {
        // Buat query untuk menyimpan data nasabah
        if ($jaminan == 'BPKB Motor' || $jaminan == 'BPKB Mobil') {
            // Query dengan field tahun kendaraan dan status pajak
            $query = "INSERT INTO nasabah (
                        nama_nasabah,
                        jenis_kelamin,
                        status_perkawinan,
                        pekerjaan,
                        penghasilan,
                        jumlah_tanggungan,
                        jumlah_pinjaman,
                        jangka_waktu,
                        jaminan,
                        tahun_kendaraan,
                        status_pajak,
                        kepemilikan_rumah,
                        umur,
                        tujuan_pinjaman
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            // Prepare statement
            $stmt = mysqli_prepare($koneksi, $query);

            // Bind parameter
            mysqli_stmt_bind_param(
                $stmt,
                "ssssdidissssis",
                $nama_nasabah,
                $jenis_kelamin,
                $status_perkawinan,
                $pekerjaan,
                $penghasilan,
                $jumlah_tanggungan,
                $jumlah_pinjaman,
                $jangka_waktu,
                $jaminan,
                $tahun_kendaraan,
                $status_pajak,
                $kepemilikan_rumah,
                $umur,
                $tujuan_pinjaman
            );
        } else {
            // Query tanpa field kendaraan
            $query = "INSERT INTO nasabah (
                        nama_nasabah,
                        jenis_kelamin,
                        status_perkawinan,
                        pekerjaan,
                        penghasilan,
                        jumlah_tanggungan,
                        jumlah_pinjaman,
                        jangka_waktu,
                        jaminan,
                        kepemilikan_rumah,
                        umur,
                        tujuan_pinjaman
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            // Prepare statement
            $stmt = mysqli_prepare($koneksi, $query);

            // Bind parameter
            mysqli_stmt_bind_param(
                $stmt,
                "ssssdiidssis",
                $nama_nasabah,
                $jenis_kelamin,
                $status_perkawinan,
                $pekerjaan,
                $penghasilan,
                $jumlah_tanggungan,
                $jumlah_pinjaman,
                $jangka_waktu,
                $jaminan,
                $kepemilikan_rumah,
                $umur,
                $tujuan_pinjaman
            );
        }

        // Eksekusi query
        if (mysqli_stmt_execute($stmt)) {
            // Ambil ID nasabah yang baru saja diinsert
            $id_nasabah = mysqli_insert_id($koneksi);

            error_log("Data nasabah berhasil disimpan dengan ID: " . $id_nasabah);

            // Lakukan prediksi otomatis setelah data tersimpan
            try {
                error_log("Memulai prediksi otomatis untuk nasabah ID: " . $id_nasabah);

                // Pastikan server model Python berjalan
                if (!ensure_model_server_running()) {
                    throw new Exception("Gagal memulai server model Python untuk prediksi otomatis");
                }

                // Ambil data nasabah yang baru disimpan untuk prediksi
                $query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
                $stmt_nasabah = mysqli_prepare($koneksi, $query_nasabah);
                mysqli_stmt_bind_param($stmt_nasabah, "i", $id_nasabah);
                mysqli_stmt_execute($stmt_nasabah);
                $result_nasabah = mysqli_stmt_get_result($stmt_nasabah);
                $nasabah_data = mysqli_fetch_assoc($result_nasabah);

                if (!$nasabah_data) {
                    throw new Exception("Data nasabah tidak ditemukan setelah disimpan");
                }

                // URL API model Python
                $api_url = 'http://localhost:5000/api/predict';

                // Siapkan data untuk dikirim ke API (hanya kirim id_nasabah)
                $data_untuk_api = [
                    'id_nasabah' => $id_nasabah
                ];

                // Konversi ke JSON
                $json_data = json_encode($data_untuk_api);
                error_log("Mengirim request ke API model Python: " . $json_data);

                // Inisialisasi cURL
                $ch = curl_init($api_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Content-Length: ' . strlen($json_data)
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Timeout 30 detik
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // Connection timeout 10 detik
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

                // Eksekusi request
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curl_error = curl_error($ch);
                curl_close($ch);

                error_log("Response dari API model Python (HTTP $http_code): " . $response);

                if ($response === false || !empty($curl_error)) {
                    throw new Exception("Error koneksi ke API model Python: " . $curl_error);
                }

                if ($http_code !== 200) {
                    throw new Exception("API model Python mengembalikan status error: " . $http_code);
                }

                // Parse hasil dari API
                $hasil_api = json_decode($response, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception("Response API tidak valid JSON: " . $response);
                }

                if (isset($hasil_api['error'])) {
                    throw new Exception("API model Python mengembalikan error: " . $hasil_api['error']);
                }

                // Simpan hasil prediksi ke database
                $hasil_prediksi = $hasil_api['hasil_prediksi'];
                $probabilitas = $hasil_api['probabilitas'];
                $keterangan = $hasil_api['keterangan'] ?? 'Prediksi otomatis menggunakan model backpropagation neural network';

                $query_hasil = "INSERT INTO hasil_prediksi (id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                                VALUES (?, ?, ?, ?, NOW())";
                $stmt_hasil = mysqli_prepare($koneksi, $query_hasil);
                mysqli_stmt_bind_param($stmt_hasil, "isds", $id_nasabah, $hasil_prediksi, $probabilitas, $keterangan);

                if (mysqli_stmt_execute($stmt_hasil)) {
                    $id_prediksi = mysqli_insert_id($koneksi);

                    // Update status kelayakan di tabel nasabah
                    $query_update = "UPDATE nasabah SET kelayakan = ? WHERE id_nasabah = ?";
                    $stmt_update = mysqli_prepare($koneksi, $query_update);
                    mysqli_stmt_bind_param($stmt_update, "si", $hasil_prediksi, $id_nasabah);
                    mysqli_stmt_execute($stmt_update);

                    error_log("Prediksi otomatis berhasil: " . $hasil_prediksi . " (probabilitas: " . $probabilitas . ")");

                    // Redirect ke halaman sukses dengan hasil prediksi
                    header("Location: success.php?id_nasabah=" . $id_nasabah . "&prediction=" . urlencode($hasil_prediksi) . "&probability=" . urlencode($probabilitas));
                    exit;
                } else {
                    error_log("Gagal menyimpan hasil prediksi: " . mysqli_error($koneksi));
                    throw new Exception("Gagal menyimpan hasil prediksi");
                }

            } catch (Exception $e) {
                error_log("Error saat prediksi otomatis: " . $e->getMessage());
                // Jika prediksi gagal, tetap redirect ke halaman sukses tanpa hasil prediksi
                // Nasabah masih bisa diproses manual oleh analis
                header("Location: success.php?id_nasabah=" . $id_nasabah . "&prediction_error=" . urlencode($e->getMessage()));
                exit;
            }
        } else {
            $error = true;
            $error_message = "Terjadi kesalahan saat menyimpan data: " . mysqli_error($koneksi);
        }

        // Tutup statement
        mysqli_stmt_close($stmt);
    }
}

// Jika terjadi error, redirect kembali ke halaman form dengan pesan error
if ($error) {
    header("Location: apply.php?error=" . urlencode($error_message));
    exit;
}
?>
