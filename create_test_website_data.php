<?php
// Script untuk membuat data test nasabah website

include 'koneksi.php';

echo "<h2>Membuat Data Test Nasabah Website</h2>\n";

// Data test nasabah website
$test_data = [
    [
        'nama_nasabah' => '<PERSON><PERSON> (Website)',
        'jenis_kelamin' => 'Laki-laki',
        'status_perkawinan' => 'Menikah',
        'pekerjaan' => 'Swasta',
        'penghasilan' => 8000000,
        'jumlah_tanggungan' => 3,
        'jumlah_pinjaman' => 50000000,
        'jangka_waktu' => 24,
        'jaminan' => 'BPKB Motor',
        'tahun_kendaraan' => 2019,
        'status_pajak' => 'Aktif',
        'kepemilikan_rumah' => 'Milik Sendiri',
        'umur' => 35,
        'tujuan_pinjaman' => 'Pengajuan Online Website Leasing',
        'sumber_data' => 'website'
    ],
    [
        'nama_nasabah' => '<PERSON><PERSON> (Website)',
        'jenis_kelamin' => 'Perempuan',
        'status_perkawinan' => 'Menikah',
        'pekerjaan' => 'PNS',
        'penghasilan' => 6500000,
        'jumlah_tanggungan' => 2,
        'jumlah_pinjaman' => 30000000,
        'jangka_waktu' => 18,
        'jaminan' => 'BPKB Mobil',
        'tahun_kendaraan' => 2020,
        'status_pajak' => 'Aktif',
        'kepemilikan_rumah' => 'Milik Sendiri',
        'umur' => 32,
        'tujuan_pinjaman' => 'Pengajuan Online Website Leasing',
        'sumber_data' => 'website'
    ],
    [
        'nama_nasabah' => 'Ahmad Wijaya (Website)',
        'jenis_kelamin' => 'Laki-laki',
        'status_perkawinan' => 'Belum Menikah',
        'pekerjaan' => 'Wiraswasta',
        'penghasilan' => 12000000,
        'jumlah_tanggungan' => 1,
        'jumlah_pinjaman' => 75000000,
        'jangka_waktu' => 36,
        'jaminan' => 'BPKB Motor',
        'tahun_kendaraan' => 2021,
        'status_pajak' => 'Aktif',
        'kepemilikan_rumah' => 'Kontrak',
        'umur' => 28,
        'tujuan_pinjaman' => 'Pengajuan Online Website Leasing',
        'sumber_data' => 'website'
    ]
];

$sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, pekerjaan, penghasilan, jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak, kepemilikan_rumah, umur, tujuan_pinjaman, sumber_data) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$berhasil = 0;
$gagal = 0;

foreach ($test_data as $data) {
    $stmt = mysqli_prepare($koneksi, $sql);
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "ssssdidissssis",
            $data['nama_nasabah'],
            $data['jenis_kelamin'],
            $data['status_perkawinan'],
            $data['pekerjaan'],
            $data['penghasilan'],
            $data['jumlah_tanggungan'],
            $data['jumlah_pinjaman'],
            $data['jangka_waktu'],
            $data['jaminan'],
            $data['tahun_kendaraan'],
            $data['status_pajak'],
            $data['kepemilikan_rumah'],
            $data['umur'],
            $data['tujuan_pinjaman'],
            $data['sumber_data']
        );
        
        if (mysqli_stmt_execute($stmt)) {
            $id = mysqli_insert_id($koneksi);
            echo "✅ Berhasil menambahkan: " . htmlspecialchars($data['nama_nasabah']) . " (ID: $id)<br>\n";
            $berhasil++;
        } else {
            echo "❌ Gagal menambahkan: " . htmlspecialchars($data['nama_nasabah']) . " - " . mysqli_error($koneksi) . "<br>\n";
            $gagal++;
        }
        mysqli_stmt_close($stmt);
    } else {
        echo "❌ Error prepare statement untuk: " . htmlspecialchars($data['nama_nasabah']) . "<br>\n";
        $gagal++;
    }
}

echo "<hr>\n";
echo "<h3>Ringkasan:</h3>\n";
echo "✅ Berhasil: $berhasil data<br>\n";
echo "❌ Gagal: $gagal data<br>\n";

// Tampilkan data nasabah website yang belum diprediksi
echo "<h3>Data Nasabah Website yang Belum Diprediksi:</h3>\n";
$query = "SELECT n.id_nasabah, n.nama_nasabah, n.penghasilan, n.jumlah_pinjaman, n.sumber_data, n.created_at
          FROM nasabah n
          LEFT JOIN hasil_prediksi hp ON n.id_nasabah = hp.id_nasabah
          WHERE n.sumber_data = 'website' AND hp.id_prediksi IS NULL
          ORDER BY n.created_at DESC";

$result = mysqli_query($koneksi, $query);

if ($result && mysqli_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>Nama</th><th>Penghasilan</th><th>Pinjaman</th><th>Sumber</th><th>Tanggal</th>";
    echo "</tr>\n";
    
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id_nasabah'] . "</td>";
        echo "<td>" . htmlspecialchars($row['nama_nasabah']) . "</td>";
        echo "<td>Rp " . number_format($row['penghasilan'], 0, ',', '.') . "</td>";
        echo "<td>Rp " . number_format($row['jumlah_pinjaman'], 0, ',', '.') . "</td>";
        echo "<td>" . $row['sumber_data'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>ℹ️ Tidak ada data nasabah website yang belum diprediksi</p>\n";
}

echo "<hr>\n";
echo "<h3>Test Halaman:</h3>\n";
echo "<p>";
echo "<a href='prediksi_nasabah.php' style='margin-right: 10px;'>🌐 Test Prediksi Nasabah Website</a>";
echo "<a href='prediksi_baru.php' style='margin-right: 10px;'>📝 Test Prediksi Baru Manual</a>";
echo "<a href='daftar_kelayakan.php' style='margin-right: 10px;'>📊 Daftar Kelayakan</a>";
echo "<a href='debug_prediksi_baru.php' style='margin-right: 10px;'>🔍 Debug Prediksi</a>";
echo "</p>\n";

// Statistik sumber data
echo "<h3>Statistik Sumber Data:</h3>\n";
$stats_query = "SELECT sumber_data, COUNT(*) as jumlah FROM nasabah GROUP BY sumber_data";
$stats_result = mysqli_query($koneksi, $stats_query);

if ($stats_result) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr style='background: #f0f0f0;'><th>Sumber Data</th><th>Jumlah</th></tr>\n";
    while ($row = mysqli_fetch_assoc($stats_result)) {
        echo "<tr><td>" . $row['sumber_data'] . "</td><td>" . $row['jumlah'] . "</td></tr>\n";
    }
    echo "</table>\n";
}
?>
