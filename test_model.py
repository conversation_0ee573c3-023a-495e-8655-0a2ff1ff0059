#!/usr/bin/env python3
"""
Script untuk test model dan melihat fitur yang diharapkan
"""

import os
import pickle
import pandas as pd
import numpy as np
from datetime import datetime

# Path ke model
MODEL_DIR = os.path.join(os.path.dirname(__file__), 'model')
MODEL_PATH = os.path.join(MODEL_DIR, 'backpropagation_model.pkl')

def test_model():
    """Test model dan lihat fitur yang diharapkan"""
    try:
        print("=== TEST MODEL ===")
        print(f"Loading model from: {MODEL_PATH}")
        
        # Load model
        with open(MODEL_PATH, 'rb') as f:
            model = pickle.load(f)
        
        print(f"Model type: {type(model)}")
        
        # Jika model adalah pipeline, lihat komponennya
        if hasattr(model, 'named_steps'):
            print("Pipeline steps:", list(model.named_steps.keys()))
            
            # Lihat preprocessor
            if 'preprocessor' in model.named_steps:
                preprocessor = model.named_steps['preprocessor']
                print(f"Preprocessor type: {type(preprocessor)}")
                
                # Lihat transformers
                if hasattr(preprocessor, 'transformers_'):
                    print("Transformers:")
                    for name, transformer, features in preprocessor.transformers_:
                        print(f"  {name}: {type(transformer)} on features {features}")
                
                # Coba dapatkan feature names
                try:
                    feature_names = preprocessor.get_feature_names_out()
                    print(f"Total features after preprocessing: {len(feature_names)}")
                    print("Feature names:", list(feature_names))
                except Exception as e:
                    print(f"Could not get feature names: {e}")
        
        # Test dengan data sample
        print("\n=== TEST PREDICTION ===")
        
        # Data sample sesuai dengan yang diharapkan model
        sample_data = pd.DataFrame([{
            'umur': 35,
            'jenis_kelamin': 'Laki-laki',
            'pekerjaan': 'PNS',
            'penghasilan': 8000000,
            'jumlah_tanggungan': 2,
            'jumlah_pinjaman': 50000000,
            'jangka_waktu': 24,
            'kepemilikan_rumah': 'Milik Sendiri',
            'jaminan': 'BPKB Mobil',
            'tahun_kendaraan': 2020,
            'status_pajak': 'Aktif',
            'tujuan_pinjaman': 'Kebutuhan Pribadi',
            'umur_kendaraan': datetime.now().year - 2020,
            'rasio_pinjaman_penghasilan': 50000000 / 8000000,
            'penghasilan_per_tanggungan': 8000000 / max(1, 2)
        }])
        
        print("Sample data:")
        print(sample_data.iloc[0].to_dict())
        
        # Test prediction
        prediction = model.predict(sample_data)
        probability = model.predict_proba(sample_data)
        
        print(f"Prediction: {prediction}")
        print(f"Probability: {probability}")
        print(f"Result: {'Layak' if prediction[0] == 1 else 'Tidak Layak'}")
        print(f"Confidence: {probability[0][1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_model()
