<?php
// Mulai session jika belum dimulai
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
include 'koneksi.php';

// Cek apakah user sudah login
if (!isset($_SESSION['username']) || !isset($_SESSION['level']) || $_SESSION['level'] != 'admin') {
    header("Location: login.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>SISTEM PREDIKSI KELAYAKAN KREDIT</title>

        <!-- Bootstrap Core CSS -->
        <link href="assets/css/bootstrap.min.css" rel="stylesheet">

        <!-- MetisMenu CSS -->
        <link href="assets/css/metisMenu.min.css" rel="stylesheet">

        <!-- Timeline CSS -->
        <link href="assets/css/timeline.css" rel="stylesheet">

        <!-- Custom CSS -->
        <link href="assets/css/startmin.css" rel="stylesheet">

        <!-- Morris Charts CSS -->
        <link href="assets/css/morris.css" rel="stylesheet">

        <!-- Custom Fonts -->
        <link href="assets/css/font-awesome.min.css" rel="stylesheet" type="text/css">

        <!-- DataTables CSS -->
    <link href="assets/css/dataTables/dataTables.bootstrap.css" rel="stylesheet">

    </head>
    <body>
        <!-- Sidebar overlay for mobile -->
        <div class="sidebar-overlay"></div>

        <div id="wrapper">

            <!-- Navigation -->
            <nav class="navbar navbar-default navbar-fixed-top" role="navigation">
                <div class="navbar-header">
                    <!-- Hamburger button for sidebar toggle -->
                    <button type="button" class="navbar-toggle sidebar-toggle" data-toggle="collapse" data-target=".sidebar-collapse">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="index_admin.php">Sistem Prediksi Kelayakan Kredit</a>
                </div>

                <!-- Top Navigation Right -->
                <ul class="nav navbar-right navbar-top-links">
                    <li class="dropdown">
                        <div class="user-info">
                            <i class="fa fa-user-circle"></i>
                            <span><?php echo htmlspecialchars($_SESSION['username'] ?? 'Admin'); ?></span>
                        </div>
                    </li>
                    <li>
                        <a href="logout.php" title="Logout">
                            <i class="fa fa-sign-out"></i>
                        </a>
                    </li>
                </ul>

                <!-- Sidebar -->
                <div class="navbar-default sidebar" role="navigation">
                    <div class="sidebar-nav navbar-collapse sidebar-collapse">
                        <ul class="nav" id="side-menu">
                            <li>
                                <a href="index_admin.php"><i class="fa fa-home fa-fw"></i> Dashboard</a>
                            </li>
                            <li>
                                <a href="data_user.php"><i class="fa fa-user fa-fw"></i> Data User</a>
                            </li>
                            <li>
                                <a href="data_nasabah.php"><i class="fa fa-users fa-fw"></i> Data Nasabah</a>
                            </li>
                    </div>
                </div>
            </nav>

        <!-- jQuery -->
        <script src="assets/js/jquery.min.js"></script>

        <!-- Bootstrap Core JavaScript -->
        <script src="assets/js/bootstrap.min.js"></script>

        <!-- Metis Menu Plugin JavaScript -->
        <script src="assets/js/metisMenu.min.js"></script>

        <!-- Custom Theme JavaScript -->
        <script src="assets/js/startmin.js"></script>

        <!-- Custom Sidebar JavaScript -->
        <script src="assets/js/custom-sidebar.js"></script>
    </body>
</html>