<?php
// Include file koneksi dan helper
include 'koneksi.php';
include 'date_helper.php';

// Tampilkan header
echo "<h1>Sinkronisasi Tanggal Prediksi</h1>";

// Ambil semua ID nasabah yang memiliki prediksi
$query_nasabah = "SELECT DISTINCT id_nasabah FROM prediksi_detail";
$result_nasabah = mysqli_query($koneksi, $query_nasabah);

$success_count = 0;
$error_count = 0;

// Proses setiap nasabah
while ($row = mysqli_fetch_assoc($result_nasabah)) {
    $id_nasabah = $row['id_nasabah'];
    
    // Ambil nama nasabah
    $query_nama = "SELECT nama_nasabah FROM nasabah WHERE id_nasabah = ?";
    $stmt_nama = $koneksi->prepare($query_nama);
    $stmt_nama->bind_param("i", $id_nasabah);
    $stmt_nama->execute();
    $result_nama = $stmt_nama->get_result();
    $nama_data = $result_nama->fetch_assoc();
    $nama_nasabah = $nama_data ? $nama_data['nama_nasabah'] : "ID: $id_nasabah";
    
    echo "<h3>Menyinkronkan data untuk nasabah: $nama_nasabah</h3>";
    
    // Tampilkan data sebelum sinkronisasi
    echo "<h4>Data Sebelum Sinkronisasi:</h4>";
    
    // Ambil data prediksi untuk nasabah tersebut
    $query_before = "SELECT pd.id_prediksi, pd.tanggal_prediksi as pd_tanggal, 
                           hp.tanggal_prediksi as hp_tanggal, lp.tanggal_prediksi as lp_tanggal
                    FROM prediksi_detail pd
                    LEFT JOIN hasil_prediksi hp ON pd.id_nasabah = hp.id_nasabah
                    LEFT JOIN laporan_prediksi lp ON pd.id_laporan = lp.id_laporan
                    WHERE pd.id_nasabah = ?";
    $stmt_before = $koneksi->prepare($query_before);
    $stmt_before->bind_param("i", $id_nasabah);
    $stmt_before->execute();
    $result_before = $stmt_before->get_result();
    $data_before = $result_before->fetch_assoc();
    
    if ($data_before) {
        echo "<table border='1'>";
        echo "<tr>
                <th>ID Prediksi</th>
                <th>prediksi_detail.tanggal_prediksi</th>
                <th>hasil_prediksi.tanggal_prediksi</th>
                <th>laporan_prediksi.tanggal_prediksi</th>
              </tr>";
        echo "<tr>";
        echo "<td>" . $data_before['id_prediksi'] . "</td>";
        echo "<td>" . ($data_before['pd_tanggal'] ? format_tanggal($data_before['pd_tanggal']) : 'N/A') . "</td>";
        echo "<td>" . ($data_before['hp_tanggal'] ? format_tanggal($data_before['hp_tanggal']) : 'N/A') . "</td>";
        echo "<td>" . ($data_before['lp_tanggal'] ? format_tanggal($data_before['lp_tanggal']) : 'N/A') . "</td>";
        echo "</tr>";
        echo "</table>";
        
        // Sinkronkan tanggal prediksi
        $result = sync_prediction_dates($id_nasabah);
        
        if ($result) {
            $success_count++;
            echo "<p style='color: green;'>Sinkronisasi berhasil!</p>";
            
            // Tampilkan data setelah sinkronisasi
            echo "<h4>Data Setelah Sinkronisasi:</h4>";
            
            // Ambil data prediksi untuk nasabah tersebut
            $stmt_before->execute();
            $result_after = $stmt_before->get_result();
            $data_after = $result_after->fetch_assoc();
            
            if ($data_after) {
                echo "<table border='1'>";
                echo "<tr>
                        <th>ID Prediksi</th>
                        <th>prediksi_detail.tanggal_prediksi</th>
                        <th>hasil_prediksi.tanggal_prediksi</th>
                        <th>laporan_prediksi.tanggal_prediksi</th>
                      </tr>";
                echo "<tr>";
                echo "<td>" . $data_after['id_prediksi'] . "</td>";
                echo "<td>" . ($data_after['pd_tanggal'] ? format_tanggal($data_after['pd_tanggal']) : 'N/A') . "</td>";
                echo "<td>" . ($data_after['hp_tanggal'] ? format_tanggal($data_after['hp_tanggal']) : 'N/A') . "</td>";
                echo "<td>" . ($data_after['lp_tanggal'] ? format_tanggal($data_after['lp_tanggal']) : 'N/A') . "</td>";
                echo "</tr>";
                echo "</table>";
            }
        } else {
            $error_count++;
            echo "<p style='color: red;'>Sinkronisasi gagal!</p>";
        }
    } else {
        $error_count++;
        echo "<p style='color: red;'>Tidak ada data prediksi untuk nasabah ini.</p>";
    }
    
    echo "<hr>";
}

// Tampilkan ringkasan
echo "<h2>Ringkasan Sinkronisasi</h2>";
echo "<p>Total nasabah yang diproses: " . ($success_count + $error_count) . "</p>";
echo "<p>Berhasil: $success_count</p>";
echo "<p>Gagal: $error_count</p>";

// Tampilkan tombol kembali
echo "<p><a href='index.php'>Kembali ke Halaman Utama</a></p>";
?>
