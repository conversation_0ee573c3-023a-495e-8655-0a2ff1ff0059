#!/usr/bin/env python
# coding: utf-8

# # Evaluasi Model Backpropagation
# 
# Script ini mengevaluasi model backpropagation yang telah disimpan dengan dataset_nasabah.csv

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
import pickle
import os
import re
from datetime import datetime

# Set random seed untuk reproduksibilitas
np.random.seed(42)

# Fungsi untuk preprocessing data
def format_rupiah(value):
    """Mengubah string Rupiah menjadi nilai numerik"""
    if isinstance(value, str):
        # Hapus 'Rp', titik, dan spasi
        value = value.replace('Rp', '').replace('.', '').replace(' ', '')
        # Hapus karakter non-numerik
        value = re.sub(r'[^\d]', '', value)
        if value:
            return float(value)
    return float(value) if value else 0

def extract_number_from_age(age_str):
    """Mengekstrak angka dari string umur"""
    if isinstance(age_str, str):
        match = re.search(r'(\d+)', age_str)
        if match:
            return int(match.group(1))
    return int(age_str) if age_str else 0

def preprocess_dataset(df):
    """Preprocessing dataset"""
    # Buat salinan dataframe
    df_processed = df.copy()
    
    # Ekstrak umur dari string
    if 'umur' in df_processed.columns:
        df_processed['umur'] = df_processed['umur'].apply(extract_number_from_age)
    
    # Konversi nilai Rupiah ke numerik
    if 'penghasilan' in df_processed.columns:
        df_processed['penghasilan'] = df_processed['penghasilan'].apply(format_rupiah)
    
    if 'jumlah_pinjaman' in df_processed.columns:
        df_processed['jumlah_pinjaman'] = df_processed['jumlah_pinjaman'].apply(format_rupiah)
    
    # Konversi waktu pengembalian ke numerik
    if 'waktu_pengembalian' in df_processed.columns:
        df_processed['waktu_pengembalian'] = pd.to_numeric(df_processed['waktu_pengembalian'], errors='coerce')
    
    # Konversi tahun kendaraan ke numerik
    if 'tahun_kendaraan' in df_processed.columns:
        df_processed['tahun_kendaraan'] = pd.to_numeric(df_processed['tahun_kendaraan'], errors='coerce')
    
    # Tambahkan fitur turunan
    if 'penghasilan' in df_processed.columns and 'jumlah_tanggungan' in df_processed.columns:
        df_processed['jumlah_tanggungan'] = pd.to_numeric(df_processed['jumlah_tanggungan'], errors='coerce').fillna(1)
        df_processed['penghasilan_per_kapita'] = df_processed['penghasilan'] / df_processed['jumlah_tanggungan'].clip(lower=1)
    
    if 'penghasilan' in df_processed.columns and 'jumlah_pinjaman' in df_processed.columns:
        df_processed['rasio_pinjaman'] = df_processed['jumlah_pinjaman'] / (df_processed['penghasilan'] * 12)
    
    if 'penghasilan' in df_processed.columns and 'waktu_pengembalian' in df_processed.columns and 'jumlah_pinjaman' in df_processed.columns:
        # Estimasi angsuran bulanan (dengan bunga flat 1% per bulan)
        bunga_bulanan = 0.01  # 1% per bulan
        df_processed['waktu_pengembalian'] = df_processed['waktu_pengembalian'].fillna(12)
        df_processed['total_bunga'] = df_processed['jumlah_pinjaman'] * bunga_bulanan * df_processed['waktu_pengembalian']
        df_processed['total_pembayaran'] = df_processed['jumlah_pinjaman'] + df_processed['total_bunga']
        df_processed['angsuran_bulanan'] = df_processed['total_pembayaran'] / df_processed['waktu_pengembalian']
        df_processed['rasio_angsuran'] = df_processed['angsuran_bulanan'] / df_processed['penghasilan']
    
    # Tambahkan fitur umur kendaraan
    current_year = datetime.now().year
    if 'tahun_kendaraan' in df_processed.columns:
        df_processed['umur_kendaraan'] = current_year - df_processed['tahun_kendaraan']
    
    return df_processed

# Memuat dataset
try:
    # Coba memuat dengan berbagai delimiter
    try:
        df = pd.read_csv('dataset_nasabah.csv', sep=';')
    except:
        try:
            df = pd.read_csv('model/dataset_nasabah.csv', sep=';')
        except:
            try:
                df = pd.read_csv('dataset_nasabah.csv', sep=',')
            except:
                df = pd.read_csv('model/dataset_nasabah.csv', sep=',')
    
    print(f"Dataset berhasil dimuat dengan {df.shape[0]} baris dan {df.shape[1]} kolom")
except Exception as e:
    print(f"Error saat memuat dataset: {e}")
    raise

# Preprocessing dataset
df_processed = preprocess_dataset(df)

# Tampilkan informasi dataset
print("\nInformasi Dataset setelah preprocessing:")
print(df_processed.info())

# Tampilkan statistik deskriptif
print("\nStatistik Deskriptif:")
print(df_processed.describe())

# Periksa distribusi kelas target
print("\nDistribusi Kelas Target (Kelayakan):")
print(df_processed['kelayakan'].value_counts())
print(df_processed['kelayakan'].value_counts(normalize=True) * 100)

# Visualisasi distribusi kelas target
plt.figure(figsize=(10, 6))
sns.countplot(x='kelayakan', data=df_processed)
plt.title('Distribusi Kelas Target (Kelayakan)')
plt.savefig('model/distribusi_kelayakan.png')
plt.close()

# Pisahkan fitur dan target
X = df_processed.drop('kelayakan', axis=1)
y = df_processed['kelayakan']

# Hapus kolom yang tidak relevan untuk model
columns_to_drop = []
for col in X.columns:
    if col.startswith('Unnamed:'):
        columns_to_drop.append(col)

X = X.drop(columns=columns_to_drop, errors='ignore')

# Split data menjadi training dan testing
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

# Memuat model yang telah disimpan
try:
    model_path = 'model/backpropagation_model.pkl'
    with open(model_path, 'rb') as file:
        model = pickle.load(file)
    print(f"\nModel berhasil dimuat dari {model_path}")
except Exception as e:
    print(f"Error saat memuat model: {e}")
    raise

# Evaluasi model
y_pred = model.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred, pos_label='Layak')
recall = recall_score(y_test, y_pred, pos_label='Layak')
f1 = f1_score(y_test, y_pred, pos_label='Layak')

print("\nHasil Evaluasi Model:")
print(f"Akurasi: {accuracy:.4f}")
print(f"Precision: {precision:.4f}")
print(f"Recall: {recall:.4f}")
print(f"F1 Score: {f1:.4f}")

# Confusion Matrix
cm = confusion_matrix(y_test, y_pred)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['Tidak Layak', 'Layak'],
            yticklabels=['Tidak Layak', 'Layak'])
plt.xlabel('Prediksi')
plt.ylabel('Aktual')
plt.title('Confusion Matrix - 80% Training / 20% Testing')
plt.savefig('model/confusion_matrix_80_20.png')
plt.close()

# Classification Report
print("\nClassification Report:")
print(classification_report(y_test, y_pred))

# Simpan hasil evaluasi ke file
with open('model/evaluasi_model.txt', 'w') as file:
    file.write("EVALUASI MODEL BACKPROPAGATION\n")
    file.write("="*50 + "\n\n")
    file.write("Hasil Evaluasi Model:\n")
    file.write(f"Akurasi: {accuracy:.4f}\n")
    file.write(f"Precision: {precision:.4f}\n")
    file.write(f"Recall: {recall:.4f}\n")
    file.write(f"F1 Score: {f1:.4f}\n\n")
    file.write("Classification Report:\n")
    file.write(classification_report(y_test, y_pred) + "\n\n")
    file.write("Informasi Model:\n")
    file.write("- Arsitektur: MLP dengan hidden layer (10, 5)\n")
    file.write("- Fungsi aktivasi: ReLU\n")
    file.write("- Optimizer: Adam\n")
    file.write("- Learning rate: adaptive\n")
    file.write("- Maksimum iterasi: 1000\n")

print("\nHasil evaluasi telah disimpan ke model/evaluasi_model.txt")
