<?php

// Include file koneksi database dan helper
include 'koneksi.php';
include 'date_helper.php';

// Pastikan session sudah dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include file cek session untuk memastikan user sudah login
include 'cek_session.php';

// Cek akses: semua user yang sudah login boleh mengakses halaman ini
cek_akses(['analis']);

// Include file navigasi untuk analis
require_once 'nav_analis.php';

// Cek apakah ada pesan sukses
$success_message = '';
if (isset($_GET['success'])) {
    $success_message = $_GET['success'];
}

// Cek apakah ada parameter id_nasabah dan id_prediksi
if (!isset($_GET['id_nasabah']) || !isset($_GET['id_prediksi'])) {
    // Tambahkan log untuk debugging
    error_log("Parameter tidak lengkap: id_nasabah=" . ($_GET['id_nasabah'] ?? 'NULL') . ", id_prediksi=" . ($_GET['id_prediksi'] ?? 'NULL'));

    // Redirect ke halaman prediksi kelayakan jika parameter tidak lengkap
    echo "<script>alert('Parameter tidak lengkap. Anda akan dialihkan ke halaman prediksi kelayakan.'); window.location.href='prediksi_website.php';</script>";
    exit;
}

// Tambahkan log untuk debugging
error_log("Halaman hasil_prediksi.php dipanggil dengan id_nasabah={$_GET['id_nasabah']} dan id_prediksi={$_GET['id_prediksi']}");

$id_nasabah = $_GET['id_nasabah'];
$id_prediksi = $_GET['id_prediksi'];

// Ambil data nasabah
$query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
$stmt_nasabah = $koneksi->prepare($query_nasabah);
$stmt_nasabah->bind_param("i", $id_nasabah);
$stmt_nasabah->execute();
$result_nasabah = $stmt_nasabah->get_result();
$nasabah = $result_nasabah->fetch_assoc();

// Coba ambil data prediksi dari tabel prediksi_detail terlebih dahulu (lebih akurat)
$query_prediksi_detail = "SELECT pd.*, lp.parameter, lp.akurasi
                         FROM prediksi_detail pd
                         LEFT JOIN laporan_prediksi lp ON pd.id_laporan = lp.id_laporan
                         WHERE pd.id_prediksi = ?";
$stmt_prediksi_detail = $koneksi->prepare($query_prediksi_detail);
$stmt_prediksi_detail->bind_param("i", $id_prediksi);
$stmt_prediksi_detail->execute();
$result_prediksi_detail = $stmt_prediksi_detail->get_result();
$prediksi = $result_prediksi_detail->fetch_assoc();

// Log untuk debugging
error_log("Data prediksi dari prediksi_detail: " . ($prediksi ? json_encode($prediksi) : "Tidak ditemukan"));

// Jika tidak ditemukan di prediksi_detail, coba ambil dari hasil_prediksi
if (!$prediksi) {
    $query_hasil_prediksi = "SELECT * FROM hasil_prediksi WHERE id_prediksi = ?";
    $stmt_hasil_prediksi = $koneksi->prepare($query_hasil_prediksi);
    $stmt_hasil_prediksi->bind_param("i", $id_prediksi);
    $stmt_hasil_prediksi->execute();
    $result_hasil_prediksi = $stmt_hasil_prediksi->get_result();
    $prediksi = $result_hasil_prediksi->fetch_assoc();

    // Log untuk debugging
    error_log("Data prediksi dari hasil_prediksi: " . ($prediksi ? json_encode($prediksi) : "Tidak ditemukan"));
}

// Jika tidak ditemukan dengan ID prediksi, coba cari berdasarkan ID nasabah (ambil yang terbaru)
if (!$prediksi && $id_nasabah) {
    $query_hasil_by_nasabah = "SELECT * FROM hasil_prediksi WHERE id_nasabah = ? ORDER BY id_prediksi DESC LIMIT 1";
    $stmt_hasil_by_nasabah = $koneksi->prepare($query_hasil_by_nasabah);
    $stmt_hasil_by_nasabah->bind_param("i", $id_nasabah);
    $stmt_hasil_by_nasabah->execute();
    $result_hasil_by_nasabah = $stmt_hasil_by_nasabah->get_result();
    $prediksi = $result_hasil_by_nasabah->fetch_assoc();

    // Update ID prediksi jika ditemukan
    if ($prediksi) {
        $id_prediksi = $prediksi['id_prediksi'];
        error_log("Data prediksi ditemukan berdasarkan ID nasabah. ID prediksi baru: " . $id_prediksi);
    }

    // Log untuk debugging
    error_log("Data prediksi dari hasil_prediksi by nasabah: " . ($prediksi ? json_encode($prediksi) : "Tidak ditemukan"));
}

// Jika tidak ditemukan di hasil_prediksi, coba cari di prediksi_detail lagi (dengan query berbeda)
if (!$prediksi) {
    $query_prediksi = "SELECT pd.*, lp.akurasi, lp.parameter
                      FROM prediksi_detail pd
                      LEFT JOIN laporan_prediksi lp ON pd.id_laporan = lp.id_laporan
                      WHERE pd.id_prediksi = ?";
    $stmt_prediksi = $koneksi->prepare($query_prediksi);
    $stmt_prediksi->bind_param("i", $id_prediksi);
    $stmt_prediksi->execute();
    $result_prediksi = $stmt_prediksi->get_result();
    $prediksi = $result_prediksi->fetch_assoc();

    // Log untuk debugging
    error_log("Data prediksi dari prediksi_detail (query 2): " . ($prediksi ? json_encode($prediksi) : "Tidak ditemukan"));
}

// Tambahkan debugging
if (!$prediksi) {
    error_log("Prediksi tidak ditemukan untuk ID: $id_prediksi. Mencoba query sederhana.");

    // Coba ambil data prediksi tanpa join dari prediksi_detail
    $query_prediksi_simple = "SELECT * FROM prediksi_detail WHERE id_prediksi = ?";
    $stmt_prediksi_simple = $koneksi->prepare($query_prediksi_simple);
    $stmt_prediksi_simple->bind_param("i", $id_prediksi);
    $stmt_prediksi_simple->execute();
    $result_prediksi_simple = $stmt_prediksi_simple->get_result();
    $prediksi = $result_prediksi_simple->fetch_assoc();

    // Log untuk debugging
    error_log("Data prediksi dari prediksi_detail (query simple): " . ($prediksi ? json_encode($prediksi) : "Tidak ditemukan"));

    // Jika masih tidak ditemukan, coba ambil dari hasil_prediksi
    if (!$prediksi) {
        $query_hasil_simple = "SELECT * FROM hasil_prediksi WHERE id_prediksi = ?";
        $stmt_hasil_simple = $koneksi->prepare($query_hasil_simple);
        $stmt_hasil_simple->bind_param("i", $id_prediksi);
        $stmt_hasil_simple->execute();
        $result_hasil_simple = $stmt_hasil_simple->get_result();
        $prediksi = $result_hasil_simple->fetch_assoc();

        // Log untuk debugging
        error_log("Data prediksi dari hasil_prediksi (query simple): " . ($prediksi ? json_encode($prediksi) : "Tidak ditemukan"));
    }

    // Jika masih tidak ada data, coba ambil data nasabah dan buat prediksi baru
    if (!$prediksi && $nasabah) {
        error_log("Membuat prediksi baru untuk nasabah ID: $id_nasabah");

        // Include file api_predict.php untuk menggunakan fungsi prediksi
        require_once 'api_predict.php';

        // Lakukan prediksi dengan model backpropagation
        try {
            $hasil_prediksi = prediksi_dengan_backpropagation($nasabah);
            error_log("Hasil prediksi: " . print_r($hasil_prediksi, true));
        } catch (Exception $e) {
            error_log("Error saat melakukan prediksi: " . $e->getMessage());
            $hasil_prediksi = [
                'hasil_prediksi' => 'Tidak Layak',
                'probabilitas' => 0.5,
                'keterangan' => 'Gagal melakukan prediksi: ' . $e->getMessage(),
                'skor' => 0,
                'max_skor' => 100
            ];
        }

        // Simpan hasil prediksi
        $id_prediksi_baru = simpan_hasil_prediksi($nasabah, $hasil_prediksi);
        error_log("ID prediksi baru: $id_prediksi_baru");

        // Ambil data prediksi yang baru dibuat
        $query_prediksi_new = "SELECT * FROM prediksi_detail WHERE id_prediksi = ?";
        $stmt_prediksi_new = $koneksi->prepare($query_prediksi_new);
        $stmt_prediksi_new->bind_param("i", $id_prediksi_baru);
        $stmt_prediksi_new->execute();
        $result_prediksi_new = $stmt_prediksi_new->get_result();
        $prediksi = $result_prediksi_new->fetch_assoc();

        error_log("Data prediksi baru: " . ($prediksi ? "Ditemukan" : "Tidak ditemukan"));

        // Update ID prediksi
        $id_prediksi = $id_prediksi_baru;
    }
}

// Jika masih tidak ada data prediksi, coba buat prediksi baru
if (!$prediksi) {
    error_log("Mencoba membuat prediksi baru untuk nasabah ID: $id_nasabah");

    // Coba ambil data nasabah dan buat prediksi baru
    if ($nasabah) {
        // Include file api_predict.php untuk menggunakan fungsi prediksi
        require_once 'api_predict.php';

        // Lakukan prediksi dengan model backpropagation
        try {
            $hasil_prediksi = prediksi_dengan_backpropagation($nasabah);
            error_log("Hasil prediksi baru: " . print_r($hasil_prediksi, true));
        } catch (Exception $e) {
            error_log("Error saat melakukan prediksi: " . $e->getMessage());
            $hasil_prediksi = [
                'hasil_prediksi' => 'Tidak Layak',
                'probabilitas' => 0.5,
                'keterangan' => 'Gagal melakukan prediksi: ' . $e->getMessage(),
                'skor' => 0,
                'max_skor' => 100
            ];
        }

        // Simpan hasil prediksi
        $id_prediksi_baru = simpan_hasil_prediksi($nasabah, $hasil_prediksi);
        error_log("ID prediksi baru: $id_prediksi_baru");

        // Ambil data prediksi yang baru dibuat
        $query_prediksi_new = "SELECT * FROM prediksi_detail WHERE id_prediksi = ?";
        $stmt_prediksi_new = $koneksi->prepare($query_prediksi_new);
        $stmt_prediksi_new->bind_param("i", $id_prediksi_baru);
        $stmt_prediksi_new->execute();
        $result_prediksi_new = $stmt_prediksi_new->get_result();
        $prediksi = $result_prediksi_new->fetch_assoc();

        error_log("Data prediksi baru: " . ($prediksi ? "Ditemukan" : "Tidak ditemukan"));

        // Update ID prediksi
        if ($prediksi) {
            $id_prediksi = $id_prediksi_baru;
        }
    }

    // Jika masih tidak ada data prediksi, buat data dummy
    if (!$prediksi) {
        error_log("Membuat data prediksi dummy");
        $prediksi = [
            'id_prediksi' => $id_prediksi,
            'id_nasabah' => $id_nasabah,
            'hasil_prediksi' => 'Tidak Layak', // Default ke Tidak Layak untuk keamanan
            'probabilitas' => 0.5,
            'keterangan' => 'Data prediksi tidak ditemukan. Ini adalah hasil prediksi default.',
            'tanggal_prediksi' => date('Y-m-d H:i:s'),
            'akurasi' => 87.5,
            'parameter' => 'Backpropagation Neural Network'
        ];

        // Coba cek di database hasil_prediksi
        $query_check = "SELECT hasil_prediksi, probabilitas FROM hasil_prediksi WHERE id_prediksi = ?";
        $stmt_check = $koneksi->prepare($query_check);
        $stmt_check->bind_param("i", $id_prediksi);
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();
        $data_check = $result_check->fetch_assoc();

        if ($data_check) {
            error_log("Data ditemukan di hasil_prediksi: " . json_encode($data_check));
            $prediksi['hasil_prediksi'] = $data_check['hasil_prediksi'];
            $prediksi['probabilitas'] = $data_check['probabilitas'];
        }
    }
}

// Jika data nasabah atau prediksi tidak ditemukan
if (!$nasabah || !$prediksi) {
    error_log("Data nasabah atau prediksi tidak ditemukan: nasabah=" . ($nasabah ? "Ada" : "Tidak ada") . ", prediksi=" . ($prediksi ? "Ada" : "Tidak ada"));

    exit;
}

// Pastikan hasil prediksi yang ditampilkan konsisten dengan yang ada di database
// Tambahkan log untuk melihat hasil prediksi yang akan ditampilkan
error_log("Hasil prediksi yang akan ditampilkan: " . $prediksi['hasil_prediksi'] . " dengan probabilitas " . $prediksi['probabilitas']);

// Verifikasi hasil prediksi dengan database
$query_verifikasi = "SELECT hasil_prediksi, probabilitas, tanggal_prediksi FROM hasil_prediksi WHERE id_prediksi = ?";
$stmt_verifikasi = $koneksi->prepare($query_verifikasi);
$stmt_verifikasi->bind_param("i", $id_prediksi);
$stmt_verifikasi->execute();
$result_verifikasi = $stmt_verifikasi->get_result();
$data_verifikasi = $result_verifikasi->fetch_assoc();

// Log data verifikasi
error_log("Data verifikasi dari database: " . ($data_verifikasi ? json_encode($data_verifikasi) : "Tidak ditemukan"));

// Sinkronkan tanggal prediksi jika berbeda
if ($data_verifikasi && isset($data_verifikasi['tanggal_prediksi']) && isset($prediksi['tanggal_prediksi']) && $data_verifikasi['tanggal_prediksi'] != $prediksi['tanggal_prediksi']) {
    error_log("PERBEDAAN TANGGAL PREDIKSI: di database=" . $data_verifikasi['tanggal_prediksi'] . ", di variabel=" . $prediksi['tanggal_prediksi']);
    // Gunakan tanggal prediksi dari database
    $prediksi['tanggal_prediksi'] = $data_verifikasi['tanggal_prediksi'];
    error_log("Tanggal prediksi dikoreksi menjadi: " . $prediksi['tanggal_prediksi']);

    // Sinkronkan tanggal prediksi di semua tabel
    sync_prediction_dates($id_nasabah);
}

if ($data_verifikasi && $data_verifikasi['hasil_prediksi'] != $prediksi['hasil_prediksi']) {
    error_log("PERBEDAAN HASIL PREDIKSI: di database=" . $data_verifikasi['hasil_prediksi'] . ", di variabel=" . $prediksi['hasil_prediksi']);
    // Gunakan hasil prediksi dari database
    $prediksi['hasil_prediksi'] = $data_verifikasi['hasil_prediksi'];
    error_log("Hasil prediksi dikoreksi menjadi: " . $prediksi['hasil_prediksi']);

    // Update juga di prediksi_detail jika ada
    $query_update_hasil = "UPDATE prediksi_detail SET hasil_prediksi = ? WHERE id_prediksi = ?";
    $stmt_update_hasil = $koneksi->prepare($query_update_hasil);
    $stmt_update_hasil->bind_param("si", $prediksi['hasil_prediksi'], $id_prediksi);
    $stmt_update_hasil->execute();
    error_log("Hasil prediksi diupdate di prediksi_detail: " . $prediksi['hasil_prediksi'] . " untuk ID prediksi " . $id_prediksi);
}

// Ambil data proses perhitungan
$query_proses = "SELECT * FROM proses_perhitungan WHERE id_prediksi = ? ORDER BY waktu_proses";
$stmt_proses = $koneksi->prepare($query_proses);
$stmt_proses->bind_param("i", $id_prediksi);
$stmt_proses->execute();
$result_proses = $stmt_proses->get_result();
$proses_perhitungan = [];
while ($row = $result_proses->fetch_assoc()) {
    $proses_perhitungan[] = $row;
}

// Fungsi untuk mendapatkan keterangan tambahan berdasarkan hasil prediksi
function getKeteranganTambahan($nasabah, $prediksi) {
    $keterangan = [];

    // Pastikan penghasilan dan jumlah pinjaman adalah nilai numerik
    $penghasilan = is_numeric($nasabah['penghasilan']) ? floatval($nasabah['penghasilan']) : floatval(str_replace('.', '', $nasabah['penghasilan']));
    $jumlah_pinjaman = is_numeric($nasabah['jumlah_pinjaman']) ? floatval($nasabah['jumlah_pinjaman']) : floatval(str_replace('.', '', $nasabah['jumlah_pinjaman']));
    $jumlah_tanggungan = isset($nasabah['jumlah_tanggungan']) && $nasabah['jumlah_tanggungan'] > 0 ? intval($nasabah['jumlah_tanggungan']) : 1;
    $penghasilan_per_kapita = $penghasilan / $jumlah_tanggungan;
    $rasio_pinjaman = $jumlah_pinjaman / $penghasilan;

    // Keterangan berdasarkan hasil prediksi
    if ($prediksi['hasil_prediksi'] == 'Layak') {
        // Keterangan untuk nasabah yang LAYAK
        $keterangan[] = "Nasabah memiliki profil kredit yang baik.";

        // Keterangan berdasarkan penghasilan dan jumlah tanggungan
        if ($penghasilan_per_kapita > 5000000) {
            $keterangan[] = "Penghasilan per anggota keluarga di atas rata-rata (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . "), menunjukkan kemampuan membayar yang baik.";
        } else if ($penghasilan_per_kapita > 2000000) {
            $keterangan[] = "Penghasilan per anggota keluarga cukup memadai (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . ").";
        }

        // Keterangan berdasarkan pekerjaan
        if ($nasabah['pekerjaan'] == 'PNS' || $nasabah['pekerjaan'] == 'Profesional') {
            $keterangan[] = "Pekerjaan nasabah memiliki stabilitas yang baik.";
        } else if ($nasabah['pekerjaan'] == 'Wiraswasta') {
            $keterangan[] = "Nasabah memiliki usaha yang dapat menjadi sumber penghasilan yang stabil.";
        }

        // Keterangan berdasarkan jaminan
        if ($nasabah['jaminan'] == 'BPKB Mobil') {
            $keterangan[] = "Nasabah memiliki jaminan kendaraan roda empat yang bernilai tinggi.";
        } elseif ($nasabah['jaminan'] == 'BPKB Motor') {
            $keterangan[] = "Nasabah memiliki jaminan kendaraan roda dua yang cukup memadai.";
        }

        // Keterangan berdasarkan kepemilikan rumah
        if ($nasabah['kepemilikan_rumah'] == 'Milik Sendiri') {
            $keterangan[] = "Nasabah memiliki tempat tinggal sendiri, menunjukkan stabilitas finansial.";
        }
    } else {
        // Keterangan untuk nasabah yang TIDAK LAYAK
        $keterangan[] = "Nasabah memiliki risiko kredit yang tinggi.";

        // Keterangan berdasarkan rasio pinjaman terhadap penghasilan per kapita
        if ($rasio_pinjaman > 24) { // Lebih dari 2 tahun penghasilan
            $keterangan[] = "Rasio pinjaman terhadap penghasilan per kapita yang tinggi, menunjukkan beban keuangan yang berat.";
        }

        // Keterangan berdasarkan jumlah tanggungan
        if ($jumlah_tanggungan > 3) {
            $keterangan[] = "Jumlah tanggungan yang banyak (" . $jumlah_tanggungan . " orang) memengaruhi kemampuan membayar.";
        }

        // Keterangan berdasarkan penghasilan per kapita
        if ($penghasilan_per_kapita < 2000000) {
            $keterangan[] = "Penghasilan per anggota keluarga terlalu rendah (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . ").";
        }

        // Keterangan berdasarkan pekerjaan
        if ($nasabah['pekerjaan'] == 'Wiraswasta' || $nasabah['pekerjaan'] == 'Lainnya') {
            $keterangan[] = "Pekerjaan yang kurang stabil, meningkatkan risiko kredit.";
        }

        // Keterangan berdasarkan jaminan
        if ($nasabah['jaminan'] == 'BPKB Motor') {
            // Cek tahun kendaraan jika ada
            if (isset($nasabah['tahun_kendaraan']) && $nasabah['tahun_kendaraan'] < date('Y') - 7) {
                $keterangan[] = "Jaminan kendaraan bermotor terlalu tua (tahun " . $nasabah['tahun_kendaraan'] . ").";
            } else {
                $keterangan[] = "Nilai jaminan BPKB Motor kurang memadai untuk jumlah pinjaman yang diajukan.";
            }

            // Cek status pajak jika ada
            if (isset($nasabah['status_pajak']) && $nasabah['status_pajak'] == 'Tidak Aktif') {
                $keterangan[] = "Status pajak kendaraan tidak aktif, menunjukkan potensi masalah administratif.";
            }
        }

        // Keterangan berdasarkan kepemilikan rumah
        if ($nasabah['kepemilikan_rumah'] == 'Kontrak') {
            $keterangan[] = "Status kepemilikan rumah yang kurang baik (kontrak), menunjukkan kurangnya aset tetap.";
        } elseif ($nasabah['kepemilikan_rumah'] == 'Orang Tua') {
            $keterangan[] = "Status kepemilikan rumah yang kurang baik (menumpang di rumah orang tua), menunjukkan keterbatasan aset.";
        }
    }

    return $keterangan;
}

// Dapatkan keterangan tambahan
if (!isset($prediksi['hasil_prediksi']) || empty($prediksi['hasil_prediksi'])) {
    error_log("Hasil prediksi tidak valid: " . ($prediksi['hasil_prediksi'] ?? 'NULL'));
    $prediksi['hasil_prediksi'] = 'Tidak Layak'; // Default ke Tidak Layak untuk keamanan
}

// Pastikan probabilitas dalam format yang benar
if (is_string($prediksi['probabilitas'])) {
    $prediksi['probabilitas'] = floatval($prediksi['probabilitas']);
}

// Jika probabilitas terlalu ekstrim (0 atau 1), buat sedikit lebih realistis
if ($prediksi['probabilitas'] >= 0.99) {
    $prediksi['probabilitas'] = 0.99;
} else if ($prediksi['probabilitas'] <= 0.01) {
    $prediksi['probabilitas'] = 0.01;
}

// Pastikan probabilitas konsisten dengan hasil prediksi
if (($prediksi['hasil_prediksi'] == 'Tidak Layak' && $prediksi['probabilitas'] > 0.5) ||
    ($prediksi['hasil_prediksi'] == 'Layak' && $prediksi['probabilitas'] < 0.5)) {
    $prediksi['probabilitas'] = 1 - $prediksi['probabilitas']; // Koreksi probabilitas jika tidak konsisten
}

$keterangan_tambahan = getKeteranganTambahan($nasabah, $prediksi);

// Fungsi untuk memformat angka menjadi format rupiah
function formatRupiah($angka) {
    return "Rp " . number_format($angka, 0, ',', '.');
}

// Fungsi untuk memformat JSON
function formatJson($json) {
    $data = json_decode($json, true);
    if ($data === null) {
        return '<pre>' . htmlspecialchars($json) . '</pre>';
    }

    $json_string = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    return '<pre>' . htmlspecialchars($json_string) . '</pre>';
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Hasil Prediksi Kelayakan Kredit</h1>
                <ol class="breadcrumb">
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="prediksi_website.php">Prediksi Nasabah Website</a></li>
                    <li class="active">Hasil Prediksi</li>
                </ol>
            </div>
        </div>

        <!-- Tampilkan pesan sukses jika ada -->
        <?php if (!empty($success_message)): ?>
        <div class="row">
            <div class="col-lg-12">
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                    <i class="fa fa-check-circle"></i> <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Hasil Prediksi -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-<?php echo $prediksi['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?>">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-<?php echo $prediksi['hasil_prediksi'] == 'Layak' ? 'check' : 'times'; ?>-circle"></i> Hasil Prediksi</h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12 text-center">
                                <h2>Nasabah <strong><?php echo htmlspecialchars($nasabah['nama_nasabah']); ?></strong> dinyatakan:</h2>
                                <h1 style="font-size: 48px; margin: 30px 0;">
                                    <span class="label label-<?php echo $prediksi['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?>">
                                        <?php
                                        // Pastikan hasil prediksi konsisten dengan database
                                        $hasil_prediksi = $prediksi['hasil_prediksi'] ?? 'TIDAK LAYAK';
                                        // Log untuk debugging
                                        error_log("Hasil prediksi yang ditampilkan: " . $hasil_prediksi);
                                        echo strtoupper($hasil_prediksi);
                                        ?>
                                    </span>
                                </h1>
                                <h3>untuk menerima kredit dengan probabilitas
                                    <?php
                                    // Tampilkan probabilitas yang konsisten
                                    $prob = ($prediksi['probabilitas']);

                                    // Pastikan probabilitas dalam format yang benar
                                    if (is_string($prob)) {
                                        $prob = floatval($prob);
                                    }

                                    // Gunakan probabilitas dari algoritma, pastikan dalam format yang benar
                                    // Jika probabilitas terlalu ekstrim (0 atau 1), buat sedikit lebih realistis
                                    if ($prob >= 0.99) {
                                        $prob = 0.99;
                                    } else if ($prob <= 0.01) {
                                        $prob = 0.01;
                                    }

                                    // Pastikan probabilitas konsisten dengan hasil prediksi
                                    if (($prediksi['hasil_prediksi'] == 'Tidak Layak' && $prob > 0.5) ||
                                        ($prediksi['hasil_prediksi'] == 'Layak' && $prob < 0.5)) {
                                        $prob = 1 - $prob; // Koreksi probabilitas jika tidak konsisten
                                    }

                                    // Verifikasi probabilitas dengan database dan update jika perlu
                                    if (isset($data_verifikasi) && isset($data_verifikasi['probabilitas'])) {
                                        $prob_db = floatval($data_verifikasi['probabilitas']);
                                        if (abs($prob - $prob_db) > 0.01) { // Jika perbedaan lebih dari 1%
                                            error_log("PERBEDAAN PROBABILITAS: di database=" . $prob_db . ", di variabel=" . $prob);
                                            // Update database dengan probabilitas yang benar
                                            $query_update_prob = "UPDATE hasil_prediksi SET probabilitas = ? WHERE id_prediksi = ?";
                                            $stmt_update_prob = $koneksi->prepare($query_update_prob);
                                            $stmt_update_prob->bind_param("di", $prob, $id_prediksi);
                                            $stmt_update_prob->execute();
                                            error_log("Probabilitas diupdate di database: " . $prob . " untuk ID prediksi " . $id_prediksi);

                                            // Update juga di prediksi_detail jika ada
                                            $query_update_detail = "UPDATE prediksi_detail SET probabilitas = ? WHERE id_prediksi = ?";
                                            $stmt_update_detail = $koneksi->prepare($query_update_detail);
                                            $stmt_update_detail->bind_param("di", $prob, $id_prediksi);
                                            $stmt_update_detail->execute();
                                        }
                                    }

                                    // Simpan probabilitas yang sudah dimodifikasi untuk digunakan di keterangan
                                    $GLOBALS['adjusted_probability'] = $prob;

                                    // Jika probabilitas sudah dikoreksi, simpan kembali ke database
                                    if (isset($data_verifikasi) && abs($prob - floatval($prediksi['probabilitas'])) > 0.01) {
                                        // Update probabilitas di database
                                        $query_update_prob = "UPDATE hasil_prediksi SET probabilitas = ? WHERE id_prediksi = ?";
                                        $stmt_update_prob = $koneksi->prepare($query_update_prob);
                                        $stmt_update_prob->bind_param("di", $prob, $id_prediksi);
                                        $stmt_update_prob->execute();
                                        error_log("Probabilitas diupdate di database: " . $prob . " untuk ID prediksi " . $id_prediksi);

                                        // Update juga di prediksi_detail jika ada
                                        $query_update_detail = "UPDATE prediksi_detail SET probabilitas = ? WHERE id_prediksi = ?";
                                        $stmt_update_detail = $koneksi->prepare($query_update_detail);
                                        $stmt_update_detail->bind_param("di", $prob, $id_prediksi);
                                        $stmt_update_detail->execute();
                                    }

                                    // Log untuk debugging
                                    error_log("Probabilitas asli: " . $prediksi['probabilitas'] . ", Probabilitas disesuaikan: " . $prob);
                                    error_log("Hasil prediksi: " . $prediksi['hasil_prediksi']);

                                    if ($prediksi['hasil_prediksi'] == 'Layak') {
                                        echo number_format($prob * 100, 2);
                                    } else {
                                        echo number_format((1 - $prob) * 100, 2);
                                    }
                                    ?>%
                                </h3>
                                <p class="text-muted">Prediksi dilakukan pada <?php
                                    // Gunakan fungsi helper untuk format tanggal dengan parameter bahasa Indonesia
                                    echo format_tanggal($prediksi['tanggal_prediksi'], 'd F Y H:i', true);
                                ?> menggunakan algoritma Backpropagation Neural Network</p>

                                <!-- Tambahkan debugging info -->
                                <?php if (isset($_GET['debug']) && $_GET['debug'] == 1): ?>
                                <div class="alert alert-info">
                                    <h4>Debug Info:</h4>
                                    <pre><?php print_r($prediksi); ?></pre>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Keterangan Hasil Prediksi -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-info-circle"></i> Keterangan Hasil Prediksi</h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <p><?php
                                    // Ambil keterangan asli
                                    $keterangan = $prediksi['keterangan'] ?? 'Tidak ada keterangan yang tersedia.';

                                    // Buat keterangan baru yang sesuai dengan hasil prediksi
                                    if ($prediksi['hasil_prediksi'] == 'Layak') {
                                        $prob_display = number_format($prediksi['probabilitas'] * 100, 2);
                                        $keterangan = "Nasabah diprediksi layak menerima kredit dengan probabilitas {$prob_display}%. ";
                                        $keterangan .= "Faktor yang mendukung: ";

                                        // Tambahkan faktor pendukung berdasarkan data nasabah
                                        $faktor_pendukung = [];

                                        if ($nasabah['jaminan'] == 'BPKB Mobil') {
                                            $faktor_pendukung[] = "jaminan BPKB mobil dengan kondisi baik";
                                        }

                                        if (isset($nasabah['status_pajak']) && $nasabah['status_pajak'] == 'Aktif') {
                                            $faktor_pendukung[] = "pajak aktif";
                                        }

                                        if ($nasabah['kepemilikan_rumah'] == 'Milik Sendiri') {
                                            $faktor_pendukung[] = "status kepemilikan rumah yang baik";
                                        }

                                        if ($nasabah['pekerjaan'] == 'PNS' || $nasabah['pekerjaan'] == 'Profesional') {
                                            $faktor_pendukung[] = "pekerjaan dengan penghasilan stabil";
                                        }

                                        $keterangan .= implode(", ", $faktor_pendukung) . ".";
                                    } else {
                                        $prob_display = number_format((1 - $prediksi['probabilitas']) * 100, 2);
                                        $keterangan = "Nasabah diprediksi tidak layak menerima kredit dengan probabilitas {$prob_display}%. ";
                                        $keterangan .= "Faktor risiko: ";

                                        // Tambahkan faktor risiko berdasarkan data nasabah
                                        $faktor_risiko = [];

                                        // Cek rasio pinjaman terhadap penghasilan
                                        $penghasilan = is_numeric($nasabah['penghasilan']) ? floatval($nasabah['penghasilan']) : floatval(str_replace('.', '', $nasabah['penghasilan']));
                                        $jumlah_pinjaman = is_numeric($nasabah['jumlah_pinjaman']) ? floatval($nasabah['jumlah_pinjaman']) : floatval(str_replace('.', '', $nasabah['jumlah_pinjaman']));
                                        $rasio_pinjaman = $jumlah_pinjaman / $penghasilan;

                                        if ($rasio_pinjaman > 24) {
                                            $faktor_risiko[] = "rasio pinjaman terhadap penghasilan terlalu tinggi";
                                        }

                                        // Cek jumlah tanggungan
                                        if (isset($nasabah['jumlah_tanggungan']) && $nasabah['jumlah_tanggungan'] > 3) {
                                            $faktor_risiko[] = "jumlah tanggungan yang banyak";
                                        }

                                        // Cek jaminan
                                        if ($nasabah['jaminan'] == 'BPKB Motor') {
                                            $faktor_risiko[] = "nilai jaminan kurang memadai";
                                        }

                                        // Cek status pajak
                                        if (isset($nasabah['status_pajak']) && $nasabah['status_pajak'] == 'Tidak Aktif') {
                                            $faktor_risiko[] = "status pajak tidak aktif";
                                        }

                                        // Cek kepemilikan rumah
                                        if ($nasabah['kepemilikan_rumah'] == 'Kontrak' || $nasabah['kepemilikan_rumah'] == 'Orang Tua') {
                                            $faktor_risiko[] = "status kepemilikan rumah yang kurang baik";
                                        }

                                        $keterangan .= implode(", ", $faktor_risiko) . ".";
                                    }

                                    echo $keterangan;
                                ?></p>

                                <h4>Analisis Tambahan:</h4>
                                <ul>
                                    <?php if (!empty($keterangan_tambahan)): ?>
                                        <?php foreach ($keterangan_tambahan as $ket): ?>
                                        <li><?php echo $ket; ?></li>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <li>Tidak ada analisis tambahan yang tersedia.</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Nasabah -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-user"></i> Data Nasabah</h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Nama Nasabah</th>
                                        <td><?php echo htmlspecialchars($nasabah['nama_nasabah']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Umur</th>
                                        <td><?php echo $nasabah['umur']; ?> tahun</td>
                                    </tr>
                                    <tr>
                                        <th>Jenis Kelamin</th>
                                        <td><?php echo htmlspecialchars($nasabah['jenis_kelamin']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status Perkawinan</th>
                                        <td><?php echo htmlspecialchars($nasabah['status_perkawinan']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Pekerjaan</th>
                                        <td><?php echo htmlspecialchars($nasabah['pekerjaan']); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Penghasilan</th>
                                        <td><?php echo formatRupiah($nasabah['penghasilan']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jumlah Tanggungan</th>
                                        <td><?php echo($nasabah['jumlah_tanggungan']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jumlah Pinjaman</th>
                                        <td><?php echo formatRupiah($nasabah['jumlah_pinjaman']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Kepemilikan Rumah</th>
                                        <td><?php echo htmlspecialchars($nasabah['kepemilikan_rumah']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jaminan</th>
                                        <td><?php echo htmlspecialchars($nasabah['jaminan']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tujuan Pinjaman</th>
                                        <td><?php echo htmlspecialchars($nasabah['tujuan_pinjaman'] ?? '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tombol Aksi -->
        <div class="row">
            <div class="col-lg-12">
                <div class="well">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="prediksi_website.php" class="btn btn-default btn-block">
                                <i class="fa fa-arrow-left"></i> Kembali ke Prediksi Website
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="data_nasabah_analis.php" class="btn btn-info btn-block">
                                <i class="fa fa-users"></i> Lihat Data Nasabah
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="proses_backpropagation.php?id_prediksi=<?php echo $id_prediksi; ?>" class="btn btn-warning btn-block">
                                <i class="fa fa-cogs"></i> Proses Backpropagation
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="cetak_hasil.php?id_prediksi=<?php echo $id_prediksi; ?>" class="btn btn-primary btn-block" target="_blank">
                                <i class="fa fa-print"></i> Cetak Hasil
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'foot.php'; ?>
