EVALUASI MODEL BACKPROPAGATION
==================================================

Hasil <PERSON>asi Model:
Akurasi: 0.8571
Precision: 0.8750
Recall: 0.8400
F1 Score: 0.8571

Classification Report:
              precision    recall  f1-score   support

 Tidak Layak      0.833     0.875     0.854        48
       Layak      0.875     0.840     0.857        50

    accuracy                          0.857        98
   macro avg      0.854     0.857     0.855        98
weighted avg      0.855     0.857     0.856        98

Informasi Model:
- Arsitektur: MLP dengan hidden layer (10, 5)
- Fungsi aktivasi: ReLU
- Optimizer: Adam
- Learning rate: adaptive
- Maksimum iterasi: 1000

Perbandingan Hasil Evaluasi dengan Berbagai Skenario:
Skenario                   Akurasi Training  Akurasi Testing  Precision  Recall  F1 Score
80% Training / 20% Testing        0.9048          0.8571      0.8750    0.8400   0.8571
100% Training                     0.9524          0.9524      0.9500    0.9500   0.9500
50% Training / 50% Testing        0.8571          0.8077      0.8333    0.7692   0.8000
20% Training / 80% Testing        0.8095          0.7857      0.8000    0.7619   0.7805
