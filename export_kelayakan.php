<?php
include 'koneksi.php';
include 'cek_session.php';

// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);

// Set header untuk file Excel
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="Daftar_Kelayakan_Nasabah_' . date('Y-m-d') . '.xls"');
header('Cache-Control: max-age=0');

// Filter berdasarkan kelayakan jika ada
$filter = "";
if (isset($_GET['kelayakan']) && in_array($_GET['kelayakan'], ['Layak', 'Tidak Layak'])) {
    $kelayakan = $_GET['kelayakan'];
    $filter = " AND kelayakan = '$kelayakan'";
}

// Ambil daftar nasabah dengan kelayakan
$sql = "SELECT id_nasabah, nama_nasabah, umur, jenis_kela<PERSON>, pek<PERSON><PERSON><PERSON>, pen<PERSON><PERSON><PERSON>,
              jumlah_pinjaman, kepem<PERSON><PERSON>_rumah, jamina<PERSON>, kelaya<PERSON>, jumlah_tanggungan,
              tahun_kendaraan, status_pajak, tujuan_pinjaman
       FROM nasabah
       WHERE kelayakan IS NOT NULL $filter
       ORDER BY nama_nasabah";

$result = mysqli_query($koneksi, $sql);

if (!$result) {
    die('Error: ' . mysqli_error($koneksi));
}

// Mulai output Excel
echo '<!DOCTYPE html>';
echo '<html>';
echo '<head>';
echo '<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />';
echo '<title>Export Data Kelayakan Nasabah</title>';
echo '</head>';
echo '<body>';

// Judul laporan
echo '<h2>Daftar Kelayakan Nasabah</h2>';
echo '<p>Tanggal Export: ' . date('d-m-Y H:i:s') . '</p>';

if (isset($_GET['kelayakan'])) {
    echo '<p>Filter: Nasabah dengan status ' . $_GET['kelayakan'] . '</p>';
}

// Tabel data
echo '<table border="1">';
echo '<thead>';
echo '<tr>';
echo '<th>No</th>';
echo '<th>Nama Nasabah</th>';
echo '<th>Umur</th>';
echo '<th>Jenis Kelamin</th>';
echo '<th>Pekerjaan</th>';
echo '<th>Penghasilan</th>';
echo '<th>Jumlah Tanggungan</th>';
echo '<th>Jumlah Pinjaman</th>';
echo '<th>Kepemilikan Rumah</th>';
echo '<th>Jaminan</th>';
echo '<th>Tahun Kendaraan</th>';
echo '<th>Status Pajak</th>';
echo '<th>Tujuan Pinjaman</th>';
echo '<th>Status Kelayakan</th>';
echo '</tr>';
echo '</thead>';
echo '<tbody>';

if (mysqli_num_rows($result) > 0) {
    $no = 1;
    while ($row = mysqli_fetch_assoc($result)) {
        echo '<tr>';
        echo '<td>' . $no++ . '</td>';
        echo '<td>' . $row['nama_nasabah'] . '</td>';
        echo '<td>' . $row['umur'] . ' tahun</td>';
        echo '<td>' . $row['jenis_kelamin'] . '</td>';
        echo '<td>' . $row['pekerjaan'] . '</td>';
        echo '<td>Rp ' . number_format($row['penghasilan'], 0, ',', '.') . '</td>';
        echo '<td>' . ($row['jumlah_tanggungan'] ?? '-') . '</td>';
        echo '<td>Rp ' . number_format($row['jumlah_pinjaman'], 0, ',', '.') . '</td>';
        echo '<td>' . $row['kepemilikan_rumah'] . '</td>';
        echo '<td>' . $row['jaminan'] . '</td>';
        echo '<td>' . ($row['tahun_kendaraan'] ?? '-') . '</td>';
        echo '<td>' . ($row['status_pajak'] ?? '-') . '</td>';
        echo '<td>' . ($row['tujuan_pinjaman'] ?? 'Kebutuhan Pribadi') . '</td>';
        echo '<td>' . $row['kelayakan'] . '</td>';
        echo '</tr>';
    }
} else {
    echo '<tr>';
    echo '<td colspan="14" align="center">Tidak ada data nasabah dengan status kelayakan</td>';
    echo '</tr>';
}

echo '</tbody>';
echo '</table>';

// Statistik
$sql_stat = "SELECT kelayakan, COUNT(*) as jumlah FROM nasabah WHERE kelayakan IS NOT NULL $filter GROUP BY kelayakan";
$result_stat = mysqli_query($koneksi, $sql_stat);

$layak = 0;
$tidak_layak = 0;

if ($result_stat) {
    while ($row = mysqli_fetch_assoc($result_stat)) {
        if ($row['kelayakan'] == 'Layak') {
            $layak = $row['jumlah'];
        } else {
            $tidak_layak = $row['jumlah'];
        }
    }
}

$total = $layak + $tidak_layak;
$persen_layak = $total > 0 ? round(($layak / $total) * 100, 2) : 0;
$persen_tidak_layak = $total > 0 ? round(($tidak_layak / $total) * 100, 2) : 0;

echo '<br><br>';
echo '<h3>Statistik Kelayakan</h3>';
echo '<table border="1">';
echo '<thead>';
echo '<tr>';
echo '<th>Status</th>';
echo '<th>Jumlah</th>';
echo '<th>Persentase</th>';
echo '</tr>';
echo '</thead>';
echo '<tbody>';
echo '<tr>';
echo '<td>Layak</td>';
echo '<td>' . $layak . '</td>';
echo '<td>' . $persen_layak . '%</td>';
echo '</tr>';
echo '<tr>';
echo '<td>Tidak Layak</td>';
echo '<td>' . $tidak_layak . '</td>';
echo '<td>' . $persen_tidak_layak . '%</td>';
echo '</tr>';
echo '<tr>';
echo '<td><strong>Total</strong></td>';
echo '<td><strong>' . $total . '</strong></td>';
echo '<td><strong>100%</strong></td>';
echo '</tr>';
echo '</tbody>';
echo '</table>';

echo '</body>';
echo '</html>';
?>
