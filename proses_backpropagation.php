<?php
include 'koneksi.php';
include 'cek_session.php';
include 'date_helper.php'; // Tambahkan include untuk date_helper.php
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);
require_once 'nav_analis.php';

// Cek apakah ada parameter id_prediksi
if (!isset($_GET['id_prediksi'])) {
    // Redirect ke halaman proses prediksi jika parameter tidak lengkap
    header("Location: prediksi_baru.php");
    exit;
}

$id_prediksi = $_GET['id_prediksi'];

// Coba ambil data prediksi dari tabel hasil_prediksi terlebih dahulu
$query_prediksi = "SELECT hp.*, n.*
                  FROM hasil_prediksi hp
                  JOIN nasabah n ON hp.id_nasabah = n.id_nasabah
                  WHERE hp.id_prediksi = ?";
$stmt_prediksi = $koneksi->prepare($query_prediksi);
$stmt_prediksi->bind_param("i", $id_prediksi);
$stmt_prediksi->execute();
$result_prediksi = $stmt_prediksi->get_result();
$data = $result_prediksi->fetch_assoc();

// Jika tidak ditemukan di hasil_prediksi, coba cari di prediksi_detail
if (!$data) {
    $query_prediksi_detail = "SELECT pd.*, lp.akurasi, lp.parameter, n.*
                            FROM prediksi_detail pd
                            LEFT JOIN laporan_prediksi lp ON pd.id_laporan = lp.id_laporan
                            JOIN nasabah n ON pd.id_nasabah = n.id_nasabah
                            WHERE pd.id_prediksi = ?";
    $stmt_prediksi_detail = $koneksi->prepare($query_prediksi_detail);
    $stmt_prediksi_detail->bind_param("i", $id_prediksi);
    $stmt_prediksi_detail->execute();
    $result_prediksi_detail = $stmt_prediksi_detail->get_result();
    $data = $result_prediksi_detail->fetch_assoc();
}

// Jika data tidak ditemukan
if (!$data) {
    // Redirect ke halaman proses prediksi
    header("Location: prediksi_baru.php");
    exit;
}

// Ambil data proses perhitungan
$query_proses = "SELECT * FROM proses_perhitungan WHERE id_prediksi = ? ORDER BY waktu_proses";
$stmt_proses = $koneksi->prepare($query_proses);
$stmt_proses->bind_param("i", $id_prediksi);
$stmt_proses->execute();
$result_proses = $stmt_proses->get_result();
$proses_perhitungan = [];
while ($row = $result_proses->fetch_assoc()) {
    $proses_perhitungan[] = $row;
}

// Fungsi formatRupiah sudah diimpor dari format_helper.php melalui koneksi.php

// Fungsi untuk normalisasi data
function normalizeValue($value, $min, $max) {
    if ($max == $min) return 0.5; // Hindari pembagian dengan nol
    return ($value - $min) / ($max - $min);
}

// Fungsi untuk menghitung angsuran bulanan
function hitungAngsuranBulanan($jumlah_pinjaman, $jangka_waktu, $bunga_bulanan = 0.01) {
    // Bunga flat per bulan (1%)
    $total_bunga = $jumlah_pinjaman * $bunga_bulanan * $jangka_waktu;
    $total_pembayaran = $jumlah_pinjaman + $total_bunga;
    $angsuran_bulanan = $total_pembayaran / $jangka_waktu;

    return [
        'angsuran_bulanan' => $angsuran_bulanan,
        'total_bunga' => $total_bunga,
        'total_pembayaran' => $total_pembayaran
    ];
}

// Simulasi proses backpropagation
function simulateBackpropagation($data) {
    // Definisikan nilai min dan max untuk normalisasi
    $min_max = [
        'umur' => ['min' => 20, 'max' => 60],
        'penghasilan' => ['min' => 1000000, 'max' => 20000000],
        'jumlah_tanggungan' => ['min' => 0, 'max' => 10],
        'jumlah_pinjaman' => ['min' => 1000000, 'max' => 100000000],
        'jangka_waktu' => ['min' => 6, 'max' => 60],
        'tahun_kendaraan' => ['min' => 2010, 'max' => 2025]
    ];

    // Hitung parameter tambahan
    $penghasilan = $data['penghasilan'];
    $jumlah_tanggungan = isset($data['jumlah_tanggungan']) ? $data['jumlah_tanggungan'] : 1;
    $jumlah_pinjaman = $data['jumlah_pinjaman'];
    $jangka_waktu = $data['jangka_waktu'];

    // Hitung penghasilan per kapita
    $penghasilan_per_kapita = $penghasilan / max(1, $jumlah_tanggungan);

    // Hitung angsuran bulanan
    $hasil_angsuran = hitungAngsuranBulanan($jumlah_pinjaman, $jangka_waktu);
    $angsuran_bulanan = $hasil_angsuran['angsuran_bulanan'];

    // Hitung rasio angsuran terhadap penghasilan
    $rasio_angsuran = $angsuran_bulanan / $penghasilan;

    // Hitung rasio pinjaman terhadap penghasilan tahunan
    $rasio_pinjaman = $jumlah_pinjaman / ($penghasilan * 12);

    // Normalisasi input numerik
    $normalized_inputs = [
        'umur' => normalizeValue($data['umur'], $min_max['umur']['min'], $min_max['umur']['max']),
        'penghasilan' => normalizeValue($data['penghasilan'], $min_max['penghasilan']['min'], $min_max['penghasilan']['max']),
        'jumlah_pinjaman' => normalizeValue($data['jumlah_pinjaman'], $min_max['jumlah_pinjaman']['min'], $min_max['jumlah_pinjaman']['max']),
        'jangka_waktu' => normalizeValue($data['jangka_waktu'], $min_max['jangka_waktu']['min'], $min_max['jangka_waktu']['max']),
        'rasio_angsuran' => min(1, $rasio_angsuran), // Normalisasi antara 0-1
        'rasio_pinjaman' => min(1, $rasio_pinjaman) // Normalisasi antara 0-1
    ];

    // Tambahkan normalisasi untuk jumlah_tanggungan dengan pengecekan
    if (isset($data['jumlah_tanggungan']) && $data['jumlah_tanggungan'] !== null) {
        $normalized_inputs['jumlah_tanggungan'] = normalizeValue($data['jumlah_tanggungan'], $min_max['jumlah_tanggungan']['min'], $min_max['jumlah_tanggungan']['max']);
    } else {
        $normalized_inputs['jumlah_tanggungan'] = 0; // Default value jika tidak ada data
    }

    // Tambahkan normalisasi untuk tahun kendaraan jika ada
    if (isset($data['tahun_kendaraan']) && $data['tahun_kendaraan'] !== null) {
        $normalized_inputs['tahun_kendaraan'] = normalizeValue($data['tahun_kendaraan'], $min_max['tahun_kendaraan']['min'], $min_max['tahun_kendaraan']['max']);
    } else {
        $normalized_inputs['tahun_kendaraan'] = 0.5; // Default value jika tidak ada data
    }

    // One-hot encoding untuk input kategorikal
    $jenis_kelamin_input = ($data['jenis_kelamin'] == 'Laki-laki') ? 1 : 0;
    $status_perkawinan_input = [
        'menikah' => ($data['status_perkawinan'] == 'Menikah') ? 1 : 0,
        'belum_menikah' => ($data['status_perkawinan'] == 'Belum Menikah') ? 1 : 0,
        'cerai' => ($data['status_perkawinan'] == 'Cerai') ? 1 : 0
    ];
    $pekerjaan_input = [
        'pns' => ($data['pekerjaan'] == 'PNS') ? 1 : 0,
        'swasta' => ($data['pekerjaan'] == 'Swasta') ? 1 : 0,
        'wiraswasta' => ($data['pekerjaan'] == 'Wiraswasta') ? 1 : 0,
        'lainnya' => ($data['pekerjaan'] == 'Lainnya') ? 1 : 0
    ];
    $jaminan_input = [
        'bpkb_motor' => ($data['jaminan'] == 'BPKB Motor') ? 1 : 0,
        'bpkb_mobil' => ($data['jaminan'] == 'BPKB Mobil') ? 1 : 0,
        'sertifikat_tanah' => ($data['jaminan'] == 'Sertifikat Tanah') ? 1 : 0,
        'lainnya' => ($data['jaminan'] == 'Lainnya') ? 1 : 0
    ];
    $kepemilikan_rumah_input = [
        'milik_sendiri' => ($data['kepemilikan_rumah'] == 'Milik Sendiri') ? 1 : 0,
        'kontrak' => ($data['kepemilikan_rumah'] == 'Kontrak') ? 1 : 0,
        'orang_tua' => ($data['kepemilikan_rumah'] == 'Orang Tua') ? 1 : 0
    ];

    // Simulasi bobot input ke hidden layer (nilai acak untuk simulasi)
    // Bobot positif menunjukkan pengaruh positif terhadap kelayakan kredit
    // Bobot negatif menunjukkan pengaruh negatif terhadap kelayakan kredit
    $weights_input_hidden = [
        // Variabel numerik
        'umur' => 0.35, // Umur lebih tinggi cenderung lebih layak
        'penghasilan' => 0.65, // Penghasilan lebih tinggi cenderung lebih layak
        'jumlah_pinjaman' => -0.45, // Jumlah pinjaman lebih tinggi cenderung kurang layak
        'jangka_waktu' => -0.25, // Jangka waktu lebih lama cenderung kurang layak
        'jumlah_tanggungan' => -0.30, // Jumlah tanggungan lebih banyak cenderung kurang layak
        'rasio_angsuran' => -0.70, // Rasio angsuran lebih tinggi cenderung kurang layak
        'rasio_pinjaman' => -0.60, // Rasio pinjaman lebih tinggi cenderung kurang layak
        'tahun_kendaraan' => 0.40, // Tahun kendaraan lebih baru cenderung lebih layak

        // Variabel kategorikal
        'jenis_kelamin' => 0.15, // Laki-laki (1) vs Perempuan (0)
        'status_perkawinan' => [0.25, -0.15, -0.35], // [Menikah, Belum Menikah, Cerai]
        'pekerjaan' => [0.45, 0.25, 0.15, -0.25], // [PNS, Swasta, Wiraswasta, Lainnya]
        'jaminan' => [0.15, 0.35, 0.55, -0.15], // [BPKB Motor, BPKB Mobil, Sertifikat Tanah, Lainnya]
        'kepemilikan_rumah' => [0.45, -0.25, 0.15] // [Milik Sendiri, Kontrak, Orang Tua]
    ];

    // Simulasi bias hidden layer
    // Bias adalah nilai konstan yang ditambahkan ke setiap neuron
    $bias_hidden = [0.25, 0.15, 0.35, 0.45, 0.55];

    // Simulasi bobot hidden ke output layer
    // Bobot menentukan seberapa penting output dari setiap hidden neuron
    $weights_hidden_output = [0.65, 0.45, 0.35, 0.25, 0.15];

    // Simulasi bias output layer
    // Bias output menentukan threshold dasar untuk output
    $bias_output = 0.35;

    // Penjelasan pengaruh setiap variabel terhadap kelayakan kredit
    $pengaruh_variabel = [
        'umur' => [
            'bobot' => $weights_input_hidden['umur'],
            'penjelasan' => 'Umur yang lebih tinggi menunjukkan kematangan finansial yang lebih baik.'
        ],
        'penghasilan' => [
            'bobot' => $weights_input_hidden['penghasilan'],
            'penjelasan' => 'Penghasilan yang lebih tinggi meningkatkan kemampuan membayar angsuran.'
        ],
        'jumlah_pinjaman' => [
            'bobot' => $weights_input_hidden['jumlah_pinjaman'],
            'penjelasan' => 'Jumlah pinjaman yang lebih besar meningkatkan risiko gagal bayar.'
        ],
        'jangka_waktu' => [
            'bobot' => $weights_input_hidden['jangka_waktu'],
            'penjelasan' => 'Jangka waktu yang lebih lama meningkatkan ketidakpastian pembayaran.'
        ],
        'rasio_angsuran' => [
            'bobot' => $weights_input_hidden['rasio_angsuran'],
            'penjelasan' => 'Rasio angsuran terhadap penghasilan yang tinggi mengurangi kelayakan kredit.'
        ],
        'rasio_pinjaman' => [
            'bobot' => $weights_input_hidden['rasio_pinjaman'],
            'penjelasan' => 'Rasio pinjaman terhadap penghasilan tahunan yang tinggi mengurangi kelayakan kredit.'
        ]
    ];

    // Simulasi aktivasi hidden layer (sigmoid)
    $hidden_layer_outputs = [];
    $hidden_layer_details = [];

    for ($i = 0; $i < 5; $i++) {
        $sum = $bias_hidden[$i];
        $detail_perhitungan = "Bias: {$bias_hidden[$i]}";

        // Variabel numerik
        $sum += $normalized_inputs['umur'] * $weights_input_hidden['umur'];
        $detail_perhitungan .= " + Umur: {$normalized_inputs['umur']} × {$weights_input_hidden['umur']} = " .
                              number_format($normalized_inputs['umur'] * $weights_input_hidden['umur'], 4);

        $sum += $normalized_inputs['penghasilan'] * $weights_input_hidden['penghasilan'];
        $detail_perhitungan .= " + Penghasilan: {$normalized_inputs['penghasilan']} × {$weights_input_hidden['penghasilan']} = " .
                              number_format($normalized_inputs['penghasilan'] * $weights_input_hidden['penghasilan'], 4);

        $sum += $normalized_inputs['jumlah_pinjaman'] * $weights_input_hidden['jumlah_pinjaman'];
        $detail_perhitungan .= " + Jumlah Pinjaman: {$normalized_inputs['jumlah_pinjaman']} × {$weights_input_hidden['jumlah_pinjaman']} = " .
                              number_format($normalized_inputs['jumlah_pinjaman'] * $weights_input_hidden['jumlah_pinjaman'], 4);

        $sum += $normalized_inputs['jangka_waktu'] * $weights_input_hidden['jangka_waktu'];
        $detail_perhitungan .= " + Jangka Waktu: {$normalized_inputs['jangka_waktu']} × {$weights_input_hidden['jangka_waktu']} = " .
                              number_format($normalized_inputs['jangka_waktu'] * $weights_input_hidden['jangka_waktu'], 4);

        // Parameter tambahan
        if (isset($normalized_inputs['jumlah_tanggungan'])) {
            $sum += $normalized_inputs['jumlah_tanggungan'] * $weights_input_hidden['jumlah_tanggungan'];
            $detail_perhitungan .= " + Jumlah Tanggungan: {$normalized_inputs['jumlah_tanggungan']} × {$weights_input_hidden['jumlah_tanggungan']} = " .
                                  number_format($normalized_inputs['jumlah_tanggungan'] * $weights_input_hidden['jumlah_tanggungan'], 4);
        }

        if (isset($normalized_inputs['rasio_angsuran'])) {
            $sum += $normalized_inputs['rasio_angsuran'] * $weights_input_hidden['rasio_angsuran'];
            $detail_perhitungan .= " + Rasio Angsuran: {$normalized_inputs['rasio_angsuran']} × {$weights_input_hidden['rasio_angsuran']} = " .
                                  number_format($normalized_inputs['rasio_angsuran'] * $weights_input_hidden['rasio_angsuran'], 4);
        }

        if (isset($normalized_inputs['rasio_pinjaman'])) {
            $sum += $normalized_inputs['rasio_pinjaman'] * $weights_input_hidden['rasio_pinjaman'];
            $detail_perhitungan .= " + Rasio Pinjaman: {$normalized_inputs['rasio_pinjaman']} × {$weights_input_hidden['rasio_pinjaman']} = " .
                                  number_format($normalized_inputs['rasio_pinjaman'] * $weights_input_hidden['rasio_pinjaman'], 4);
        }

        if (isset($normalized_inputs['tahun_kendaraan'])) {
            $sum += $normalized_inputs['tahun_kendaraan'] * $weights_input_hidden['tahun_kendaraan'];
            $detail_perhitungan .= " + Tahun Kendaraan: {$normalized_inputs['tahun_kendaraan']} × {$weights_input_hidden['tahun_kendaraan']} = " .
                                  number_format($normalized_inputs['tahun_kendaraan'] * $weights_input_hidden['tahun_kendaraan'], 4);
        }

        // Variabel kategorikal
        $sum += $jenis_kelamin_input * $weights_input_hidden['jenis_kelamin'];
        $detail_perhitungan .= " + Jenis Kelamin: {$jenis_kelamin_input} × {$weights_input_hidden['jenis_kelamin']} = " .
                              number_format($jenis_kelamin_input * $weights_input_hidden['jenis_kelamin'], 4);

        $sum += $status_perkawinan_input['menikah'] * $weights_input_hidden['status_perkawinan'][0];
        $detail_perhitungan .= " + Status Menikah: {$status_perkawinan_input['menikah']} × {$weights_input_hidden['status_perkawinan'][0]} = " .
                              number_format($status_perkawinan_input['menikah'] * $weights_input_hidden['status_perkawinan'][0], 4);

        $sum += $status_perkawinan_input['belum_menikah'] * $weights_input_hidden['status_perkawinan'][1];
        $detail_perhitungan .= " + Status Belum Menikah: {$status_perkawinan_input['belum_menikah']} × {$weights_input_hidden['status_perkawinan'][1]} = " .
                              number_format($status_perkawinan_input['belum_menikah'] * $weights_input_hidden['status_perkawinan'][1], 4);

        $sum += $status_perkawinan_input['cerai'] * $weights_input_hidden['status_perkawinan'][2];
        $detail_perhitungan .= " + Status Cerai: {$status_perkawinan_input['cerai']} × {$weights_input_hidden['status_perkawinan'][2]} = " .
                              number_format($status_perkawinan_input['cerai'] * $weights_input_hidden['status_perkawinan'][2], 4);

        $sum += $pekerjaan_input['pns'] * $weights_input_hidden['pekerjaan'][0];
        $detail_perhitungan .= " + Pekerjaan PNS: {$pekerjaan_input['pns']} × {$weights_input_hidden['pekerjaan'][0]} = " .
                              number_format($pekerjaan_input['pns'] * $weights_input_hidden['pekerjaan'][0], 4);

        $sum += $pekerjaan_input['swasta'] * $weights_input_hidden['pekerjaan'][1];
        $detail_perhitungan .= " + Pekerjaan Swasta: {$pekerjaan_input['swasta']} × {$weights_input_hidden['pekerjaan'][1]} = " .
                              number_format($pekerjaan_input['swasta'] * $weights_input_hidden['pekerjaan'][1], 4);

        $sum += $pekerjaan_input['wiraswasta'] * $weights_input_hidden['pekerjaan'][2];
        $detail_perhitungan .= " + Pekerjaan Wiraswasta: {$pekerjaan_input['wiraswasta']} × {$weights_input_hidden['pekerjaan'][2]} = " .
                              number_format($pekerjaan_input['wiraswasta'] * $weights_input_hidden['pekerjaan'][2], 4);

        $sum += $pekerjaan_input['lainnya'] * $weights_input_hidden['pekerjaan'][3];
        $detail_perhitungan .= " + Pekerjaan Lainnya: {$pekerjaan_input['lainnya']} × {$weights_input_hidden['pekerjaan'][3]} = " .
                              number_format($pekerjaan_input['lainnya'] * $weights_input_hidden['pekerjaan'][3], 4);

        $sum += $jaminan_input['bpkb_motor'] * $weights_input_hidden['jaminan'][0];
        $detail_perhitungan .= " + Jaminan BPKB Motor: {$jaminan_input['bpkb_motor']} × {$weights_input_hidden['jaminan'][0]} = " .
                              number_format($jaminan_input['bpkb_motor'] * $weights_input_hidden['jaminan'][0], 4);

        $sum += $jaminan_input['bpkb_mobil'] * $weights_input_hidden['jaminan'][1];
        $detail_perhitungan .= " + Jaminan BPKB Mobil: {$jaminan_input['bpkb_mobil']} × {$weights_input_hidden['jaminan'][1]} = " .
                              number_format($jaminan_input['bpkb_mobil'] * $weights_input_hidden['jaminan'][1], 4);

        $sum += $jaminan_input['sertifikat_tanah'] * $weights_input_hidden['jaminan'][2];
        $detail_perhitungan .= " + Jaminan Sertifikat Tanah: {$jaminan_input['sertifikat_tanah']} × {$weights_input_hidden['jaminan'][2]} = " .
                              number_format($jaminan_input['sertifikat_tanah'] * $weights_input_hidden['jaminan'][2], 4);

        $sum += $jaminan_input['lainnya'] * $weights_input_hidden['jaminan'][3];
        $detail_perhitungan .= " + Jaminan Lainnya: {$jaminan_input['lainnya']} × {$weights_input_hidden['jaminan'][3]} = " .
                              number_format($jaminan_input['lainnya'] * $weights_input_hidden['jaminan'][3], 4);

        $sum += $kepemilikan_rumah_input['milik_sendiri'] * $weights_input_hidden['kepemilikan_rumah'][0];
        $detail_perhitungan .= " + Rumah Milik Sendiri: {$kepemilikan_rumah_input['milik_sendiri']} × {$weights_input_hidden['kepemilikan_rumah'][0]} = " .
                              number_format($kepemilikan_rumah_input['milik_sendiri'] * $weights_input_hidden['kepemilikan_rumah'][0], 4);

        $sum += $kepemilikan_rumah_input['kontrak'] * $weights_input_hidden['kepemilikan_rumah'][1];
        $detail_perhitungan .= " + Rumah Kontrak: {$kepemilikan_rumah_input['kontrak']} × {$weights_input_hidden['kepemilikan_rumah'][1]} = " .
                              number_format($kepemilikan_rumah_input['kontrak'] * $weights_input_hidden['kepemilikan_rumah'][1], 4);

        $sum += $kepemilikan_rumah_input['orang_tua'] * $weights_input_hidden['kepemilikan_rumah'][2];
        $detail_perhitungan .= " + Rumah Orang Tua: {$kepemilikan_rumah_input['orang_tua']} × {$weights_input_hidden['kepemilikan_rumah'][2]} = " .
                              number_format($kepemilikan_rumah_input['orang_tua'] * $weights_input_hidden['kepemilikan_rumah'][2], 4);

        // Fungsi aktivasi sigmoid: f(x) = 1 / (1 + e^(-x))
        $hidden_layer_outputs[$i] = 1 / (1 + exp(-$sum));

        // Simpan detail perhitungan
        $hidden_layer_details[$i] = [
            'sum' => $sum,
            'detail' => $detail_perhitungan,
            'output' => $hidden_layer_outputs[$i]
        ];
    }

    // Simulasi aktivasi output layer (sigmoid)
    $output_sum = $bias_output;
    $output_detail = "Bias Output: {$bias_output}";

    for ($i = 0; $i < 5; $i++) {
        $output_sum += $hidden_layer_outputs[$i] * $weights_hidden_output[$i];
        $output_detail .= " + Hidden Neuron " . ($i+1) . ": {$hidden_layer_outputs[$i]} × {$weights_hidden_output[$i]} = " .
                         number_format($hidden_layer_outputs[$i] * $weights_hidden_output[$i], 4);
    }

    // Fungsi aktivasi sigmoid: f(x) = 1 / (1 + e^(-x))
    $output = 1 / (1 + exp(-$output_sum));
    $output_detail .= " = {$output_sum} → Sigmoid({$output_sum}) = {$output}";

    // Hasil prediksi
    $hasil_prediksi = ($output >= 0.5) ? 'Layak' : 'Tidak Layak';
    $probabilitas = $output;

    // Faktor-faktor yang paling berpengaruh
    $faktor_berpengaruh = [];
    foreach ($pengaruh_variabel as $var => $info) {
        if (isset($normalized_inputs[$var])) {
            $pengaruh = $normalized_inputs[$var] * $info['bobot'];
            $faktor_berpengaruh[$var] = [
                'nilai_normalisasi' => $normalized_inputs[$var],
                'bobot' => $info['bobot'],
                'pengaruh' => $pengaruh,
                'penjelasan' => $info['penjelasan']
            ];
        }
    }

    // Urutkan faktor berdasarkan besarnya pengaruh (absolut)
    uasort($faktor_berpengaruh, function($a, $b) {
        return abs($b['pengaruh']) - abs($a['pengaruh']);
    });

    // Ambil 3 faktor teratas
    $top_faktor = array_slice($faktor_berpengaruh, 0, 3, true);

    return [
        // Input dan normalisasi
        'normalized_inputs' => $normalized_inputs,
        'jenis_kelamin_input' => $jenis_kelamin_input,
        'status_perkawinan_input' => $status_perkawinan_input,
        'pekerjaan_input' => $pekerjaan_input,
        'jaminan_input' => $jaminan_input,
        'kepemilikan_rumah_input' => $kepemilikan_rumah_input,

        // Parameter tambahan
        'penghasilan_per_kapita' => $penghasilan_per_kapita,
        'angsuran_bulanan' => $angsuran_bulanan,
        'rasio_angsuran' => $rasio_angsuran,
        'rasio_pinjaman' => $rasio_pinjaman,
        'hasil_angsuran' => $hasil_angsuran,

        // Bobot dan bias
        'weights_input_hidden' => $weights_input_hidden,
        'bias_hidden' => $bias_hidden,
        'weights_hidden_output' => $weights_hidden_output,
        'bias_output' => $bias_output,

        // Detail perhitungan
        'hidden_layer_outputs' => $hidden_layer_outputs,
        'hidden_layer_details' => $hidden_layer_details,
        'output_detail' => $output_detail,
        'output_sum' => $output_sum,

        // Hasil
        'output' => $output,
        'hasil_prediksi' => $hasil_prediksi,
        'probabilitas' => $probabilitas,

        // Analisis faktor
        'pengaruh_variabel' => $pengaruh_variabel,
        'top_faktor' => $top_faktor
    ];
}

// Simulasi proses backpropagation
$backpropagation_result = simulateBackpropagation($data);
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Proses Perhitungan Backpropagation</h1>
                <ol class="breadcrumb">
                    <li><a href="index_analis.php">Dashboard</a></li>
                    <li><a href="prediksi_baru.php">Prediksi Kelayakan</a></li>
                    <li><a href="hasil_prediksi.php?id_nasabah=<?php echo $data['id_nasabah']; ?>&id_prediksi=<?php echo $id_prediksi; ?>">Hasil Prediksi</a></li>
                    <li class="active">Proses Backpropagation</li>
                </ol>
            </div>
        </div>

        <!-- Hasil Prediksi -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-<?php echo $data['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?>">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-calculator"></i> Hasil Prediksi untuk <?php echo htmlspecialchars($data['nama_nasabah']); ?></h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h4>Hasil: <strong><?php echo $data['hasil_prediksi']; ?></strong> dengan probabilitas
                                <?php
                                    // Gunakan nilai probabilitas dari model Python yang tersimpan di database
                                    $prob = $data['probabilitas'];
                                    // Pastikan probabilitas dalam format yang benar
                                    if (is_string($prob)) {
                                        $prob = floatval($prob);
                                    }

                                    // Tampilkan probabilitas langsung tanpa modifikasi
                                    echo number_format($prob * 100, 2);
                                ?>%</h4>
                                <p><?php
                                    // Ambil keterangan asli
                                    $keterangan = $data['keterangan'] ?? 'Tidak ada keterangan yang tersedia.';

                                    echo $keterangan;
                                ?></p>
                                <p class="text-muted">Prediksi dilakukan pada <?php
                                    // Gunakan fungsi helper untuk format tanggal dengan parameter bahasa Indonesia
                                    echo format_tanggal($data['tanggal_prediksi'], 'd F Y H:i', true);
                                ?> menggunakan algoritma Backpropagation Neural Network</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Proses Backpropagation -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-cogs"></i> Proses Perhitungan Backpropagation</h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h4>Langkah 1: Normalisasi Input</h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Variabel</th>
                                                <th>Nilai Asli</th>
                                                <th>Nilai Normalisasi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Umur</td>
                                                <td><?php echo $data['umur']; ?> tahun</td>
                                                <td><?php echo number_format($backpropagation_result['normalized_inputs']['umur'], 4); ?></td>
                                            </tr>
                                            <tr>
                                                <td>Penghasilan</td>
                                                <td><?php echo formatRupiah($data['penghasilan']); ?></td>
                                                <td><?php echo number_format($backpropagation_result['normalized_inputs']['penghasilan'], 4); ?></td>
                                            </tr>
                                             <tr>
                                                <td>Jumlah Tanggungan</td>
                                                <td><?php echo isset($data['jumlah_tanggungan']) ? $data['jumlah_tanggungan'] : '0'; ?></td>
                                                <td><?php echo number_format($backpropagation_result['normalized_inputs']['jumlah_tanggungan'], 4); ?></td>
                                            </tr>
                                            <tr>
                                                <td>Jumlah Pinjaman</td>
                                                <td><?php echo formatRupiah($data['jumlah_pinjaman']); ?></td>
                                                <td><?php echo number_format($backpropagation_result['normalized_inputs']['jumlah_pinjaman'], 4); ?></td>
                                            </tr>
                                            <tr>
                                                <td>Jangka Waktu</td>
                                                <td><?php echo $data['jangka_waktu']; ?> bulan</td>
                                                <td><?php echo number_format($backpropagation_result['normalized_inputs']['jangka_waktu'], 4); ?></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h4>Langkah 2: One-Hot Encoding untuk Variabel Kategorikal</h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Variabel</th>
                                                <th>Nilai Asli</th>
                                                <th>Nilai Encoding</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Jenis Kelamin</td>
                                                <td><?php echo $data['jenis_kelamin']; ?></td>
                                                <td><?php echo $backpropagation_result['jenis_kelamin_input']; ?></td>
                                            </tr>
                                            <tr>
                                                <td>Status Perkawinan</td>
                                                <td><?php echo $data['status_perkawinan']; ?></td>
                                                <td>
                                                    Menikah: <?php echo $backpropagation_result['status_perkawinan_input']['menikah']; ?><br>
                                                    Belum Menikah: <?php echo $backpropagation_result['status_perkawinan_input']['belum_menikah']; ?><br>
                                                    Cerai: <?php echo $backpropagation_result['status_perkawinan_input']['cerai']; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Pekerjaan</td>
                                                <td><?php echo $data['pekerjaan']; ?></td>
                                                <td>
                                                    PNS: <?php echo $backpropagation_result['pekerjaan_input']['pns']; ?><br>
                                                    Swasta: <?php echo $backpropagation_result['pekerjaan_input']['swasta']; ?><br>
                                                    Wiraswasta: <?php echo $backpropagation_result['pekerjaan_input']['wiraswasta']; ?><br>
                                                    Lainnya: <?php echo $backpropagation_result['pekerjaan_input']['lainnya']; ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Jaminan</td>
                                                <td><?php echo $data['jaminan']; ?></td>
                                                <td>
                                                    BPKB Motor: <?php echo $backpropagation_result['jaminan_input']['bpkb_motor']; ?><br>
                                                    BPKB Mobil: <?php echo $backpropagation_result['jaminan_input']['bpkb_mobil']; ?><br>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Kepemilikan Rumah</td>
                                                <td><?php echo $data['kepemilikan_rumah']; ?></td>
                                                <td>
                                                    Milik Sendiri: <?php echo $backpropagation_result['kepemilikan_rumah_input']['milik_sendiri']; ?><br>
                                                    Kontrak: <?php echo $backpropagation_result['kepemilikan_rumah_input']['kontrak']; ?><br>
                                                    Orang Tua: <?php echo $backpropagation_result['kepemilikan_rumah_input']['orang_tua']; ?>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h4>Langkah 3: Parameter Tambahan</h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Parameter</th>
                                                <th>Nilai</th>
                                                <th>Keterangan</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Penghasilan per Kapita</td>
                                                <td><?php echo formatRupiah($backpropagation_result['penghasilan_per_kapita']); ?></td>
                                                <td>Penghasilan dibagi jumlah tanggungan</td>
                                            </tr>
                                            <tr>
                                                <td>Angsuran Bulanan</td>
                                                <td><?php echo formatRupiah($backpropagation_result['angsuran_bulanan']); ?></td>
                                                <td>Estimasi angsuran per bulan dengan bunga flat 1%</td>
                                            </tr>
                                            <tr>
                                                <td>Total Bunga</td>
                                                <td><?php echo formatRupiah($backpropagation_result['hasil_angsuran']['total_bunga']); ?></td>
                                                <td>Total bunga selama masa pinjaman</td>
                                            </tr>
                                            <tr>
                                                <td>Total Pembayaran</td>
                                                <td><?php echo formatRupiah($backpropagation_result['hasil_angsuran']['total_pembayaran']); ?></td>
                                                <td>Total pembayaran selama masa pinjaman (pokok + bunga)</td>
                                            </tr>
                                            <tr>
                                                <td>Rasio Angsuran/Penghasilan</td>
                                                <td><?php echo number_format($backpropagation_result['rasio_angsuran'] * 100, 2); ?>%</td>
                                                <td>Persentase penghasilan yang digunakan untuk angsuran</td>
                                            </tr>
                                            <tr>
                                                <td>Rasio Pinjaman/Penghasilan Tahunan</td>
                                                <td><?php echo number_format($backpropagation_result['rasio_pinjaman'] * 100, 2); ?>%</td>
                                                <td>Persentase penghasilan tahunan yang digunakan untuk pinjaman</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h4>Langkah 4: Perhitungan Hidden Layer</h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Neuron</th>
                                                <th>Nilai Input * Bobot + Bias</th>
                                                <th>Hasil Aktivasi Sigmoid</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php for ($i = 0; $i < 5; $i++): ?>
                                            <tr>
                                                <td>Hidden Neuron <?php echo $i+1; ?></td>
                                                <td>
                                                    <button class="btn btn-xs btn-info" type="button" data-toggle="collapse" data-target="#detail-neuron-<?php echo $i; ?>" aria-expanded="false" aria-controls="detail-neuron-<?php echo $i; ?>">
                                                        Lihat Detail
                                                    </button>
                                                    <div class="collapse" id="detail-neuron-<?php echo $i; ?>">
                                                        <div class="well well-sm" style="margin-top: 10px; font-size: 12px;">
                                                            <?php echo $backpropagation_result['hidden_layer_details'][$i]['detail']; ?>
                                                            <br><br>
                                                            <strong>Total: <?php echo number_format($backpropagation_result['hidden_layer_details'][$i]['sum'], 6); ?></strong>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo number_format($backpropagation_result['hidden_layer_outputs'][$i], 6); ?></td>
                                            </tr>
                                            <?php endfor; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <h4>Langkah 5: Perhitungan Output Layer</h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Perhitungan</th>
                                                <th>Hasil</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Detail Perhitungan</td>
                                                <td>
                                                    <button class="btn btn-xs btn-info" type="button" data-toggle="collapse" data-target="#detail-output" aria-expanded="false" aria-controls="detail-output">
                                                        Lihat Detail
                                                    </button>
                                                    <div class="collapse" id="detail-output">
                                                        <div class="well well-sm" style="margin-top: 10px; font-size: 12px;">
                                                            <?php echo $backpropagation_result['output_detail']; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Nilai Output (Sigmoid)</td>
                                                <td><?php echo number_format($backpropagation_result['output'], 6); ?></td>
                                            </tr>
                                            <tr>
                                                <td>Threshold</td>
                                                <td>0.5</td>
                                            </tr>
                                            <tr>
                                                <td>Hasil Prediksi</td>
                                                <td>
                                                    <strong class="text-<?php echo $data['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?>">
                                                        <?php echo $data['hasil_prediksi']; ?>
                                                    </strong>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Nilai Probabilitas Model Python</td>
                                                <td><?php
                                                    // Gunakan nilai probabilitas dari model Python yang tersimpan di database
                                                    $prob = $data['probabilitas'];
                                                    // Pastikan probabilitas dalam format yang benar
                                                    if (is_string($prob)) {
                                                        $prob = floatval($prob);
                                                    }

                                                    // Tampilkan probabilitas langsung tanpa modifikasi
                                                    echo number_format($prob * 100, 2);
                                                ?>%</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <h4>Langkah 6: Faktor yang Paling Berpengaruh</h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Faktor</th>
                                                <th>Bobot</th>
                                                <th>Pengaruh</th>
                                                <th>Penjelasan</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($backpropagation_result['top_faktor'] as $var => $info): ?>
                                            <tr>
                                                <td><?php echo ucfirst(str_replace('_', ' ', $var)); ?></td>
                                                <td><?php echo number_format($info['bobot'], 4); ?></td>
                                                <td class="<?php echo $info['pengaruh'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                    <?php echo number_format($info['pengaruh'], 4); ?>
                                                </td>
                                                <td><?php echo $info['penjelasan']; ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Nasabah -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-user"></i> Data Nasabah</h3>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Nama Nasabah</th>
                                        <td><?php echo htmlspecialchars($data['nama_nasabah']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Umur</th>
                                        <td><?php echo $data['umur']; ?> tahun</td>
                                    </tr>
                                    <tr>
                                        <th>Jenis Kelamin</th>
                                        <td><?php echo htmlspecialchars($data['jenis_kelamin']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status Perkawinan</th>
                                        <td><?php echo htmlspecialchars($data['status_perkawinan']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Pekerjaan</th>
                                        <td><?php echo htmlspecialchars($data['pekerjaan']); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="30%">Penghasilan</th>
                                        <td><?php echo formatRupiah($data['penghasilan']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jumlah Tanggungan</th>
                                        <td><?php echo isset($data['jumlah_tanggungan']) ? $data['jumlah_tanggungan'] : '0'; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jumlah Pinjaman</th>
                                        <td><?php echo formatRupiah($data['jumlah_pinjaman']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jangka Waktu</th>
                                        <td><?php echo $data['jangka_waktu']; ?> bulan</td>
                                    </tr>
                                    <tr>
                                        <th>Kepemilikan Rumah</th>
                                        <td><?php echo htmlspecialchars($data['kepemilikan_rumah']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Jaminan</th>
                                        <td><?php echo htmlspecialchars($data['jaminan']); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tombol Aksi -->
        <div class="row">
            <div class="col-lg-12">
                <div class="well">
                    <div class="row">
                        <div class="col-md-6">
                            <a href="hasil_prediksi.php?id_nasabah=<?php echo $data['id_nasabah']; ?>&id_prediksi=<?php echo $id_prediksi; ?>" class="btn btn-default btn-block">
                                <i class="fa fa-arrow-left"></i> Kembali ke Hasil Prediksi
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="cetak_hasil.php?id_prediksi=<?php echo $id_prediksi; ?>" class="btn btn-primary btn-block" target="_blank">
                                <i class="fa fa-print"></i> Cetak Hasil Prediksi
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'foot.php'; ?>
