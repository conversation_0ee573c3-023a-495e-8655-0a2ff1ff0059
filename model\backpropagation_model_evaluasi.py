import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
import pickle
import os
import re
from datetime import datetime

# Set random seed untuk reproduksibilitas
np.random.seed(42)

# ## Fungsi Utilitas

def format_rupiah(value):
    """Mengubah string Rupiah menjadi nilai numerik"""
    if isinstance(value, str):
        # Hapus 'Rp', titik, dan spasi
        value = value.replace('Rp', '').replace('.', '').replace(' ', '')
        # Hapus karakter non-numerik
        value = re.sub(r'[^\d]', '', value)
        if value:
            return float(value)
    return float(value) if value else 0

def extract_number_from_age(age_str):
    """Mengekstrak angka dari string umur"""
    if isinstance(age_str, str):
        match = re.search(r'(\d+)', age_str)
        if match:
            return int(match.group(1))
    return int(age_str) if age_str else 0

def preprocess_dataset(df):
    """Preprocessing dataset"""
    # Buat salinan dataframe
    df_processed = df.copy()

    # Ekstrak umur dari string
    if 'umur' in df_processed.columns:
        df_processed['umur'] = df_processed['umur'].apply(extract_number_from_age)

    # Konversi nilai Rupiah ke numerik
    if 'penghasilan' in df_processed.columns:
        df_processed['penghasilan'] = df_processed['penghasilan'].apply(format_rupiah)

    if 'jumlah_pinjaman' in df_processed.columns:
        df_processed['jumlah_pinjaman'] = df_processed['jumlah_pinjaman'].apply(format_rupiah)

    # Konversi waktu pengembalian ke numerik
    if 'waktu_pengembalian' in df_processed.columns:
        df_processed['waktu_pengembalian'] = pd.to_numeric(df_processed['waktu_pengembalian'], errors='coerce')

    # Konversi tahun kendaraan ke numerik
    if 'tahun_kendaraan' in df_processed.columns:
        df_processed['tahun_kendaraan'] = pd.to_numeric(df_processed['tahun_kendaraan'], errors='coerce')

    # Tambahkan fitur turunan
    if 'penghasilan' in df_processed.columns and 'jumlah_tanggungan' in df_processed.columns:
        df_processed['jumlah_tanggungan'] = pd.to_numeric(df_processed['jumlah_tanggungan'], errors='coerce').fillna(1)
        df_processed['penghasilan_per_kapita'] = df_processed['penghasilan'] / df_processed['jumlah_tanggungan'].clip(lower=1)

    if 'penghasilan' in df_processed.columns and 'jumlah_pinjaman' in df_processed.columns:
        df_processed['rasio_pinjaman'] = df_processed['jumlah_pinjaman'] / (df_processed['penghasilan'] * 12)

    if 'penghasilan' in df_processed.columns and 'waktu_pengembalian' in df_processed.columns and 'jumlah_pinjaman' in df_processed.columns:
        # Estimasi angsuran bulanan (dengan bunga flat 1% per bulan)
        bunga_bulanan = 0.01  # 1% per bulan
        df_processed['waktu_pengembalian'] = df_processed['waktu_pengembalian'].fillna(12)
        df_processed['total_bunga'] = df_processed['jumlah_pinjaman'] * bunga_bulanan * df_processed['waktu_pengembalian']
        df_processed['total_pembayaran'] = df_processed['jumlah_pinjaman'] + df_processed['total_bunga']
        df_processed['angsuran_bulanan'] = df_processed['total_pembayaran'] / df_processed['waktu_pengembalian']
        df_processed['rasio_angsuran'] = df_processed['angsuran_bulanan'] / df_processed['penghasilan']

    # Tambahkan fitur umur kendaraan
    current_year = datetime.now().year
    if 'tahun_kendaraan' in df_processed.columns:
        df_processed['umur_kendaraan'] = current_year - df_processed['tahun_kendaraan']

    return df_processed

# ## Memuat dan Preprocessing Dataset

# Coba memuat dataset
try:
    # Coba memuat dengan berbagai delimiter
    try:
        df = pd.read_csv('dataset_nasabah.csv', sep=';')
    except:
        try:
            df = pd.read_csv('model/dataset_nasabah.csv', sep=';')
        except:
            try:
                df = pd.read_csv('dataset_nasabah.csv', sep=',')
            except:
                df = pd.read_csv('model/dataset_nasabah.csv', sep=',')

    print(f"Dataset berhasil dimuat dengan {df.shape[0]} baris dan {df.shape[1]} kolom")
except Exception as e:
    print(f"Error saat memuat dataset: {e}")
    raise

# Preprocessing dataset
df_processed = preprocess_dataset(df)

# Tampilkan informasi dataset
print("\nInformasi Dataset setelah preprocessing:")
print(df_processed.info())

# Tampilkan statistik deskriptif
print("\nStatistik Deskriptif:")
print(df_processed.describe())

# Periksa distribusi kelas target
print("\nDistribusi Kelas Target (Kelayakan):")
print(df_processed['kelayakan'].value_counts())
print(df_processed['kelayakan'].value_counts(normalize=True) * 100)

# Visualisasi distribusi kelas target
plt.figure(figsize=(10, 6))
sns.countplot(x='kelayakan', data=df_processed)
plt.title('Distribusi Kelas Target (Kelayakan)')
plt.savefig('model/distribusi_kelayakan.png')
plt.close()

# ## Fungsi untuk Evaluasi Model dengan Berbagai Skenario Split Data

def evaluate_model(X, y, test_size, random_state=42):
    """
    Evaluasi model dengan berbagai skenario pembagian data

    Parameters:
    -----------
    X : DataFrame
        Fitur input
    y : Series
        Target output
    test_size : float
        Proporsi data testing (0.0 - 1.0)
    random_state : int
        Seed untuk reproduksibilitas

    Returns:
    --------
    dict
        Dictionary berisi hasil evaluasi model
    """
    results = {}

    # Jika test_size = 0, gunakan semua data untuk training
    if test_size == 0:
        X_train, X_test = X, X
        y_train, y_test = y, y
        print(f"\n\n{'='*50}")
        print(f"Skenario: 100% data training (Overfitting Test)")
        print(f"{'='*50}")
    else:
        # Split data menjadi training dan testing
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state, stratify=y)
        train_pct = (1 - test_size) * 100
        test_pct = test_size * 100
        print(f"\n\n{'='*50}")
        print(f"Skenario: {train_pct:.0f}% data training dan {test_pct:.0f}% data testing")
        print(f"{'='*50}")

    # Identifikasi kolom numerik dan kategorikal
    numeric_features = X.select_dtypes(include=['int64', 'float64']).columns.tolist()
    categorical_features = X.select_dtypes(include=['object']).columns.tolist()

    print(f"Fitur numerik: {numeric_features}")
    print(f"Fitur kategorikal: {categorical_features}")

    # Buat preprocessor
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', StandardScaler(), numeric_features),
            ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
        ] if categorical_features else [('num', StandardScaler(), numeric_features)]
    )

    # Buat pipeline dengan preprocessor dan model
    pipeline = Pipeline([
        ('preprocessor', preprocessor),
        ('classifier', MLPClassifier(
            hidden_layer_sizes=(10, 5),  # Dua hidden layer dengan 10 dan 5 neuron
            activation='relu',           # Fungsi aktivasi ReLU
            solver='adam',               # Optimizer Adam
            alpha=0.0001,                # Regularization parameter
            batch_size='auto',           # Ukuran batch otomatis
            learning_rate='adaptive',    # Learning rate adaptif
            max_iter=1000,               # Maksimum iterasi
            random_state=random_state    # Seed untuk reproduksibilitas
        ))
    ])

    # Melatih model
    print("Melatih model...")
    pipeline.fit(X_train, y_train)

    # Prediksi pada data training
    y_train_pred = pipeline.predict(X_train)
    train_accuracy = accuracy_score(y_train, y_train_pred)
    print(f"Akurasi pada data training: {train_accuracy:.4f}")

    # Prediksi pada data testing
    y_test_pred = pipeline.predict(X_test)

    # Jika test_size = 0, gunakan data training sebagai evaluasi
    if test_size == 0:
        print("Menggunakan data training sebagai evaluasi (Overfitting Test)")
        test_accuracy = train_accuracy
    else:
        test_accuracy = accuracy_score(y_test, y_test_pred)
        print(f"Akurasi pada data testing: {test_accuracy:.4f}")

    # Menghitung metrik evaluasi
    precision = precision_score(y_test, y_test_pred, pos_label='Layak')
    recall = recall_score(y_test, y_test_pred, pos_label='Layak')
    f1 = f1_score(y_test, y_test_pred, pos_label='Layak')

    # Menampilkan confusion matrix
    cm = confusion_matrix(y_test, y_test_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['Tidak Layak', 'Layak'],
                yticklabels=['Tidak Layak', 'Layak'])
    plt.xlabel('Prediksi')
    plt.ylabel('Aktual')
    plt.title(f'Confusion Matrix - {train_pct:.0f}% Training / {test_pct:.0f}% Testing')
    plt.savefig(f'model/confusion_matrix_{train_pct:.0f}_{test_pct:.0f}.png')
    plt.close()

    # Menampilkan classification report
    print("\nClassification Report:")
    print(classification_report(y_test, y_test_pred))

    # Menyimpan hasil evaluasi
    results = {
        'train_size': 1 - test_size,
        'test_size': test_size,
        'train_accuracy': train_accuracy,
        'test_accuracy': test_accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'confusion_matrix': cm,
        'pipeline': pipeline
    }

    return results

# ## Persiapan Data untuk Pelatihan Model

# Pisahkan fitur dan target
X = df_processed.drop('kelayakan', axis=1)
y = df_processed['kelayakan']

# Hapus kolom yang tidak relevan untuk model
columns_to_drop = []
for col in X.columns:
    if col.startswith('Unnamed:'):
        columns_to_drop.append(col)

X = X.drop(columns=columns_to_drop, errors='ignore')

# ## Evaluasi Model dengan Berbagai Skenario

# Skenario 1: 80% data training dan 20% data testing (standar)
results_80_20 = evaluate_model(X, y, test_size=0.2)

# Skenario 2: 100% data training (overfitting test)
results_100_0 = evaluate_model(X, y, test_size=0)

# Skenario 3: 50% data training dan 50% data testing
results_50_50 = evaluate_model(X, y, test_size=0.5)

# Skenario 4: 20% data training dan 80% data testing
results_20_80 = evaluate_model(X, y, test_size=0.8)

# ## Perbandingan Hasil Evaluasi

# Membuat DataFrame untuk perbandingan hasil
comparison = pd.DataFrame({
    'Skenario': ['80% Training / 20% Testing', '100% Training', '50% Training / 50% Testing', '20% Training / 80% Testing'],
    'Akurasi Training': [results_80_20['train_accuracy'], results_100_0['train_accuracy'],
                         results_50_50['train_accuracy'], results_20_80['train_accuracy']],
    'Akurasi Testing': [results_80_20['test_accuracy'], results_100_0['test_accuracy'],
                        results_50_50['test_accuracy'], results_20_80['test_accuracy']],
    'Precision': [results_80_20['precision'], results_100_0['precision'],
                  results_50_50['precision'], results_20_80['precision']],
    'Recall': [results_80_20['recall'], results_100_0['recall'],
               results_50_50['recall'], results_20_80['recall']],
    'F1 Score': [results_80_20['f1_score'], results_100_0['f1_score'],
                 results_50_50['f1_score'], results_20_80['f1_score']]
})

print("\n\nPerbandingan Hasil Evaluasi:")
print(comparison)

# Visualisasi perbandingan metrik
plt.figure(figsize=(12, 8))
metrics = ['Akurasi Training', 'Akurasi Testing', 'Precision', 'Recall', 'F1 Score']
for i, metric in enumerate(metrics):
    plt.subplot(2, 3, i+1)
    sns.barplot(x='Skenario', y=metric, data=comparison)
    plt.xticks(rotation=45, ha='right')
    plt.title(metric)
plt.tight_layout()
plt.savefig('model/perbandingan_metrik.png')
plt.close()

# ## Simpan Model Terbaik

# Pilih model terbaik (biasanya model dengan skenario 80/20)
best_model = results_80_20['pipeline']

# Simpan model ke file
model_filename = 'model/backpropagation_model.pkl'

# Pastikan direktori model ada
os.makedirs('model', exist_ok=True)

# Simpan model
with open(model_filename, 'wb') as file:
    pickle.dump(best_model, file)

print(f"\nModel terbaik telah disimpan ke {model_filename}")

# ## Kesimpulan

print("\n\nKESIMPULAN:")
print("="*50)
print(f"Model terbaik adalah skenario 80% Training / 20% Testing dengan:")
print(f"- Akurasi: {results_80_20['test_accuracy']:.4f}")
print(f"- Precision: {results_80_20['precision']:.4f}")
print(f"- Recall: {results_80_20['recall']:.4f}")
print(f"- F1 Score: {results_80_20['f1_score']:.4f}")
print("="*50)

# Menampilkan informasi tentang model
print("\nInformasi Model:")
print(f"- Arsitektur: MLP dengan hidden layer (10, 5)")
print(f"- Fungsi aktivasi: ReLU")
print(f"- Optimizer: Adam")
print(f"- Learning rate: adaptive")
print(f"- Maksimum iterasi: 1000")

# Simpan hasil evaluasi ke file
with open('model/evaluasi_model.txt', 'w') as file:
    file.write("EVALUASI MODEL BACKPROPAGATION\n")
    file.write("="*50 + "\n\n")
    file.write("Perbandingan Hasil Evaluasi:\n")
    file.write(comparison.to_string() + "\n\n")
    file.write("KESIMPULAN:\n")
    file.write("="*50 + "\n")
    file.write(f"Model terbaik adalah skenario 80% Training / 20% Testing dengan:\n")
    file.write(f"- Akurasi: {results_80_20['test_accuracy']:.4f}\n")
    file.write(f"- Precision: {results_80_20['precision']:.4f}\n")
    file.write(f"- Recall: {results_80_20['recall']:.4f}\n")
    file.write(f"- F1 Score: {results_80_20['f1_score']:.4f}\n")
    file.write("="*50 + "\n\n")
    file.write("Informasi Model:\n")
    file.write(f"- Arsitektur: MLP dengan hidden layer (10, 5)\n")
    file.write(f"- Fungsi aktivasi: ReLU\n")
    file.write(f"- Optimizer: Adam\n")
    file.write(f"- Learning rate: adaptive\n")
    file.write(f"- Maksimum iterasi: 1000\n")

print("\nHasil evaluasi telah disimpan ke model/evaluasi_model.txt")

# ## Membuat Data Sampel dan Menunjukkan Proses Perhitungan Prediksi

print("\n\n" + "="*50)
print("DEMONSTRASI PROSES PERHITUNGAN PREDIKSI DENGAN DATA SAMPEL")
print("="*50)

def create_sample_data(num_samples=5):
    """
    Membuat data sampel untuk demonstrasi proses perhitungan prediksi

    Parameters:
    -----------
    num_samples : int
        Jumlah sampel yang akan dibuat

    Returns:
    --------
    DataFrame
        DataFrame berisi data sampel
    """
    # Buat data sampel dengan variasi nilai
    np.random.seed(42)  # Untuk reproduksibilitas

    # Definisikan rentang nilai untuk setiap fitur
    umur_range = (25, 60)
    penghasilan_range = (3000000, 15000000)
    jumlah_pinjaman_range = (5000000, 50000000)
    waktu_pengembalian_range = (12, 60)
    jumlah_tanggungan_range = (0, 5)
    tahun_kendaraan_range = (2010, 2023)

    # Buat data sampel
    data = []
    for i in range(num_samples):
        # Variasi nilai untuk setiap sampel
        umur = np.random.randint(umur_range[0], umur_range[1])
        jenis_kelamin = np.random.choice(['Laki-laki', 'Perempuan'])
        status_pernikahan = np.random.choice(['Menikah', 'Belum Menikah', 'Cerai'])
        pekerjaan = np.random.choice(['PNS', 'Karyawan Swasta', 'Wiraswasta', 'Profesional', 'Lainnya'])
        penghasilan = np.random.randint(penghasilan_range[0], penghasilan_range[1])
        jumlah_pinjaman = np.random.randint(jumlah_pinjaman_range[0], jumlah_pinjaman_range[1])
        kepemilikan_rumah = np.random.choice(['Milik Sendiri', 'Sewa', 'Milik Keluarga'])
        jenis_agunan = np.random.choice(['BPKB Mobil', 'BPKB Motor'])
        tahun_kendaraan = np.random.randint(tahun_kendaraan_range[0], tahun_kendaraan_range[1])
        status_pajak = np.random.choice(['Aktif', 'Tidak Aktif'])
        jumlah_tanggungan = np.random.randint(jumlah_tanggungan_range[0], jumlah_tanggungan_range[1])
        waktu_pengembalian = np.random.randint(waktu_pengembalian_range[0], waktu_pengembalian_range[1])

        # Tambahkan ke data
        sample = {
            'umur': umur,
            'jenis_kelamin': jenis_kelamin,
            'status_pernikahan': status_pernikahan,
            'pekerjaan': pekerjaan,
            'penghasilan': penghasilan,
            'jumlah_pinjaman': jumlah_pinjaman,
            'kepemilikan_rumah': kepemilikan_rumah,
            'jenis_agunan': jenis_agunan,
            'tahun_kendaraan': tahun_kendaraan,
            'status_pajak': status_pajak,
            'jumlah_tanggungan': jumlah_tanggungan,
            'waktu_pengembalian': waktu_pengembalian
        }
        data.append(sample)

    # Buat DataFrame
    df_sample = pd.DataFrame(data)

    return df_sample

# Buat data sampel
num_samples = 5  # Jumlah sampel yang akan dibuat
df_sample = create_sample_data(num_samples)

# Tampilkan data sampel
print("\nData Sampel yang Dibuat:")
print(df_sample)

# Preprocessing data sampel
df_sample_processed = preprocess_dataset(df_sample)

# Tampilkan data sampel setelah preprocessing
print("\nData Sampel Setelah Preprocessing:")
print(df_sample_processed)

# Fungsi untuk menunjukkan proses perhitungan prediksi
def demonstrate_prediction_process(model, sample_data):
    """
    Menunjukkan proses perhitungan prediksi secara detail

    Parameters:
    -----------
    model : Pipeline
        Model yang akan digunakan untuk prediksi
    sample_data : DataFrame
        Data sampel yang akan diprediksi
    """
    print("\n" + "="*50)
    print("PROSES PERHITUNGAN PREDIKSI")
    print("="*50)

    # Ambil preprocessor dan classifier dari pipeline
    preprocessor = model.named_steps['preprocessor']
    classifier = model.named_steps['classifier']

    # Preprocessing data
    print("\n1. Preprocessing Data:")
    print("-" * 30)

    # Tampilkan data asli
    print("\nData Asli:")
    print(sample_data)

    # Transformasi data menggunakan preprocessor
    X_transformed = preprocessor.transform(sample_data)

    # Tampilkan informasi tentang transformasi
    print("\nHasil Transformasi:")
    print(f"Dimensi data setelah transformasi: {X_transformed.shape}")

    # Jika model menggunakan MLP Classifier, tampilkan detail perhitungan
    if isinstance(classifier, MLPClassifier):
        print("\n2. Forward Propagation:")
        print("-" * 30)

        # Dapatkan parameter model
        coefs = classifier.coefs_
        intercepts = classifier.intercepts_

        print(f"\nArsitektur Jaringan: Input({X_transformed.shape[1]}) -> Hidden1({coefs[0].shape[1]}) -> Hidden2({coefs[1].shape[1]}) -> Output({coefs[2].shape[1]})")

        # Hitung aktivasi untuk setiap layer
        print("\nPerhitungan Aktivasi Layer:")

        # Input layer ke hidden layer 1
        print("\nInput -> Hidden Layer 1:")
        hidden1_input = np.dot(X_transformed, coefs[0]) + intercepts[0]
        hidden1_output = np.maximum(0, hidden1_input)  # ReLU activation
        print(f"Dimensi input: {X_transformed.shape}")
        print(f"Dimensi weight: {coefs[0].shape}")
        print(f"Dimensi bias: {intercepts[0].shape}")
        print(f"Hasil dot product + bias: {hidden1_input.shape}")
        print(f"Hasil aktivasi ReLU: {hidden1_output.shape}")

        # Hidden layer 1 ke hidden layer 2
        print("\nHidden Layer 1 -> Hidden Layer 2:")
        hidden2_input = np.dot(hidden1_output, coefs[1]) + intercepts[1]
        hidden2_output = np.maximum(0, hidden2_input)  # ReLU activation
        print(f"Dimensi input: {hidden1_output.shape}")
        print(f"Dimensi weight: {coefs[1].shape}")
        print(f"Dimensi bias: {intercepts[1].shape}")
        print(f"Hasil dot product + bias: {hidden2_input.shape}")
        print(f"Hasil aktivasi ReLU: {hidden2_output.shape}")

        # Hidden layer 2 ke output layer
        print("\nHidden Layer 2 -> Output Layer:")
        output_input = np.dot(hidden2_output, coefs[2]) + intercepts[2]

        # Fungsi softmax untuk output layer
        def softmax(x):
            exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))
            return exp_x / np.sum(exp_x, axis=1, keepdims=True)

        output_probs = softmax(output_input)
        print(f"Dimensi input: {hidden2_output.shape}")
        print(f"Dimensi weight: {coefs[2].shape}")
        print(f"Dimensi bias: {intercepts[2].shape}")
        print(f"Hasil dot product + bias: {output_input.shape}")
        print(f"Hasil aktivasi Softmax (probabilitas): {output_probs.shape}")

        # Tampilkan probabilitas untuk setiap sampel
        print("\n3. Hasil Probabilitas dan Prediksi:")
        print("-" * 30)

        # Dapatkan label kelas
        classes = classifier.classes_

        # Prediksi menggunakan model
        y_pred = model.predict(sample_data)
        y_pred_proba = model.predict_proba(sample_data)

        # Tampilkan hasil untuk setiap sampel
        for i in range(len(sample_data)):
            print(f"\nSampel #{i+1}:")
            print(f"Probabilitas kelas {classes[0]}: {y_pred_proba[i][0]:.4f}")
            print(f"Probabilitas kelas {classes[1]}: {y_pred_proba[i][1]:.4f}")
            print(f"Prediksi: {y_pred[i]}")

            # Tampilkan detail sampel
            print("\nDetail Sampel:")
            for col in sample_data.columns:
                print(f"- {col}: {sample_data.iloc[i][col]}")

            # Tampilkan fitur turunan yang dihitung
            if 'rasio_pinjaman' in df_sample_processed.columns:
                print(f"- rasio_pinjaman: {df_sample_processed.iloc[i]['rasio_pinjaman']:.4f}")
            if 'penghasilan_per_kapita' in df_sample_processed.columns:
                print(f"- penghasilan_per_kapita: {df_sample_processed.iloc[i]['penghasilan_per_kapita']:.2f}")
            if 'rasio_angsuran' in df_sample_processed.columns:
                print(f"- rasio_angsuran: {df_sample_processed.iloc[i]['rasio_angsuran']:.4f}")
            if 'angsuran_bulanan' in df_sample_processed.columns:
                print(f"- angsuran_bulanan: {df_sample_processed.iloc[i]['angsuran_bulanan']:.2f}")

            # Berikan penjelasan tentang keputusan
            print("\nPenjelasan Keputusan:")
            if y_pred[i] == 'Layak':
                print("Nasabah dinyatakan LAYAK untuk kredit karena:")
                if 'rasio_angsuran' in df_sample_processed.columns and df_sample_processed.iloc[i]['rasio_angsuran'] < 0.3:
                    print(f"- Rasio angsuran terhadap penghasilan ({df_sample_processed.iloc[i]['rasio_angsuran']:.2f}) < 0.3 (baik)")
                if 'rasio_pinjaman' in df_sample_processed.columns and df_sample_processed.iloc[i]['rasio_pinjaman'] < 1.0:
                    print(f"- Rasio pinjaman terhadap penghasilan tahunan ({df_sample_processed.iloc[i]['rasio_pinjaman']:.2f}) < 1.0 (baik)")
                if 'penghasilan_per_kapita' in df_sample_processed.columns and df_sample_processed.iloc[i]['penghasilan_per_kapita'] > 3000000:
                    print(f"- Penghasilan per kapita (Rp {df_sample_processed.iloc[i]['penghasilan_per_kapita']:,.2f}) cukup tinggi")
            else:
                print("Nasabah dinyatakan TIDAK LAYAK untuk kredit karena:")
                if 'rasio_angsuran' in df_sample_processed.columns and df_sample_processed.iloc[i]['rasio_angsuran'] > 0.3:
                    print(f"- Rasio angsuran terhadap penghasilan ({df_sample_processed.iloc[i]['rasio_angsuran']:.2f}) > 0.3 (terlalu tinggi)")
                if 'rasio_pinjaman' in df_sample_processed.columns and df_sample_processed.iloc[i]['rasio_pinjaman'] > 1.0:
                    print(f"- Rasio pinjaman terhadap penghasilan tahunan ({df_sample_processed.iloc[i]['rasio_pinjaman']:.2f}) > 1.0 (terlalu tinggi)")
                if 'penghasilan_per_kapita' in df_sample_processed.columns and df_sample_processed.iloc[i]['penghasilan_per_kapita'] < 3000000:
                    print(f"- Penghasilan per kapita (Rp {df_sample_processed.iloc[i]['penghasilan_per_kapita']:,.2f}) terlalu rendah")

            print("-" * 50)

# Muat model yang telah disimpan
try:
    with open('model/backpropagation_model.pkl', 'rb') as file:
        loaded_model = pickle.load(file)
    print("\nModel berhasil dimuat dari file model/backpropagation_model.pkl")
except Exception as e:
    print(f"\nError saat memuat model: {e}")
    print("Menggunakan model yang baru saja dilatih")
    loaded_model = best_model

# Demonstrasikan proses perhitungan prediksi
demonstrate_prediction_process(loaded_model, df_sample)

# Simpan data sampel ke file CSV
df_sample.to_csv('model/data_sampel.csv', index=False)
print("\nData sampel telah disimpan ke model/data_sampel.csv")

# Simpan hasil prediksi data sampel ke file
df_sample_results = df_sample.copy()
df_sample_results['prediksi'] = loaded_model.predict(df_sample)
df_sample_results['probabilitas_layak'] = loaded_model.predict_proba(df_sample)[:, 1]
df_sample_results.to_csv('model/hasil_prediksi_sampel.csv', index=False)
print("Hasil prediksi data sampel telah disimpan ke model/hasil_prediksi_sampel.csv")

print("\n" + "="*50)
print("SELESAI")
print("="*50)
