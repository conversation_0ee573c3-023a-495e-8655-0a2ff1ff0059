<?php
/**
 * Script untuk memperbaiki struktur database dan foreign key constraints
 */

include 'koneksi.php';

echo "<h2>Memperbaiki Struktur Database</h2>";

// 1. Cek struktur tabel yang ada
echo "<h3>1. Memeriksa Struktur Tabel</h3>";

$tables_to_check = ['nasabah', 'laporan_prediksi', 'prediksi_detail', 'hasil_prediksi', 'proses_perhitungan'];

foreach ($tables_to_check as $table) {
    $check_table = mysqli_query($koneksi, "SHOW TABLES LIKE '$table'");
    if (mysqli_num_rows($check_table) > 0) {
        echo "✓ Tabel $table ada<br>";
        
        // Tampilkan struktur tabel
        $describe = mysqli_query($koneksi, "DESCRIBE $table");
        echo "<details><summary>Struktur $table</summary>";
        echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        while ($row = mysqli_fetch_assoc($describe)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table></details><br>";
    } else {
        echo "✗ Tabel $table tidak ada<br>";
    }
}

// 2. Cek foreign key constraints
echo "<h3>2. Memeriksa Foreign Key Constraints</h3>";

$fk_query = "SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'sistem_prediksi_backpropagation'
AND TABLE_NAME IN ('prediksi_detail', 'hasil_prediksi', 'proses_perhitungan')";

$fk_result = mysqli_query($koneksi, $fk_query);
if ($fk_result && mysqli_num_rows($fk_result) > 0) {
    echo "<table border='1'><tr><th>Table</th><th>Column</th><th>Constraint</th><th>Referenced Table</th><th>Referenced Column</th></tr>";
    while ($row = mysqli_fetch_assoc($fk_result)) {
        echo "<tr>";
        echo "<td>" . $row['TABLE_NAME'] . "</td>";
        echo "<td>" . $row['COLUMN_NAME'] . "</td>";
        echo "<td>" . $row['CONSTRAINT_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_TABLE_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_COLUMN_NAME'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Tidak ada foreign key constraints ditemukan.<br>";
}

// 3. Perbaiki struktur tabel prediksi_detail
echo "<h3>3. Memperbaiki Struktur Tabel prediksi_detail</h3>";

// Hapus foreign key constraint yang bermasalah jika ada
$drop_fk_queries = [
    "ALTER TABLE prediksi_detail DROP FOREIGN KEY fk_prediksi_detail",
    "ALTER TABLE prediksi_detail DROP FOREIGN KEY prediksi_detail_ibfk_1",
    "ALTER TABLE prediksi_detail DROP FOREIGN KEY prediksi_detail_ibfk_2"
];

foreach ($drop_fk_queries as $query) {
    $result = mysqli_query($koneksi, $query);
    if ($result) {
        echo "✓ Berhasil menghapus constraint: " . substr($query, strrpos($query, ' ') + 1) . "<br>";
    } else {
        echo "- Constraint tidak ditemukan atau sudah dihapus: " . substr($query, strrpos($query, ' ') + 1) . "<br>";
    }
}

// Pastikan tabel prediksi_detail memiliki struktur yang benar
$create_prediksi_detail = "
CREATE TABLE IF NOT EXISTS prediksi_detail (
    id_prediksi INT AUTO_INCREMENT PRIMARY KEY,
    id_laporan INT NOT NULL,
    id_nasabah INT NOT NULL,
    hasil_prediksi VARCHAR(20) NOT NULL,
    probabilitas DECIMAL(5,4) NOT NULL,
    keterangan TEXT,
    tanggal_prediksi DATETIME NOT NULL,
    INDEX idx_id_laporan (id_laporan),
    INDEX idx_id_nasabah (id_nasabah),
    FOREIGN KEY (id_laporan) REFERENCES laporan_prediksi(id_laporan) ON DELETE CASCADE,
    FOREIGN KEY (id_nasabah) REFERENCES nasabah(id_nasabah) ON DELETE CASCADE
)";

$result = mysqli_query($koneksi, $create_prediksi_detail);
if ($result) {
    echo "✓ Tabel prediksi_detail berhasil dibuat/diperbarui<br>";
} else {
    echo "✗ Error membuat tabel prediksi_detail: " . mysqli_error($koneksi) . "<br>";
}

// 4. Pastikan tabel hasil_prediksi memiliki struktur yang benar
echo "<h3>4. Memperbaiki Struktur Tabel hasil_prediksi</h3>";

$create_hasil_prediksi = "
CREATE TABLE IF NOT EXISTS hasil_prediksi (
    id_prediksi INT AUTO_INCREMENT PRIMARY KEY,
    id_nasabah INT NOT NULL,
    hasil_prediksi VARCHAR(20) NOT NULL,
    probabilitas DECIMAL(5,4) NOT NULL,
    keterangan TEXT,
    tanggal_prediksi DATETIME NOT NULL,
    INDEX idx_id_nasabah (id_nasabah),
    FOREIGN KEY (id_nasabah) REFERENCES nasabah(id_nasabah) ON DELETE CASCADE
)";

$result = mysqli_query($koneksi, $create_hasil_prediksi);
if ($result) {
    echo "✓ Tabel hasil_prediksi berhasil dibuat/diperbarui<br>";
} else {
    echo "✗ Error membuat tabel hasil_prediksi: " . mysqli_error($koneksi) . "<br>";
}

// 5. Pastikan tabel laporan_prediksi ada
echo "<h3>5. Memperbaiki Struktur Tabel laporan_prediksi</h3>";

$create_laporan_prediksi = "
CREATE TABLE IF NOT EXISTS laporan_prediksi (
    id_laporan INT AUTO_INCREMENT PRIMARY KEY,
    tanggal_prediksi DATETIME NOT NULL,
    parameter VARCHAR(100) NOT NULL,
    akurasi DECIMAL(5,2) NOT NULL DEFAULT 87.5
)";

$result = mysqli_query($koneksi, $create_laporan_prediksi);
if ($result) {
    echo "✓ Tabel laporan_prediksi berhasil dibuat/diperbarui<br>";
} else {
    echo "✗ Error membuat tabel laporan_prediksi: " . mysqli_error($koneksi) . "<br>";
}

// 6. Buat laporan default jika belum ada
echo "<h3>6. Membuat Laporan Default</h3>";

$check_default = mysqli_query($koneksi, "SELECT id_laporan FROM laporan_prediksi WHERE id_laporan = 1");
if (mysqli_num_rows($check_default) == 0) {
    $insert_default = "INSERT INTO laporan_prediksi (id_laporan, tanggal_prediksi, parameter, akurasi) 
                      VALUES (1, NOW(), 'Backpropagation Neural Network', 87.5)";
    $result = mysqli_query($koneksi, $insert_default);
    if ($result) {
        echo "✓ Laporan default berhasil dibuat<br>";
    } else {
        echo "✗ Error membuat laporan default: " . mysqli_error($koneksi) . "<br>";
    }
} else {
    echo "✓ Laporan default sudah ada<br>";
}

// 7. Test insert data
echo "<h3>7. Test Insert Data</h3>";

// Cek apakah ada nasabah untuk test
$test_nasabah = mysqli_query($koneksi, "SELECT id_nasabah FROM nasabah LIMIT 1");
if (mysqli_num_rows($test_nasabah) > 0) {
    $nasabah_row = mysqli_fetch_assoc($test_nasabah);
    $test_id_nasabah = $nasabah_row['id_nasabah'];
    
    // Test insert ke prediksi_detail
    $test_insert = "INSERT INTO prediksi_detail (id_laporan, id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                   VALUES (1, $test_id_nasabah, 'Test', 0.5, 'Test insert', NOW())";
    $result = mysqli_query($koneksi, $test_insert);
    if ($result) {
        $test_id = mysqli_insert_id($koneksi);
        echo "✓ Test insert berhasil (ID: $test_id)<br>";
        
        // Hapus data test
        mysqli_query($koneksi, "DELETE FROM prediksi_detail WHERE id_prediksi = $test_id");
        echo "✓ Data test berhasil dihapus<br>";
    } else {
        echo "✗ Test insert gagal: " . mysqli_error($koneksi) . "<br>";
    }
} else {
    echo "- Tidak ada data nasabah untuk test<br>";
}

echo "<h3>8. Selesai</h3>";
echo "Perbaikan struktur database selesai. Silakan coba lakukan prediksi lagi.";
?>
