<?php
// Set header untuk JSON response
header('Content-Type: application/json');

// Izinkan akses dari domain manapun (untuk pengembangan)
// Untuk produksi, batasi domain yang diizinkan
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Jika request method adalah OPTIONS, kembalikan header saja
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Fungsi untuk menangani error
function handleError($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['error' => $message]);
    exit;
}

// Ambil path dari URL
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$path = str_replace('/api/index.php', '', $path);
$path = trim($path, '/');

// Ambil method request
$method = $_SERVER['REQUEST_METHOD'];

// Koneksi ke database
require_once '../koneksi.php';

// Endpoint untuk prediksi
if ($path === 'predict' && $method === 'POST') {
    // Ambil data dari request body
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);

    if (!$data) {
        handleError('Invalid JSON data');
    }

    // Validasi data yang diperlukan
    $required_fields = ['id_nasabah'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field])) {
            handleError("Field '$field' is required");
        }
    }

    // Ambil data nasabah dari database
    $id_nasabah = $data['id_nasabah'];
    $sql = "SELECT * FROM nasabah WHERE id_nasabah = ?";
    $stmt = $koneksi->prepare($sql);
    $stmt->bind_param("i", $id_nasabah);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        handleError("Nasabah dengan ID $id_nasabah tidak ditemukan", 404);
    }

    $nasabah = $result->fetch_assoc();

    // Siapkan data untuk dikirim ke model Python
    $model_data = [
        'id_nasabah' => $nasabah['id_nasabah'], // Kirim ID nasabah saja, model akan mengambil data dari database
    ];

    // Kirim data ke model Python menggunakan cURL
    $model_url = 'http://localhost:5000/api/predict';
    $ch = curl_init($model_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($model_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code !== 200) {
        handleError('Failed to get prediction from model', 500);
    }

    $prediction = json_decode($response, true);

    // Simpan hasil prediksi ke database
    $hasil_prediksi = $prediction['hasil_prediksi'];
    $keterangan = $prediction['keterangan'];
    $tanggal_prediksi = date('Y-m-d H:i:s');

    // Buat laporan prediksi baru
    $sql_laporan = "INSERT INTO laporan_prediksi (tanggal_prediksi, akurasi, parameter) VALUES (?, ?, ?)";
    $stmt_laporan = $koneksi->prepare($sql_laporan);
    $akurasi = 87.5; // Akurasi model backpropagation
    $parameter = "Backpropagation Neural Network (MLP Classifier)";
    $stmt_laporan->bind_param("sds", $tanggal_prediksi, $akurasi, $parameter);
    $stmt_laporan->execute();
    $id_laporan = $koneksi->insert_id;

    // Simpan detail prediksi
    $sql_detail = "INSERT INTO prediksi_detail (id_nasabah, id_laporan, hasil_prediksi, keterangan, tanggal_prediksi) VALUES (?, ?, ?, ?, ?)";
    $stmt_detail = $koneksi->prepare($sql_detail);
    $stmt_detail->bind_param("iisss", $id_nasabah, $id_laporan, $hasil_prediksi, $keterangan, $tanggal_prediksi);
    $stmt_detail->execute();

    // Tambahkan ID laporan ke hasil prediksi
    $prediction['id_laporan'] = $id_laporan;
    $prediction['tanggal_prediksi'] = $tanggal_prediksi;

    // Kembalikan hasil prediksi
    echo json_encode($prediction);
    exit;
}

// Endpoint untuk mendapatkan informasi model
else if ($path === 'model-info' && $method === 'GET') {
    // Kirim request ke model Python
    $model_url = 'http://localhost:5000/api/model-info';
    $ch = curl_init($model_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code !== 200) {
        handleError('Failed to get model information', 500);
    }

    echo $response;
    exit;
}

// Endpoint untuk mendapatkan daftar nasabah
else if ($path === 'nasabah' && $method === 'GET') {
    $sql = "SELECT * FROM nasabah ORDER BY nama_nasabah";
    $result = $koneksi->query($sql);

    $nasabah = [];
    while ($row = $result->fetch_assoc()) {
        $nasabah[] = $row;
    }

    echo json_encode(['data' => $nasabah]);
    exit;
}

// Endpoint untuk mendapatkan detail nasabah
else if (preg_match('/^nasabah\/(\d+)$/', $path, $matches) && $method === 'GET') {
    $id_nasabah = $matches[1];

    $sql = "SELECT * FROM nasabah WHERE id_nasabah = ?";
    $stmt = $koneksi->prepare($sql);
    $stmt->bind_param("i", $id_nasabah);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        handleError("Nasabah dengan ID $id_nasabah tidak ditemukan", 404);
    }

    $nasabah = $result->fetch_assoc();
    echo json_encode($nasabah);
    exit;
}

// Endpoint tidak ditemukan
else {
    handleError('Endpoint not found', 404);
}
?>
