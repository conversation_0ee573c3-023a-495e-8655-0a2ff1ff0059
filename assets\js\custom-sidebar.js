/**
 * Custom Sidebar JavaScript
 * Handles sidebar toggle and active menu highlighting
 */

$(document).ready(function() {
    // Toggle sidebar on hamburger button click
    $('.sidebar-toggle').on('click', function(e) {
        e.preventDefault();
        $('#wrapper').toggleClass('sidebar-toggled');
        $('.sidebar-collapse').toggleClass('collapse');
        
        // Toggle overlay on mobile
        if ($(window).width() < 768) {
            $('.sidebar-overlay').toggleClass('active');
            $('body').toggleClass('sidebar-open');
        }
    });
    
    // Close sidebar when clicking on overlay (mobile only)
    $('.sidebar-overlay').on('click', function() {
        $('#wrapper').addClass('sidebar-toggled');
        $('.sidebar-collapse').addClass('collapse');
        $('.sidebar-overlay').removeClass('active');
        $('body').removeClass('sidebar-open');
    });
    
    // Highlight active menu based on current page URL
    highlightActiveMenu();
    
    // Handle window resize
    $(window).resize(function() {
        handleWindowResize();
    });
    
    // Initial call to handle window size
    handleWindowResize();
});

/**
 * Highlights the active menu item based on the current page URL
 */
function highlightActiveMenu() {
    // Get current page URL
    var currentPage = window.location.pathname.split('/').pop();
    
    // If no page specified, assume it's index
    if (currentPage === '') {
        currentPage = 'index.php';
    }
    
    // Remove active class from all menu items
    $('#side-menu li a').removeClass('active');
    
    // Add active class to current page menu item
    $('#side-menu li a').each(function() {
        var href = $(this).attr('href');
        if (href === currentPage) {
            $(this).addClass('active');
        }
    });
}

/**
 * Handles responsive behavior when window is resized
 */
function handleWindowResize() {
    if ($(window).width() >= 768) {
        // On desktop/tablet
        $('.sidebar-collapse').removeClass('collapse');
        $('.sidebar-overlay').removeClass('active');
        $('body').removeClass('sidebar-open');
    } else {
        // On mobile
        if (!$('#wrapper').hasClass('sidebar-toggled')) {
            $('.sidebar-collapse').addClass('collapse');
        }
    }
}
