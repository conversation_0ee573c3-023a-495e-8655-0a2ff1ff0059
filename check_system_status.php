<?php
// Script untuk mengecek status sistem prediksi

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Diagnosis Sistem Prediksi</h2>\n";

// 1. Cek koneksi database
echo "<h3>1. Status Database</h3>\n";
include 'koneksi.php';
if ($koneksi) {
    echo "✅ Koneksi database berhasil<br>\n";
    
    // Cek tabel nasabah
    $check_nasabah = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM nasabah");
    if ($check_nasabah) {
        $row = mysqli_fetch_assoc($check_nasabah);
        echo "✅ Tabel nasabah: " . $row['total'] . " data<br>\n";
    }
    
    // Cek tabel hasil_prediksi
    $check_prediksi = mysqli_query($koneksi, "SELECT COUNT(*) as total FROM hasil_prediksi");
    if ($check_prediksi) {
        $row = mysqli_fetch_assoc($check_prediksi);
        echo "✅ Tabel hasil_prediksi: " . $row['total'] . " data<br>\n";
    }
} else {
    echo "❌ Koneksi database gagal<br>\n";
}

// 2. Cek server Python
echo "<h3>2. Status Server Python</h3>\n";
$python_url = 'http://localhost:5000/api/predict';

// Test dengan curl
$ch = curl_init($python_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['test' => true]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "❌ Server Python tidak berjalan<br>\n";
    echo "Error: $curl_error<br>\n";
    echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
    echo "<strong>🚨 Server Python Tidak Aktif!</strong><br>";
    echo "Untuk menjalankan server Python:<br>";
    echo "1. Buka Command Prompt<br>";
    echo "2. Masuk ke direktori: <code>cd c:\\laragon\\www\\ngasal</code><br>";
    echo "3. Jalankan: <code>python model_wrapper.py</code><br>";
    echo "4. Atau double-click: <code>start_model_server.bat</code><br>";
    echo "</div>";
} else {
    echo "✅ Server Python berjalan (HTTP $http_code)<br>\n";
    echo "Response: " . htmlspecialchars($response) . "<br>\n";
}

// 3. Cek file-file penting
echo "<h3>3. Status File Sistem</h3>\n";
$files_to_check = [
    'api_predict.php' => 'API Prediksi PHP',
    'model_wrapper.py' => 'Model Wrapper Python',
    'start_model_server.bat' => 'Batch File Server',
    'prediksi_baru.php' => 'Form Prediksi Baru',
    'hasil_prediksi.php' => 'Halaman Hasil'
];

foreach ($files_to_check as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ $desc ($file)<br>\n";
    } else {
        echo "❌ $desc ($file) - FILE NOT FOUND<br>\n";
    }
}

// 4. Test fungsi prediksi fallback
echo "<h3>4. Test Fungsi Prediksi Fallback</h3>\n";
if (file_exists('api_predict.php')) {
    require_once 'api_predict.php';
    
    // Data test
    $test_data = [
        'nama_nasabah' => 'Test User',
        'penghasilan' => 5000000,
        'jumlah_tanggungan' => 2,
        'jumlah_pinjaman' => 20000000,
        'jangka_waktu' => 12,
        'umur' => 30
    ];
    
    try {
        if (function_exists('prediksi_dengan_backpropagation')) {
            $hasil = prediksi_dengan_backpropagation($test_data);
            echo "✅ Fungsi prediksi fallback berjalan<br>\n";
            echo "Hasil test: " . $hasil['hasil_prediksi'] . " (" . number_format($hasil['probabilitas'] * 100, 2) . "%)<br>\n";
        } else {
            echo "❌ Fungsi prediksi_dengan_backpropagation tidak ditemukan<br>\n";
        }
    } catch (Exception $e) {
        echo "❌ Error fungsi prediksi: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "❌ File api_predict.php tidak ditemukan<br>\n";
}

// 5. Cek log error PHP
echo "<h3>5. Log Error PHP (10 baris terakhir)</h3>\n";
$error_log_file = ini_get('error_log');
if ($error_log_file && file_exists($error_log_file)) {
    $lines = file($error_log_file);
    $last_lines = array_slice($lines, -10);
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;'>";
    foreach ($last_lines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</pre>";
} else {
    echo "ℹ️ Log error PHP tidak ditemukan atau tidak dikonfigurasi<br>\n";
}

// 6. Test form submission
echo "<h3>6. Test Form Submission</h3>\n";
if (isset($_POST['test_submit'])) {
    echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
    echo "<strong>✅ Form submission berhasil!</strong><br>";
    echo "Data yang diterima:<br>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "</div>";
}

echo "<form method='post'>";
echo "<input type='hidden' name='test_submit' value='1'>";
echo "<input type='text' name='test_name' placeholder='Nama Test' required>";
echo "<button type='submit' style='padding: 5px 10px; margin-left: 10px;'>Test Submit</button>";
echo "</form>";

// 7. Rekomendasi
echo "<h3>7. 🎯 Rekomendasi Perbaikan</h3>\n";
echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; margin: 10px 0;'>";

if ($response === false) {
    echo "<strong>PRIORITAS UTAMA:</strong><br>";
    echo "1. 🚨 Jalankan server Python terlebih dahulu<br>";
    echo "2. 🔄 Test lagi setelah server Python aktif<br>";
    echo "3. 📝 Gunakan sistem fallback jika server Python bermasalah<br>";
} else {
    echo "<strong>SERVER PYTHON AKTIF - Cek hal berikut:</strong><br>";
    echo "1. 🔍 Periksa console browser untuk error JavaScript<br>";
    echo "2. 📋 Test dengan form debug sederhana<br>";
    echo "3. 🔧 Periksa konfigurasi PHP error reporting<br>";
}

echo "</div>";

echo "<hr>";
echo "<h3>🔗 Link Test:</h3>";
echo "<p>";
echo "<a href='debug_prediksi_baru.php' style='margin-right: 10px;'>🔍 Debug Form</a>";
echo "<a href='prediksi_baru.php' style='margin-right: 10px;'>📝 Form Asli</a>";
echo "<a href='restart_python_server.php' style='margin-right: 10px;'>🐍 Restart Python</a>";
echo "</p>";
?>
