import json
import sys
import numpy as np
from sklearn.preprocessing import StandardScaler
import pickle
import os
from datetime import datetime

# Konfigurasi path model
MODEL_DIR = os.path.join(os.path.dirname(__file__), 'model')
MODEL_PATH = os.path.join(MODEL_DIR, 'backpropagation_model.pkl')
SCALER_PATH = os.path.join(MODEL_DIR, 'scaler.pkl')

def log_error(message):
    """Mencatat error ke log file"""
    with open('python_error.log', 'a') as f:
        f.write(f"{datetime.now().isoformat()} - ERROR - {message}\n")

def load_model():
    """Memuat model dan scaler yang sudah dilatih"""
    try:
        if not os.path.exists(MODEL_PATH) or not os.path.exists(SCALER_PATH):
            raise FileNotFoundError("File model atau scaler tidak ditemukan")

        with open(MODEL_PATH, 'rb') as f:
            model = pickle.load(f)
        with open(SCALER_PATH, 'rb') as f:
            scaler = pickle.load(f)
        return model, scaler
    except Exception as e:
        log_error(f"Gagal memuat model: {str(e)}")
        raise

def preprocess_data(data, scaler):
    """Mempersiapkan data untuk prediksi"""
    try:
        # Validasi data input
        required_fields = ['penghasilan', 'jumlah_tanggungan', 'jumlah_pinjaman',
                         'pekerjaan', 'jaminan', 'kepemilikan_rumah', 'umur']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Field {field} tidak ditemukan dalam data input")

        # Ekstrak dan validasi nilai
        penghasilan = float(data['penghasilan'])
        jumlah_tanggungan = max(1, int(data['jumlah_tanggungan']))
        jumlah_pinjaman = float(data['jumlah_pinjaman'])
        tahun_kendaraan = int(data.get('tahun_kendaraan', 0))
        umur = int(data['umur'])

        if penghasilan <= 0 or jumlah_pinjaman <= 0 or umur <= 0:
            raise ValueError("Nilai numerik harus positif")

        # Hitung fitur turunan
        penghasilan_per_kapita = penghasilan / jumlah_tanggungan
        # Rasio pinjaman terhadap penghasilan per kapita tahunan
        rasio_pinjaman = jumlah_pinjaman / (penghasilan_per_kapita * 12)

        # Encode fitur kategorikal
        pekerjaan_mapping = {
            'pns': 0, 'pegawai negeri': 0, 'dokter': 0, 'dosen': 0,
            'karyawan': 1, 'pegawai swasta': 1, 'guru': 1,
            'wiraswasta': 2, 'pengusaha': 2,
            'freelancer': 3, 'petani': 3, 'nelayan': 3
        }
        pekerjaan = data['pekerjaan'].lower()
        pekerjaan_encoded = pekerjaan_mapping.get(pekerjaan, 4)

        jaminan_mapping = {
            'bpkb mobil': 0,
            'bpkb motor': 1
        }
        jaminan = data['jaminan'].lower()
        jaminan_encoded = jaminan_mapping.get(jaminan, 2)

        status_pajak = data.get('status_pajak', '').lower()
        status_pajak_encoded = 1 if status_pajak == 'aktif' else 0

        kepemilikan_rumah_mapping = {
            'milik sendiri': 0, 'sendiri': 0,
            'keluarga': 1, 'orang tua': 1,
            'kontrak': 2, 'sewa': 2
        }
        kepemilikan_rumah = data['kepemilikan_rumah'].lower()
        kepemilikan_rumah_encoded = kepemilikan_rumah_mapping.get(kepemilikan_rumah, 3)

        # Susun array fitur
        features = np.array([
            penghasilan,
            jumlah_tanggungan,
            jumlah_pinjaman,
            penghasilan_per_kapita,
            rasio_pinjaman,
            pekerjaan_encoded,
            jaminan_encoded,
            tahun_kendaraan,
            status_pajak_encoded,
            kepemilikan_rumah_encoded,
            umur
        ]).reshape(1, -1)

        # Normalisasi fitur
        features_scaled = scaler.transform(features)

        return features_scaled, {
            'penghasilan_per_kapita': penghasilan_per_kapita,
            'rasio_pinjaman': rasio_pinjaman,
            'pekerjaan': pekerjaan,
            'jaminan': jaminan,
            'kepemilikan_rumah': kepemilikan_rumah
        }

    except Exception as e:
        log_error(f"Gagal preprocessing data: {str(e)}")
        raise

def generate_explanation(prediction, probability, data, features_info):
    """Menghasilkan penjelasan untuk hasil prediksi"""
    try:
        explanation = []

        if prediction == 1:  # Layak
            main_msg = f"Nasabah diprediksi LAYAK dengan probabilitas {probability:.2%}."
            explanation.append(main_msg)

            supporting_factors = []

            # Faktor penghasilan dan tanggungan
            jumlah_tanggungan = max(1, int(data['jumlah_tanggungan']))
            penghasilan_per_kapita = data['penghasilan'] / jumlah_tanggungan

            if penghasilan_per_kapita > 3000000:
                supporting_factors.append(f"penghasilan per kapita tinggi (Rp {penghasilan_per_kapita:,.0f})")
            elif penghasilan_per_kapita > 2000000:
                supporting_factors.append(f"penghasilan per kapita cukup (Rp {penghasilan_per_kapita:,.0f})")

            # Faktor tanggungan
            if data['jumlah_tanggungan'] <= 2:
                supporting_factors.append(f"jumlah tanggungan sedikit ({data['jumlah_tanggungan']} orang)")

            # Rasio pinjaman terhadap penghasilan per kapita
            rasio_pinjaman_tahunan = data['jumlah_pinjaman'] / (penghasilan_per_kapita * 12)
            if rasio_pinjaman_tahunan <= 0.5:
                supporting_factors.append(f"rasio pinjaman terhadap penghasilan per kapita tahunan baik ({rasio_pinjaman_tahunan:.2f})")

            # Faktor pekerjaan
            if features_info['pekerjaan'] in ['pns', 'pegawai negeri', 'dokter', 'dosen']:
                supporting_factors.append("pekerjaan sangat stabil")
            elif features_info['pekerjaan'] in ['karyawan', 'pegawai swasta', 'guru']:
                supporting_factors.append("pekerjaan stabil")

            # Faktor jaminan
            tahun_kendaraan = int(data.get('tahun_kendaraan', 0))
            status_pajak = data.get('status_pajak', '').lower()
            from datetime import datetime
            tahun_sekarang = datetime.now().year
            umur_kendaraan = tahun_sekarang - tahun_kendaraan if tahun_kendaraan > 0 else 999

            if features_info['jaminan'] == 'bpkb mobil':
                if umur_kendaraan <= 5 and status_pajak == 'aktif':
                    supporting_factors.append(f"jaminan BPKB mobil dengan kondisi baik (umur {umur_kendaraan} tahun) dan pajak aktif")
                elif umur_kendaraan <= 10 and status_pajak == 'aktif':
                    supporting_factors.append(f"jaminan BPKB mobil dengan pajak aktif")
                else:
                    supporting_factors.append("jaminan BPKB mobil")
            elif features_info['jaminan'] == 'bpkb motor' and status_pajak == 'aktif':
                supporting_factors.append("jaminan BPKB motor dengan pajak aktif")

            if supporting_factors:
                explanation.append("Faktor pendukung: " + ", ".join(supporting_factors) + ".")

        else:  # Tidak Layak
            main_msg = f"Nasabah diprediksi TIDAK LAYAK dengan probabilitas {(1-probability):.2%}."
            explanation.append(main_msg)

            risk_factors = []

            # Faktor penghasilan dan tanggungan
            jumlah_tanggungan = max(1, int(data['jumlah_tanggungan']))
            penghasilan_per_kapita = data['penghasilan'] / jumlah_tanggungan

            if penghasilan_per_kapita < 1500000:
                risk_factors.append(f"penghasilan per kapita rendah (Rp {penghasilan_per_kapita:,.0f})")

            # Faktor tanggungan
            if data['jumlah_tanggungan'] > 3:
                risk_factors.append(f"jumlah tanggungan banyak ({data['jumlah_tanggungan']} orang)")

            # Faktor pekerjaan
            if features_info['pekerjaan'] in ['freelancer', 'petani', 'nelayan']:
                risk_factors.append("pekerjaan kurang stabil")

            # Faktor rasio pinjaman terhadap penghasilan per kapita
            rasio_pinjaman_tahunan = data['jumlah_pinjaman'] / (penghasilan_per_kapita * 12)
            if rasio_pinjaman_tahunan > 1.0:
                risk_factors.append(f"rasio pinjaman terhadap penghasilan per kapita tahunan tinggi ({rasio_pinjaman_tahunan:.2f})")

            # Faktor jaminan
            tahun_kendaraan = int(data.get('tahun_kendaraan', 0))
            status_pajak = data.get('status_pajak', '').lower()
            from datetime import datetime
            tahun_sekarang = datetime.now().year
            umur_kendaraan = tahun_sekarang - tahun_kendaraan if tahun_kendaraan > 0 else 999

            if features_info['jaminan'] == 'bpkb motor':
                if umur_kendaraan > 8:
                    risk_factors.append(f"jaminan BPKB motor dengan umur kendaraan yang sudah tua ({umur_kendaraan} tahun)")
                if status_pajak != 'aktif':
                    risk_factors.append("jaminan BPKB motor dengan status pajak tidak aktif")
            elif features_info['jaminan'] == 'bpkb mobil':
                if umur_kendaraan > 10:
                    risk_factors.append(f"jaminan BPKB mobil dengan umur kendaraan yang sudah tua ({umur_kendaraan} tahun)")
                if status_pajak != 'aktif':
                    risk_factors.append("jaminan BPKB mobil dengan status pajak tidak aktif")

            if risk_factors:
                explanation.append("Faktor risiko: " + ", ".join(risk_factors) + ".")

        return " ".join(explanation)

    except Exception as e:
        log_error(f"Gagal generate explanation: {str(e)}")
        return "Prediksi menggunakan model backpropagation neural network"

def main():
    try:
        # Baca input dari PHP
        if len(sys.argv) < 2:
            raise ValueError("Input file tidak diberikan")

        input_file = sys.argv[1]
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"File input {input_file} tidak ditemukan")

        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Muat model dan scaler
        model, scaler = load_model()

        # Preprocess data
        features, features_info = preprocess_data(data, scaler)

        # Lakukan prediksi
        probability = model.predict_proba(features)[0][1]  # Probabilitas kelas positif
        prediction = 1 if probability >= 0.5 else 0

        # Generate penjelasan
        keterangan = generate_explanation(prediction, probability, data, features_info)

        # Hasil prediksi
        result = {
            'hasil_prediksi': 'Layak' if prediction == 1 else 'Tidak Layak',
            'probabilitas': float(probability),
            'keterangan': keterangan,
            'skor': int(probability * 100),
            'max_skor': 100,
            'status': 'success'
        }

    except Exception as e:
        log_error(f"Error dalam proses prediksi: {str(e)}")
        result = {
            'status': 'error',
            'message': str(e)
        }

    # Output hasil dalam format JSON
    print(json.dumps(result, ensure_ascii=False))

if __name__ == "__main__":
    main()