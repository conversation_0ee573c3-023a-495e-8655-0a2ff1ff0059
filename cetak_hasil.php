<?php
// <PERSON>lai session di awal file sebelum output apapun
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include 'koneksi.php';
include 'date_helper.php';

// Cek apakah ada parameter id_prediksi
if (!isset($_GET['id_prediksi'])) {
    // Redirect ke halaman proses prediksi jika parameter tidak lengkap
    header("Location: proses_prediksi.php");
    exit;
}

$id_prediksi = $_GET['id_prediksi'];

// Tambahkan log untuk debugging
error_log("Cetak hasil prediksi untuk ID prediksi: " . $id_prediksi);

// Pertama, ambil data prediksi dari tabel hasil_prediksi
$query_hasil = "SELECT * FROM hasil_prediksi WHERE id_prediksi = ?";
$stmt_hasil = $koneksi->prepare($query_hasil);
$stmt_hasil->bind_param("i", $id_prediksi);
$stmt_hasil->execute();
$result_hasil = $stmt_hasil->get_result();
$prediksi = $result_hasil->fetch_assoc();

// Jika tidak ditemukan di hasil_prediksi, coba cari di prediksi_detail
if (!$prediksi) {
    error_log("Data tidak ditemukan di hasil_prediksi, mencoba prediksi_detail");
    $query_prediksi = "SELECT pd.*, lp.akurasi, lp.parameter
                      FROM prediksi_detail pd
                      LEFT JOIN laporan_prediksi lp ON pd.id_laporan = lp.id_laporan
                      WHERE pd.id_prediksi = ?";
    $stmt_prediksi = $koneksi->prepare($query_prediksi);
    $stmt_prediksi->bind_param("i", $id_prediksi);
    $stmt_prediksi->execute();
    $result_prediksi = $stmt_prediksi->get_result();
    $prediksi = $result_prediksi->fetch_assoc();
}

// Jika data prediksi ditemukan, ambil ID nasabah
if ($prediksi) {
    $id_nasabah = $prediksi['id_nasabah'];
    error_log("Data prediksi ditemukan, ID nasabah: " . $id_nasabah);

    // Ambil data nasabah berdasarkan ID nasabah
    $query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
    $stmt_nasabah = $koneksi->prepare($query_nasabah);
    $stmt_nasabah->bind_param("i", $id_nasabah);
    $stmt_nasabah->execute();
    $result_nasabah = $stmt_nasabah->get_result();
    $nasabah = $result_nasabah->fetch_assoc();

    // Gabungkan data prediksi dan nasabah
    $data = array_merge($prediksi, $nasabah);
} else {
    error_log("Data prediksi tidak ditemukan untuk ID: " . $id_prediksi);
    // Redirect ke halaman proses prediksi
    header("Location: proses_prediksi.php");
    exit;
}

// Jika data tidak ditemukan
if (!$data) {
    error_log("Data gabungan tidak ditemukan");
    // Redirect ke halaman proses prediksi
    header("Location: proses_prediksi.php");
    exit;
}

// Fungsi untuk mendapatkan keterangan tambahan berdasarkan hasil prediksi
function getKeteranganTambahan($nasabah, $prediksi) {
    // Log untuk debugging
    error_log("getKeteranganTambahan dipanggil dengan hasil_prediksi: " . $prediksi['hasil_prediksi']);

    $keterangan = [];

    // Pastikan penghasilan dan jumlah pinjaman adalah nilai numerik
    $penghasilan = is_numeric($nasabah['penghasilan']) ? floatval($nasabah['penghasilan']) : floatval(str_replace('.', '', $nasabah['penghasilan']));
    $jumlah_pinjaman = is_numeric($nasabah['jumlah_pinjaman']) ? floatval($nasabah['jumlah_pinjaman']) : floatval(str_replace('.', '', $nasabah['jumlah_pinjaman']));
    $jumlah_tanggungan = isset($nasabah['jumlah_tanggungan']) && $nasabah['jumlah_tanggungan'] > 0 ? intval($nasabah['jumlah_tanggungan']) : 1;
    $penghasilan_per_kapita = $penghasilan / $jumlah_tanggungan;
    $rasio_pinjaman = $jumlah_pinjaman / $penghasilan;

    // Keterangan berdasarkan hasil prediksi
    if ($prediksi['hasil_prediksi'] == 'Layak') {
        // Keterangan untuk nasabah yang LAYAK
        $keterangan[] = "Nasabah memiliki profil kredit yang baik.";

        // Keterangan berdasarkan penghasilan dan jumlah tanggungan
        if ($penghasilan_per_kapita > 5000000) {
            $keterangan[] = "Penghasilan per anggota keluarga di atas rata-rata (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . "), menunjukkan kemampuan membayar yang baik.";
        } else if ($penghasilan_per_kapita > 2000000) {
            $keterangan[] = "Penghasilan per anggota keluarga cukup memadai (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . ").";
        }

        // Keterangan berdasarkan pekerjaan
        if ($nasabah['pekerjaan'] == 'PNS' || $nasabah['pekerjaan'] == 'Profesional') {
            $keterangan[] = "Pekerjaan nasabah memiliki stabilitas yang baik.";
        } else if ($nasabah['pekerjaan'] == 'Wiraswasta') {
            $keterangan[] = "Nasabah memiliki usaha yang dapat menjadi sumber penghasilan yang stabil.";
        }

        // Keterangan berdasarkan jaminan
        if ($nasabah['jaminan'] == 'BPKB Mobil') {
            $keterangan[] = "Nasabah memiliki jaminan kendaraan roda empat yang bernilai tinggi.";
        } elseif ($nasabah['jaminan'] == 'BPKB Motor') {
            $keterangan[] = "Nasabah memiliki jaminan kendaraan roda dua yang cukup memadai.";
        }

        // Keterangan berdasarkan kepemilikan rumah
        if ($nasabah['kepemilikan_rumah'] == 'Milik Sendiri') {
            $keterangan[] = "Nasabah memiliki tempat tinggal sendiri, menunjukkan stabilitas finansial.";
        }
    } else {
        // Keterangan untuk nasabah yang TIDAK LAYAK
        $keterangan[] = "Nasabah memiliki risiko kredit yang tinggi.";

        // Keterangan berdasarkan rasio pinjaman terhadap penghasilan per kapita
        if ($rasio_pinjaman > 24) { // Lebih dari 2 tahun penghasilan
            $keterangan[] = "Rasio pinjaman terhadap penghasilan per kapita yang tinggi, menunjukkan beban keuangan yang berat.";
        }

        // Keterangan berdasarkan jumlah tanggungan
        if ($jumlah_tanggungan > 3) {
            $keterangan[] = "Jumlah tanggungan yang banyak (" . $jumlah_tanggungan . " orang) memengaruhi kemampuan membayar.";
        }

        // Keterangan berdasarkan penghasilan per kapita
        if ($penghasilan_per_kapita < 2000000) {
            $keterangan[] = "Penghasilan per anggota keluarga terlalu rendah (Rp " . number_format($penghasilan_per_kapita, 0, ',', '.') . ").";
        }

        // Keterangan berdasarkan pekerjaan
        if ($nasabah['pekerjaan'] == 'Wiraswasta' || $nasabah['pekerjaan'] == 'Lainnya') {
            $keterangan[] = "Pekerjaan yang kurang stabil, meningkatkan risiko kredit.";
        }

        // Keterangan berdasarkan jaminan
        if ($nasabah['jaminan'] == 'BPKB Motor') {
            // Cek tahun kendaraan jika ada
            if (isset($nasabah['tahun_kendaraan']) && $nasabah['tahun_kendaraan'] < date('Y') - 7) {
                $keterangan[] = "Jaminan kendaraan bermotor terlalu tua (tahun " . $nasabah['tahun_kendaraan'] . ").";
            } else {
                $keterangan[] = "Nilai jaminan BPKB Motor kurang memadai untuk jumlah pinjaman yang diajukan.";
            }

            // Cek status pajak jika ada
            if (isset($nasabah['status_pajak']) && $nasabah['status_pajak'] == 'Tidak Aktif') {
                $keterangan[] = "Status pajak kendaraan tidak aktif, menunjukkan potensi masalah administratif.";
            }
        }

        // Keterangan berdasarkan kepemilikan rumah
        if ($nasabah['kepemilikan_rumah'] == 'Kontrak') {
            $keterangan[] = "Status kepemilikan rumah yang kurang baik (kontrak), menunjukkan kurangnya aset tetap.";
        } elseif ($nasabah['kepemilikan_rumah'] == 'Orang Tua') {
            $keterangan[] = "Status kepemilikan rumah yang kurang baik (menumpang di rumah orang tua), menunjukkan keterbatasan aset.";
        }
    }

    // Log keterangan yang dihasilkan
    error_log("Keterangan tambahan yang dihasilkan: " . implode(", ", $keterangan));

    return $keterangan;
}

// Dapatkan keterangan tambahan
// Pastikan kita menggunakan data nasabah yang benar
$nasabah_data = [];
foreach ($data as $key => $value) {
    if (in_array($key, ['nama_nasabah', 'jenis_kelamin', 'status_perkawinan', 'pekerjaan',
                        'penghasilan', 'jumlah_tanggungan', 'jumlah_pinjaman', 'jangka_waktu',
                        'jaminan', 'tahun_kendaraan', 'status_pajak', 'kepemilikan_rumah', 'umur'])) {
        $nasabah_data[$key] = $value;
    }
}
$prediksi_data = [];
foreach ($data as $key => $value) {
    if (in_array($key, ['hasil_prediksi', 'probabilitas', 'keterangan', 'tanggal_prediksi'])) {
        $prediksi_data[$key] = $value;
    }
}
$keterangan_tambahan = getKeteranganTambahan($nasabah_data, $prediksi_data);

// Fungsi untuk memformat angka menjadi format rupiah
function formatRupiah($angka) {
    return "Rp " . number_format($angka, 0, ',', '.');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surat Hasil Analisis Kelayakan Kredit</title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            line-height: 1.4;
            margin: 0;
            padding: 10px;
            color: #333;
            font-size: 12px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 10px;
        }
        .letterhead {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 3px double #000;
            padding-bottom: 5px;
            position: relative;
        }
        .logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.logo-image {
    width: 100px;  /* Ubah dari 60px ke 100px atau sesuai kebutuhan */
    height: 100px; /* Ubah dari 60px ke 100px atau sesuai kebutuhan */
    margin-right: 20px; /* Sesuaikan margin */
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}
        .logo-text {
            text-align: center;
        }
        .logo-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 2px;
            color:rgb(118, 40, 26);
        }
        .company-info {
            font-size: 10px;
            margin-bottom: 2px;
        }
        .letter-info {
            margin-bottom: 15px;
        }
        .letter-info table {
            width: 100%;
            border: none;
        }
        .letter-info td {
            vertical-align: top;
            padding: 1px 3px;
            border: none;
            font-size: 11px;
        }
        .letter-body {
            text-align: justify;
            margin-bottom: 15px;
        }
        .letter-body p {
            margin-bottom: 8px;
        }
        .result-box {
            border: 1px solid #000;
            padding: 5px;
            margin: 10px 0;
            text-align: center;
        }
        .result-box.layak {
            border: 2px solid #3c763d;
        }
        .result-box.tidak-layak {
            border: 2px solid #a94442;
        }
        .result-box h2 {
            margin: 0;
            font-size: 14px;
        }
        .result-box p {
            margin: 5px 0 0;
            font-size: 12px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table, .data-table th, .data-table td {
            border: 1px solid #000;
        }
        .data-table th, .data-table td {
            padding: 4px;
            text-align: left;
            font-size: 11px;
        }
        .data-table th {
            background-color: #f5f5f5;
        }
        .signature-section {
            margin-top: 20px;
            text-align: right;
        }
        .signature {
            width: 200px;
            display: inline-block;
            text-align: center;
        }
        .signature p {
            margin: 2px 0;
            font-size: 11px;
        }
        .signature .line {
            margin: 30px auto 5px;
            border-bottom: 1px solid #000;
            width: 80%;
        }
        .footer {
            margin-top: 15px;
            text-align: center;
            font-size: 9px;
            color: #777;
            border-top: 1px solid #ddd;
            padding-top: 5px;
        }
        @media print {
            @page {
                size: A4;
                margin: 1cm;
            }
            body {
                padding: 0;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="letterhead">
            <div class="logo-container">
                <div class="logo-image">
                    <img src = assets/img/logo.png width="500px" height="500px">
                </div>
                <div class="logo-text">
                    <div class="logo-name">PT. CLIPAN FINANCE INDONESIA Tbk.</div>
                    <div class="company-info">Jl. Raya Cilegon Drangong Serang - Banten Jl. Legok Dalam, Drangong, Kec. Taktakan, Kota Serang, Banten 42162</div>
                    <div class="company-info">Telp: (*************</div>
                </div>
            </div>
        </div>

        <div class="letter-info">
            <table>
                <tr>
                    <td width="120">Nomor</td>
                    <td>: <?php echo 'KRD/' . date('Ymd') . '/' . $id_prediksi; ?></td>
                    <td width="120" align="right">Tanggal</td>
                    <td width="150" align="right">: <?php echo format_tanggal(date('Y-m-d H:i:s'), 'd/m/Y'); ?></td>
                </tr>
                <tr>
                    <td>Lampiran</td>
                    <td>: -</td>
                    <td colspan="2"></td>
                </tr>
                <tr>
                    <td>Perihal</td>
                    <td colspan="3">: <strong>Hasil Analisis Kelayakan Kredit</strong></td>
                </tr>
            </table>
        </div>

        <div class="letter-body">
            <?php
            // Tambahkan debugging info jika diperlukan
            if (isset($_GET['debug']) && $_GET['debug'] == 1):
            ?>
            <div class="alert alert-info" style="font-size: 10px;">
                <h4>Debug Info:</h4>
                <p>ID Prediksi: <?php echo $id_prediksi; ?></p>
                <p>ID Nasabah: <?php echo $id_nasabah; ?></p>
                <p>Nama Nasabah: <?php echo htmlspecialchars($data['nama_nasabah'] ?? 'Tidak ada'); ?></p>
                <pre><?php print_r($data); ?></pre>
            </div>
            <?php endif; ?>

            <p>Kepada Yth, <strong><?php echo htmlspecialchars($data['nama_nasabah']); ?></strong> di Tempat</p>

            <p>Dengan hormat,</p>

            <p>Berdasarkan permohonan kredit tertanggal <?php
                // Gunakan fungsi helper untuk format tanggal
                echo format_tanggal($data['tanggal_prediksi'], 'd/m/Y');
            ?>,
            berikut hasil analisis kelayakan kredit Bapak/Ibu:</p>

            <div class="result-box <?php echo $data['hasil_prediksi'] == 'Layak' ? 'layak' : 'tidak-layak'; ?>">
                <h2>HASIL ANALISIS: <?php echo strtoupper($data['hasil_prediksi']); ?></h2>
                <p>Dengan tingkat kepercayaan: <?php
                    // Tampilkan probabilitas yang konsisten
                    $prob = $data['probabilitas'];

                    // Pastikan probabilitas dalam format yang benar
                    if (is_string($prob)) {
                        $prob = floatval($prob);
                    }

                    // Gunakan probabilitas dari algoritma, pastikan dalam format yang benar
                    // Jika probabilitas terlalu ekstrim (0 atau 1), buat sedikit lebih realistis
                    if ($prob >= 0.99) {
                        $prob = 0.99;
                    } else if ($prob <= 0.01) {
                        $prob = 0.01;
                    }

                    // Pastikan probabilitas konsisten dengan hasil prediksi
                    if (($data['hasil_prediksi'] == 'Tidak Layak' && $prob > 0.5) ||
                        ($data['hasil_prediksi'] == 'Layak' && $prob < 0.5)) {
                        $prob = 1 - $prob; // Koreksi probabilitas jika tidak konsisten
                    }

                    // Simpan probabilitas yang sudah dimodifikasi untuk digunakan di keterangan
                    $GLOBALS['adjusted_probability'] = $prob;

                    // Jika probabilitas sudah dikoreksi, simpan kembali ke database
                    if (abs($prob - floatval($data['probabilitas'])) > 0.01) {
                        // Update probabilitas di database
                        $query_update_prob = "UPDATE hasil_prediksi SET probabilitas = ? WHERE id_prediksi = ?";
                        $stmt_update_prob = $koneksi->prepare($query_update_prob);
                        $stmt_update_prob->bind_param("di", $prob, $id_prediksi);
                        $stmt_update_prob->execute();
                        error_log("Probabilitas diupdate di database: " . $prob . " untuk ID prediksi " . $id_prediksi);

                        // Update juga di prediksi_detail jika ada
                        $query_update_detail = "UPDATE prediksi_detail SET probabilitas = ? WHERE id_prediksi = ?";
                        $stmt_update_detail = $koneksi->prepare($query_update_detail);
                        $stmt_update_detail->bind_param("di", $prob, $id_prediksi);
                        $stmt_update_detail->execute();
                    }

                    // Tampilkan probabilitas yang sesuai dengan hasil prediksi
                    // Untuk hasil "Layak", tampilkan probabilitas asli
                    // Untuk hasil "Tidak Layak", tampilkan 1 - probabilitas (karena kita ingin menampilkan tingkat kepercayaan)
                    $display_prob = $data['hasil_prediksi'] == 'Layak' ? $prob : (1 - $prob);
                    echo number_format($display_prob * 100, 2);
                ?>%</p>
            </div>

            <p><strong>Data Pemohon:</strong></p>
            <table class="data-table">
                <tr>
                    <th width="25%">Nama Lengkap</th>
                    <td width="25%"><?php echo htmlspecialchars($data['nama_nasabah']); ?></td>
                    <th width="25%">Pekerjaan</th>
                    <td width="25%"><?php echo htmlspecialchars($data['pekerjaan']); ?></td>
                </tr>
                <tr>
                    <th>Usia / Jenis Kelamin</th>
                    <td><?php echo $data['umur']; ?> tahun / <?php echo htmlspecialchars($data['jenis_kelamin']); ?></td>
                    <th>Status Perkawinan</th>
                    <td><?php echo htmlspecialchars($data['status_perkawinan']); ?></td>
                </tr>
                <tr>
                    <th>Penghasilan</th>
                    <td><?php echo formatRupiah($data['penghasilan']); ?></td>
                    <th>Jumlah Tanggungan</th>
                    <td><?php echo isset($data['jumlah_tanggungan']) ? $data['jumlah_tanggungan'] : '0'; ?> orang</td>
                </tr>
                <tr>
                    <th>Jumlah Pinjaman</th>
                    <td><?php echo formatRupiah($data['jumlah_pinjaman']); ?></td>
                    <th>Jangka Waktu</th>
                    <td><?php echo $data['jangka_waktu']; ?> bulan</td>
                </tr>
                <tr>
                    <th>Jaminan</th>
                    <td><?php echo htmlspecialchars($data['jaminan']); ?></td>
                    <th>Kepemilikan Rumah</th>
                    <td><?php echo htmlspecialchars($data['kepemilikan_rumah']); ?></td>
                </tr>
            </table>

            <p><strong>Hasil Analisis:</strong> <?php
                // Ambil keterangan asli dari model Python
                $keterangan = $data['keterangan'];

                // Log keterangan asli untuk debugging
                error_log("Keterangan asli dari model Python (cetak_hasil.php): " . $keterangan);

                // Tampilkan keterangan asli dari model Python
                echo $keterangan;
            ?></p>

            <?php
            // Regenerasi keterangan tambahan dengan probabilitas yang sudah dikoreksi
            // dan pastikan hasil_prediksi digunakan dengan benar
            if (isset($GLOBALS['adjusted_probability'])) {
                // Dapatkan keterangan tambahan baru dengan probabilitas yang sudah dikoreksi
                $prediksi_data['probabilitas'] = $GLOBALS['adjusted_probability'];

                // Pastikan hasil_prediksi digunakan dengan benar
                $prediksi_data['hasil_prediksi'] = $data['hasil_prediksi'];

                // Log untuk debugging
                error_log("Regenerasi keterangan tambahan dengan hasil_prediksi: " . $prediksi_data['hasil_prediksi']);

                $keterangan_tambahan = getKeteranganTambahan($nasabah_data, $prediksi_data);
            }

            // Batasi jumlah keterangan tambahan yang ditampilkan
            $max_keterangan = 3;
            $keterangan_display = array_slice($keterangan_tambahan, 0, $max_keterangan);
            ?>

            <p><strong>Pertimbangan Utama:</strong>
            <?php echo implode('. ', $keterangan_display); ?>
            </p>

            <?php if ($data['hasil_prediksi'] == 'Layak'): ?>
            <p>Berdasarkan hasil analisis di atas, kami dengan senang hati memberitahukan bahwa permohonan kredit Bapak/Ibu <strong>DISETUJUI</strong>.
            Silakan datang ke kantor kami untuk proses pencairan dana dan penandatanganan perjanjian kredit.</p>
            <?php else: ?>
            <p>Berdasarkan hasil analisis di atas, dengan berat hati kami sampaikan bahwa permohonan kredit Bapak/Ibu <strong>BELUM DAPAT DISETUJUI</strong> pada saat ini.
            Anda dapat mengajukan permohonan kembali setelah 3 bulan.</p>
            <?php endif; ?>

            <p>Demikian surat ini kami sampaikan. Atas perhatian dan kerja sama Bapak/Ibu, kami ucapkan terima kasih.</p>
        </div>

        <div class="signature-section">
            <div class="signature">
                <p>Hormat kami,<br>PT. CLIPAN FINACE INDONESIA Tbk.</p>
                <div class="line"></div>
                <p><strong><?php echo htmlspecialchars($_SESSION['username'] ?? 'Analis Kredit'); ?></strong><br>Analis Kredit</p>
            </div>
        </div>

        <div class="footer">
            <p>Dokumen ini bersifat rahasia dan hanya diperuntukkan bagi pihak yang namanya tercantum dalam dokumen ini.</p>
            <p>Dicetak pada <?php echo date('d/m/Y H:i'); ?> | Analisis dengan Model Backpropagation (akurasi <?php echo $data['akurasi'] ?? '87.5'; ?>%)</p>
        </div>
    </div>

    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print();" style="padding: 10px 20px; background-color: #337ab7; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Cetak Surat
        </button>
        <button onclick="window.close();" style="padding: 10px 20px; background-color: #d9534f; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
            Tutup
        </button>
    </div>
</body>
</html>
