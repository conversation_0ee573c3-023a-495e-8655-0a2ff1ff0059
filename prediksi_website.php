<?php
// <PERSON><PERSON> output buffering
ob_start();

include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);
require_once 'nav_analis.php';

// Fungsi untuk memformat angka menjadi format rupiah
function formatRupiah($angka) {
    return "Rp " . number_format($angka, 0, ',', '.');
}

/**
 * Fungsi untuk memeriksa apakah server model Python sedang berjalan
 */
function is_model_server_running() {
    $api_url = 'http://localhost:5000/api/info';

    // Inisialisasi cURL dengan timeout singkat
    $ch = curl_init($api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3); // Timeout 3 detik
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2); // Connection timeout 2 detik
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Eksekusi request
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);

    // Tutup cURL
    curl_close($ch);

    // Server dianggap berjalan jika response berhasil dan HTTP code 200
    return ($response !== false && empty($curl_error) && $http_code === 200);
}

/**
 * Fungsi untuk memulai server model Python menggunakan start_model_server.bat
 */
function start_model_server() {
    try {
        error_log("Memulai server model Python...");

        // Path ke file batch
        $batch_file = __DIR__ . DIRECTORY_SEPARATOR . 'start_model_server.bat';

        // Periksa apakah file batch ada
        if (!file_exists($batch_file)) {
            error_log("File start_model_server.bat tidak ditemukan di: " . $batch_file);
            return false;
        }

        // Jalankan batch file di background menggunakan start command
        $command = 'start /B "" "' . $batch_file . '"';

        error_log("Menjalankan command: " . $command);

        // Eksekusi command
        $output = [];
        $return_var = 0;
        exec($command, $output, $return_var);

        error_log("Command executed with return code: " . $return_var);

        // Tunggu beberapa detik untuk server startup
        sleep(3);

        // Periksa apakah server sudah berjalan dengan mencoba beberapa kali
        $max_attempts = 10;
        $attempt = 0;

        while ($attempt < $max_attempts) {
            if (is_model_server_running()) {
                error_log("Server model Python berhasil dimulai setelah " . ($attempt + 1) . " percobaan");
                return true;
            }

            $attempt++;
            sleep(2); // Tunggu 2 detik sebelum mencoba lagi
            error_log("Mencoba memeriksa server, percobaan ke-" . $attempt);
        }

        error_log("Server model Python gagal dimulai setelah " . $max_attempts . " percobaan");
        return false;

    } catch (Exception $e) {
        error_log("Error saat memulai server model: " . $e->getMessage());
        return false;
    }
}

/**
 * Fungsi untuk memastikan server model Python berjalan
 */
function ensure_model_server_running() {
    // Periksa apakah server sudah berjalan
    if (is_model_server_running()) {
        error_log("Server model Python sudah berjalan");
        return true;
    }

    error_log("Server model Python tidak berjalan, mencoba memulai...");

    // Coba mulai server
    if (start_model_server()) {
        error_log("Server model Python berhasil dimulai");
        return true;
    } else {
        error_log("Gagal memulai server model Python");
        return false;
    }
}

// Inisialisasi variabel
$error_message = '';
$success_message = '';
$show_prediction_result = false;
$prediction_result = null;

// Log untuk debugging
error_log("Halaman prediksi_baru.php diakses");
$form_data = [
    'nama_nasabah' => '',
    'jenis_kelamin' => 'Laki-laki',
    'status_perkawinan' => 'Menikah',
    'pekerjaan' => 'Swasta',
    'penghasilan' => '5.000.000',
    'jumlah_tanggungan' => '',
    'jumlah_pinjaman' => '10.000.000',
    'jangka_waktu' => '12',
    'jaminan' => 'BPKB Motor',
    'tahun_kendaraan' => date('Y'),
    'status_pajak' => 'Aktif',
    'kepemilikan_rumah' => '',
    'umur' => '',
    'tujuan_pinjaman' => 'Kebutuhan Pribadi'
];

// Cek apakah ada pesan error dari proses_prediksi_leasing.php
if (isset($_GET['error'])) {
    $error_message = $_GET['error'];
}

// Cek apakah ada parameter id_nasabah dari halaman lain
$selected_nasabah = isset($_GET['id_nasabah']) ? $_GET['id_nasabah'] : null;

// Tambahkan default value untuk tahun_kendaraan dan status_pajak
if (empty($form_data['tahun_kendaraan'])) {
    $form_data['tahun_kendaraan'] = '';
}
if (empty($form_data['status_pajak'])) {
    $form_data['status_pajak'] = '';
}

// Proses form jika ada submit
if (isset($_POST['submit'])) {
    // Tambahkan log untuk debugging
    error_log("Form submitted: " . print_r($_POST, true));

    // Ambil data dari form
    $form_data = [
        'nama_nasabah' => $_POST['nama_nasabah'] ?? '',
        'jenis_kelamin' => $_POST['jenis_kelamin'] ?? '',
        'status_perkawinan' => $_POST['status_perkawinan'] ?? '',
        'pekerjaan' => $_POST['pekerjaan'] ?? '',
        'penghasilan' => $_POST['penghasilan'] ?? '',
        'jumlah_tanggungan' => $_POST['jumlah_tanggungan'] ?? '',
        'jumlah_pinjaman' => $_POST['jumlah_pinjaman'] ?? '',
        'jangka_waktu' => $_POST['jangka_waktu'] ?? '',
        'jaminan' => $_POST['jaminan'] ?? 'BPKB Motor',
        'tahun_kendaraan' => $_POST['tahun_kendaraan'] ?? (date('Y')),
        'status_pajak' => $_POST['status_pajak'] ?? '',
        'kepemilikan_rumah' => $_POST['kepemilikan_rumah'] ?? '',
        'umur' => $_POST['umur'] ?? '',
        'tujuan_pinjaman' => $_POST['tujuan_pinjaman'] ?? ''
    ];

    // Validasi input
    if (empty($form_data['nama_nasabah'])) {
        $error_message = "Nama nasabah harus diisi!";
    } elseif (empty($form_data['penghasilan'])) {
        $error_message = "Penghasilan harus diisi!";
    } elseif (empty($form_data['jumlah_tanggungan']) || $form_data['jumlah_tanggungan'] < 0) {
        $error_message = "Jumlah tanggungan harus diisi dengan nilai minimal 0!";
    } elseif (empty($form_data['jumlah_pinjaman'])) {
        $error_message = "Jumlah pinjaman harus diisi!";
    } else {
        // Validasi data untuk tahun kendaraan dan status pajak
        // Pastikan tahun kendaraan diisi dan valid
        if (empty($form_data['tahun_kendaraan'])) {
            $error_message = "Tahun kendaraan harus diisi!";
        }
        elseif ($form_data['tahun_kendaraan'] < 1990 || $form_data['tahun_kendaraan'] > date('Y')) {
            $error_message = "Tahun kendaraan tidak valid! Harus antara 1990 dan " . date('Y');
        }
        // Pastikan status pajak diisi
        elseif (empty($form_data['status_pajak'])) {
            $error_message = "Status pajak kendaraan harus diisi!";
        }

        // Jika tidak ada error message, lanjutkan proses
        if (empty($error_message)) {
            // Simpan data nasabah ke database
            $sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, pekerjaan, penghasilan,
                    jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak,
                    kepemilikan_rumah, umur, tujuan_pinjaman)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            // Gunakan tujuan pinjaman dari form jika diisi, jika tidak set default
            $tujuan_pinjaman = !empty($form_data['tujuan_pinjaman']) ? $form_data['tujuan_pinjaman'] : 'Kebutuhan Pribadi';

            // Konversi nilai numerik ke tipe yang sesuai
            // Hapus titik sebagai pemisah ribuan pada penghasilan dan jumlah pinjaman
            $penghasilan = (float)str_replace('.', '', $form_data['penghasilan']);
            $jumlah_tanggungan = (int)$form_data['jumlah_tanggungan'];
            $jumlah_pinjaman = (float)str_replace('.', '', $form_data['jumlah_pinjaman']);
            $jangka_waktu = (int)$form_data['jangka_waktu'];
            $umur = (int)$form_data['umur'];
            $tahun_kendaraan = !empty($form_data['tahun_kendaraan']) ? $form_data['tahun_kendaraan'] : null;

            // Tambahkan error handling untuk debugging
            error_log("SQL Query: " . $sql);

            $stmt = mysqli_prepare($koneksi, $sql);
            if (!$stmt) {
                error_log("Error preparing statement: " . mysqli_error($koneksi));
                $error_message = "Gagal menyiapkan query: " . mysqli_error($koneksi);
            } else {
                mysqli_stmt_bind_param($stmt, "ssssdidissssis",
                    $form_data['nama_nasabah'],
                    $form_data['jenis_kelamin'],
                    $form_data['status_perkawinan'],
                    $form_data['pekerjaan'],
                    $penghasilan,
                    $jumlah_tanggungan,
                    $jumlah_pinjaman,
                    $jangka_waktu,
                    $form_data['jaminan'],
                    $tahun_kendaraan,
                    $form_data['status_pajak'],
                    $form_data['kepemilikan_rumah'],
                    $umur,
                    $tujuan_pinjaman
                );
            }

            if (mysqli_stmt_execute($stmt)) {
                $id_nasabah = mysqli_insert_id($koneksi);

                // Lakukan prediksi menggunakan API model Python
                error_log("Melakukan prediksi untuk nasabah ID: " . $id_nasabah);

                // Ambil data nasabah
                $query_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
                $stmt_nasabah = mysqli_prepare($koneksi, $query_nasabah);
                mysqli_stmt_bind_param($stmt_nasabah, "i", $id_nasabah);
                mysqli_stmt_execute($stmt_nasabah);
                $result_nasabah = mysqli_stmt_get_result($stmt_nasabah);
                $nasabah_data = mysqli_fetch_assoc($result_nasabah);

                if ($nasabah_data) {
                    try {
                        // Pastikan server model Python berjalan sebelum melakukan prediksi
                        error_log("Memastikan server model Python berjalan...");

                        if (!ensure_model_server_running()) {
                            throw new Exception("Gagal memulai server model Python. Pastikan Python dan dependencies sudah terinstall dengan benar.");
                        }

                        error_log("Server model Python sudah berjalan, melanjutkan prediksi...");

                        // URL API model Python
                        $api_url = 'http://localhost:5000/api/predict';

                        // Hitung nilai-nilai yang diperlukan
                        $penghasilan = (float)$nasabah_data['penghasilan'];
                        $jumlah_pinjaman = (float)$nasabah_data['jumlah_pinjaman'];
                        $jumlah_tanggungan = isset($nasabah_data['jumlah_tanggungan']) ? max(1, (int)$nasabah_data['jumlah_tanggungan']) : 1;
                        $jangka_waktu = isset($nasabah_data['jangka_waktu']) ? (int)$nasabah_data['jangka_waktu'] : 12;

                        // Hitung umur kendaraan
                        $umur_kendaraan = isset($nasabah_data['tahun_kendaraan']) ? (date('Y') - (int)$nasabah_data['tahun_kendaraan']) : 0;

                        // Hitung penghasilan per tanggungan
                        $penghasilan_per_tanggungan = $penghasilan / $jumlah_tanggungan;

                        // Hitung rasio pinjaman terhadap penghasilan
                        $rasio_pinjaman_penghasilan = $jumlah_pinjaman / ($penghasilan * 12);

                        // Siapkan data untuk dikirim ke API
                        $data_untuk_api = [
                            'id_nasabah' => $id_nasabah,
                            'umur' => (int)$nasabah_data['umur'],
                            'jenis_kelamin' => $nasabah_data['jenis_kelamin'],
                            'pekerjaan' => $nasabah_data['pekerjaan'],
                            'penghasilan' => (float)$penghasilan,
                            'jumlah_tanggungan' => (int)$jumlah_tanggungan,
                            'jumlah_pinjaman' => (float)$jumlah_pinjaman,
                            'jangka_waktu' => (int)$jangka_waktu,
                            'kepemilikan_rumah' => $nasabah_data['kepemilikan_rumah'],
                            'jaminan' => $nasabah_data['jaminan'],
                            'tahun_kendaraan' => (int)$nasabah_data['tahun_kendaraan'],
                            'status_pajak' => $nasabah_data['status_pajak'],
                            'tujuan_pinjaman' => !empty($nasabah_data['tujuan_pinjaman']) ? $nasabah_data['tujuan_pinjaman'] : 'Kebutuhan Pribadi',
                            'umur_kendaraan' => (int)$umur_kendaraan,
                            'rasio_pinjaman_penghasilan' => (float)$rasio_pinjaman_penghasilan,
                            'penghasilan_per_tanggungan' => (float)$penghasilan_per_tanggungan
                        ];

                        // Konversi ke JSON
                        $json_data = json_encode($data_untuk_api);

                        // Log untuk debugging
                        error_log("Mengirim request ke API model Python: " . $json_data);

                        // Inisialisasi cURL
                        $ch = curl_init($api_url);

                        // Set opsi cURL
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
                        curl_setopt($ch, CURLOPT_HTTPHEADER, [
                            'Content-Type: application/json',
                            'Content-Length: ' . strlen($json_data)
                        ]);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Timeout 10 detik

                        // Eksekusi request
                        $response = curl_exec($ch);
                        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        $curl_error = curl_error($ch);

                        // Tutup cURL
                        curl_close($ch);

                        // Log untuk debugging
                        error_log("Response dari API model Python (HTTP $http_code): " . $response);

                        if ($response === false) {
                            throw new Exception("Error cURL: " . $curl_error);
                        }

                        if ($http_code !== 200) {
                            throw new Exception("API model Python mengembalikan status error: " . $http_code);
                        }

                        // Parse hasil dari API
                        $hasil_api = json_decode($response, true);

                        if (json_last_error() !== JSON_ERROR_NONE) {
                            throw new Exception("Response API tidak valid JSON: " . $response);
                        }

                        if (isset($hasil_api['error'])) {
                            throw new Exception("API model Python mengembalikan error: " . $hasil_api['error']);
                        }

                        // Simpan hasil prediksi ke database
                        $hasil_prediksi = $hasil_api['hasil_prediksi'];
                        $probabilitas = $hasil_api['probabilitas'];
                        $keterangan = $hasil_api['keterangan'] ?? 'Prediksi menggunakan model backpropagation neural network';

                        $query_hasil = "INSERT INTO hasil_prediksi (id_nasabah, hasil_prediksi, probabilitas, keterangan, tanggal_prediksi)
                                        VALUES (?, ?, ?, ?, NOW())";
                        $stmt_hasil = mysqli_prepare($koneksi, $query_hasil);
                        mysqli_stmt_bind_param($stmt_hasil, "isds", $id_nasabah, $hasil_prediksi, $probabilitas, $keterangan);

                        if (mysqli_stmt_execute($stmt_hasil)) {
                            $id_prediksi = mysqli_insert_id($koneksi);
                            $success_message = "Data nasabah berhasil disimpan dan prediksi berhasil dilakukan.";

                            // Redirect ke halaman hasil prediksi
                            header("Location: hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi");
                            exit;
                        } else {
                            $error_message = "Gagal menyimpan hasil prediksi: " . mysqli_error($koneksi);
                        }
                    } catch (Exception $e) {
                        $error_message = "Error saat melakukan prediksi: " . $e->getMessage();
                        error_log("Error prediksi: " . $e->getMessage());
                    }
                } else {
                    $error_message = "Data nasabah tidak ditemukan.";
                }
            } else {
                $error_message = "Gagal menyimpan data nasabah: " . mysqli_error($koneksi);
            }
        }
    }
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Prediksi Kelayakan Kredit - Nasabah Website</h1>
            </div>
        </div>

        <!-- Tampilkan pesan error jika ada -->
        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Tampilkan pesan sukses jika ada -->
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4>Prediksi Kelayakan Nasabah dari Website</h4>
            </div>
            <div class="panel-body">
                <form method="post" action="proses_prediksi_website.php">
                    <div class="form-group">
                        <label>Pilih Nasabah dari Form Pengajuan</label>
                        <select name="id_nasabah" class="form-control" required>
                            <option value="">-- Pilih Nasabah --</option>
                            <?php
                            // Query untuk mengambil nasabah dari website leasing yang belum memiliki status kelayakan
                            $sql_leasing = "SELECT id_nasabah, nama_nasabah, jumlah_pinjaman, jaminan, tujuan_pinjaman
                                           FROM nasabah
                                           WHERE kelayakan IS NULL
                                           AND tujuan_pinjaman IS NOT NULL
                                           AND (sumber_data IS NULL OR sumber_data = 'leasing')
                                           ORDER BY id_nasabah DESC";
                            $result_leasing = mysqli_query($koneksi, $sql_leasing);

                            if (mysqli_num_rows($result_leasing) > 0) {
                                while ($row = mysqli_fetch_assoc($result_leasing)) {
                                    $selected = ($selected_nasabah == $row['id_nasabah']) ? 'selected' : '';
                                    echo '<option value="' . $row['id_nasabah'] . '" ' . $selected . '>';
                                    echo htmlspecialchars($row['nama_nasabah']) . ' - ';
                                    echo 'Rp ' . number_format($row['jumlah_pinjaman'], 0, ',', '.') . ' - ';
                                    echo htmlspecialchars($row['jaminan']);
                                    if (!empty($row['tujuan_pinjaman'])) {
                                        echo ' (' . htmlspecialchars($row['tujuan_pinjaman']) . ')';
                                    }
                                    echo '</option>';
                                }
                            } else {
                                echo '<option disabled>Tidak ada nasabah dari website leasing yang perlu diprediksi</option>';
                            }
                            ?>
                        </select>
                    </div>

                    <div class="form-group">
            <button type="submit" name="submit" class="btn btn-primary">
                <i class="fa fa-calculator"></i> Proses Prediksi
            </button>
            <a href="data_nasabah_analis.php" class="btn btn-default">
                <i class="fa fa-arrow-left"></i> Kembali
            </a>
            <button type="submit" name="tambah_nasabah" class="btn btn-success">
                <i class="fa fa-plus"></i> Tambah & Prediksi Nasabah Baru
            </button>
        </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
require_once 'foot.php';
?>
