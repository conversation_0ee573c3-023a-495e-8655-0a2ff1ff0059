#!/usr/bin/env python3
"""
Script untuk melatih model backpropagation neural network baru
menggunakan dataset_nasabah.csv
"""

import numpy as np
import pandas as pd
import pickle
import os
import re
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
import warnings
warnings.filterwarnings('ignore')

# Set random seed untuk reproduksibilitas
np.random.seed(42)

print("=" * 60)
print("TRAINING MODEL BACKPROPAGATION NEURAL NETWORK BARU")
print("=" * 60)
print()

def clean_currency(value):
    """Membersihkan format mata uang Indonesia"""
    if pd.isna(value):
        return 0

    # Konversi ke string jika belum
    value = str(value)

    # Hapus 'Rp', '.', dan spasi
    value = re.sub(r'[Rp\.\s]', '', value)

    # Ganti koma dengan titik untuk desimal
    value = value.replace(',', '.')

    try:
        return float(value)
    except:
        return 0

def clean_age(value):
    """Membersihkan format umur"""
    if pd.isna(value):
        return 0

    # Konversi ke string dan ambil angka saja
    value = str(value)
    numbers = re.findall(r'\d+', value)

    if numbers:
        return int(numbers[0])
    return 0

def preprocess_dataset(df):
    """Preprocessing dataset"""
    print("Melakukan preprocessing dataset...")

    # Buat copy dataset
    df_processed = df.copy()

    # 1. Bersihkan kolom umur
    df_processed['umur'] = df_processed['umur'].apply(clean_age)

    # 2. Bersihkan kolom penghasilan dan jumlah_pinjaman
    df_processed['penghasilan'] = df_processed['penghasilan'].apply(clean_currency)
    df_processed['jumlah_pinjaman'] = df_processed['jumlah_pinjaman'].apply(clean_currency)

    # 3. Bersihkan kolom waktu_pengembalian (jangka_waktu)
    df_processed['jangka_waktu'] = df_processed['waktu_pengembalian']

    # 4. Standarisasi nama kolom dan nilai
    # Jenis kelamin
    df_processed['jenis_kelamin'] = df_processed['jenis_kelamin'].str.strip()

    # Pekerjaan
    df_processed['pekerjaan'] = df_processed['pekerjaan'].str.strip()
    df_processed['pekerjaan'] = df_processed['pekerjaan'].replace({
        'Swasta': 'Karyawan Swasta',
        'Lainnya': 'Lainnya'
    })

    # Kepemilikan rumah
    df_processed['kepemilikan_rumah'] = df_processed['kepemilikan_rumah'].str.strip()
    df_processed['kepemilikan_rumah'] = df_processed['kepemilikan_rumah'].replace({
        'M': 'Milik Sendiri',
        'Orang Tua': 'Keluarga'
    })

    # Jaminan
    df_processed['jaminan'] = df_processed['jaminan'].str.strip()

    # Status pajak
    df_processed['status_pajak'] = df_processed['status_pajak'].str.strip()

    # Tujuan pinjaman
    df_processed['tujuan_pinjaman'] = df_processed['tujuan_pinjaman'].str.strip()

    # Kelayakan (target)
    df_processed['kelayakan'] = df_processed['kelayakan'].str.strip()

    # 5. Tambahkan fitur derived
    current_year = datetime.now().year
    df_processed['umur_kendaraan'] = current_year - df_processed['tahun_kendaraan']

    # Rasio pinjaman terhadap penghasilan
    df_processed['rasio_pinjaman_penghasilan'] = df_processed['jumlah_pinjaman'] / (df_processed['penghasilan'] * 12)

    # Penghasilan per tanggungan
    df_processed['penghasilan_per_tanggungan'] = df_processed['penghasilan'] / df_processed['jumlah_tanggungan']

    # 6. Handle missing values
    df_processed = df_processed.fillna({
        'jumlah_tanggungan': 1,
        'tahun_kendaraan': 2020,
        'umur_kendaraan': 5,
        'jangka_waktu': 24
    })

    # 7. Filter data yang valid
    df_processed = df_processed[
        (df_processed['penghasilan'] > 0) &
        (df_processed['jumlah_pinjaman'] > 0) &
        (df_processed['umur'] > 0) &
        (df_processed['kelayakan'].isin(['Layak', 'Tidak Layak']))
    ]

    print(f"Dataset setelah preprocessing: {df_processed.shape[0]} baris, {df_processed.shape[1]} kolom")

    return df_processed

# Memuat dataset
print("Memuat dataset...")
try:
    # Coba memuat dengan berbagai delimiter
    try:
        df = pd.read_csv('model/dataset_nasabah.csv', sep=';', encoding='utf-8')
    except:
        try:
            df = pd.read_csv('dataset_nasabah.csv', sep=';', encoding='utf-8')
        except:
            df = pd.read_csv('model/dataset_nasabah.csv', sep=',', encoding='utf-8')

    print(f"Dataset berhasil dimuat: {df.shape[0]} baris, {df.shape[1]} kolom")
    print(f"Kolom: {list(df.columns)}")

except Exception as e:
    print(f"Error memuat dataset: {e}")
    exit(1)

# Preprocessing dataset
df_processed = preprocess_dataset(df)

# Tampilkan distribusi target
print("\nDistribusi target (kelayakan):")
print(df_processed['kelayakan'].value_counts())
print()

# Pilih fitur untuk model
feature_columns = [
    'umur', 'jenis_kelamin', 'pekerjaan', 'penghasilan', 'jumlah_tanggungan',
    'jumlah_pinjaman', 'jangka_waktu', 'kepemilikan_rumah', 'jaminan',
    'tahun_kendaraan', 'status_pajak', 'tujuan_pinjaman', 'umur_kendaraan',
    'rasio_pinjaman_penghasilan', 'penghasilan_per_tanggungan'
]

# Pisahkan fitur dan target
X = df_processed[feature_columns].copy()
y = df_processed['kelayakan'].copy()

# Konversi target ke format yang konsisten
y = y.astype(str)
y = y.map({'Layak': 1, 'Tidak Layak': 0})

# Pastikan tidak ada nilai NaN di target
y = y.fillna(0)

print(f"Fitur yang digunakan: {len(feature_columns)} fitur")
print(f"Jumlah sampel: {len(X)}")
print(f"Distribusi target: {y.value_counts()}")

# Identifikasi fitur numerik dan kategorikal
numeric_features = ['umur', 'penghasilan', 'jumlah_tanggungan', 'jumlah_pinjaman',
                   'jangka_waktu', 'tahun_kendaraan', 'umur_kendaraan',
                   'rasio_pinjaman_penghasilan', 'penghasilan_per_tanggungan']

categorical_features = ['jenis_kelamin', 'pekerjaan', 'kepemilikan_rumah',
                       'jaminan', 'status_pajak', 'tujuan_pinjaman']

print(f"Fitur numerik: {len(numeric_features)}")
print(f"Fitur kategorikal: {len(categorical_features)}")

# Buat preprocessor
preprocessor = ColumnTransformer(
    transformers=[
        ('num', StandardScaler(), numeric_features),
        ('cat', OneHotEncoder(handle_unknown='ignore', sparse_output=False), categorical_features)
    ]
)

# Buat model neural network
model = MLPClassifier(
    hidden_layer_sizes=(50, 25),  
    activation='relu',
    solver='adam',
    alpha=0.001,  
    batch_size='auto',
    learning_rate='constant',
    learning_rate_init=0.001,
    max_iter=1000, 
    shuffle=True,
    random_state=42,
    early_stopping=False,  # Disable early stopping untuk menghindari error
    verbose=True
)

# Buat pipeline
pipeline = Pipeline([
    ('preprocessor', preprocessor),
    ('classifier', model)
])

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

print(f"\nData training: {len(X_train)} sampel")
print(f"Data testing: {len(X_test)} sampel")

# Training model
print("\nMemulai training model...")
print("Ini mungkin memakan waktu beberapa menit...")

pipeline.fit(X_train, y_train)

print("Training selesai!")

# Evaluasi model
print("\nEvaluasi model:")
y_pred = pipeline.predict(X_test)

accuracy = accuracy_score(y_test, y_pred)
precision = precision_score(y_test, y_pred, pos_label=1)
recall = recall_score(y_test, y_pred, pos_label=1)
f1 = f1_score(y_test, y_pred, pos_label=1)

print(f"Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
print(f"Precision: {precision:.4f} ({precision*100:.2f}%)")
print(f"Recall: {recall:.4f} ({recall*100:.2f}%)")
print(f"F1-Score: {f1:.4f} ({f1*100:.2f}%)")

print("\nConfusion Matrix:")
cm = confusion_matrix(y_test, y_pred)
print(cm)

print("\nClassification Report:")
print(classification_report(y_test, y_pred))

# Simpan model
model_dir = 'model'
if not os.path.exists(model_dir):
    os.makedirs(model_dir)

model_path = os.path.join(model_dir, 'backpropagation_model.pkl')
scaler_path = os.path.join(model_dir, 'scaler.pkl')

print(f"\nMenyimpan model ke {model_path}...")

# Simpan pipeline lengkap
with open(model_path, 'wb') as f:
    pickle.dump(pipeline, f)

# Simpan juga preprocessor terpisah untuk kompatibilitas
with open(scaler_path, 'wb') as f:
    pickle.dump(preprocessor, f)

print("Model berhasil disimpan!")

# Test prediksi dengan sample data
print("\nTest prediksi dengan sample data:")
sample_data = X_test.iloc[:3].copy()
sample_predictions = pipeline.predict(sample_data)
sample_probabilities = pipeline.predict_proba(sample_data)

for i, (idx, row) in enumerate(sample_data.iterrows()):
    actual = y_test.iloc[i]
    predicted = sample_predictions[i]
    prob = sample_probabilities[i]

    print(f"\nSample {i+1}:")
    print(f"  Umur: {row['umur']}, Penghasilan: Rp{row['penghasilan']:,.0f}")
    print(f"  Pinjaman: Rp{row['jumlah_pinjaman']:,.0f}, Jaminan: {row['jaminan']}")
    print(f"  Actual: {actual}, Predicted: {predicted}")
    print(f"  Probability: Layak={prob[1]:.3f}, Tidak Layak={prob[0]:.3f}")

print("\n" + "=" * 60)
print("MODEL TRAINING SELESAI!")
print("=" * 60)
print(f"Model disimpan di: {model_path}")
print(f"Scaler disimpan di: {scaler_path}")
print(f"Akurasi model: {accuracy*100:.2f}%")
print("Model siap digunakan untuk prediksi!")
