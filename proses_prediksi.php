<?php
include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);
require_once 'nav_analis.php';

// Inisialisasi variabel
$error_message = '';
$success_message = '';
$form_data = [
    'nama_nasabah' => '',
    'jenis_kelamin' => '',
    'status_perkawinan' => '',
    'pekerjaan' => '',
    'penghasilan' => '',
    'jumlah_tanggungan' => '',
    'jumlah_pinjaman' => '',
    'jangka_waktu' => '',
    'jaminan' => '',
    'tahun_kendaraan' => '',
    'status_pajak' => '',
    'kepemilikan_rumah' => '',
    'umur' => ''
];

// Proses form jika ada submit
if (isset($_POST['submit'])) {
    // Ambil data dari form
    $form_data = [
        'nama_nasabah' => $_POST['nama_nasabah'],
        'jenis_kelamin' => $_POST['jenis_kelamin'],
        'status_perkawinan' => $_POST['status_perkawinan'],
        'pekerjaan' => $_POST['pekerjaan'],
        'penghasilan' => $_POST['penghasilan'],
        'jumlah_tanggungan' => $_POST['jumlah_tanggungan'],
        'jumlah_pinjaman' => $_POST['jumlah_pinjaman'],
        'jangka_waktu' => $_POST['jangka_waktu'],
        'jaminan' => $_POST['jaminan'],
        'tahun_kendaraan' => $_POST['tahun_kendaraan'],
        'status_pajak' => $_POST['status_pajak'],
        'kepemilikan_rumah' => $_POST['kepemilikan_rumah'],
        'umur' => $_POST['umur']
    ];

    // Validasi input
    if (empty($form_data['nama_nasabah'])) {
        $error_message = "Nama nasabah harus diisi!";
    } elseif (empty($form_data['penghasilan'])) {
        $error_message = "Penghasilan harus diisi!";
    } elseif (empty($form_data['jumlah_tanggungan'])) {
        $error_message = "Jumlah tanggungan harus diisi!";
    } elseif (empty($form_data['jumlah_pinjaman'])) {
        $error_message = "Jumlah pinjaman harus diisi!";
    } elseif (empty($form_data['tahun_kendaraan'])) {
        $error_message = "Tahun kendaraan harus diisi!";
    } elseif (empty($form_data['status_pajak'])) {
        $error_message = "Status pajak kendaraan harus diisi!";
    } else {
        // Simpan data nasabah ke database
        $sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, pekerjaan, penghasilan,
                jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak,
                kepemilikan_rumah, umur)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = mysqli_prepare($koneksi, $sql);
        mysqli_stmt_bind_param($stmt, "ssssdidissssi",
            $form_data['nama_nasabah'],
            $form_data['jenis_kelamin'],
            $form_data['status_perkawinan'],
            $form_data['pekerjaan'],
            $form_data['penghasilan'],
            $form_data['jumlah_tanggungan'],
            $form_data['jumlah_pinjaman'],
            $form_data['jangka_waktu'],
            $form_data['jaminan'],
            $form_data['tahun_kendaraan'],
            $form_data['status_pajak'],
            $form_data['kepemilikan_rumah'],
            $form_data['umur']
        );

        if (mysqli_stmt_execute($stmt)) {
            $id_nasabah = mysqli_insert_id($koneksi);

            // Lakukan prediksi menggunakan API
            $api_url = 'http://localhost/sistem_prediksi_backpropagation/api_predict.php';

            // Tambahkan log untuk debugging
            error_log("Prediksi untuk nasabah ID: " . $id_nasabah);

            // Gunakan cURL untuk memanggil API
            $ch = curl_init($api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(['id_nasabah' => $id_nasabah]));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            // Tambahkan log untuk debugging
            error_log("API Response: " . $response);
            error_log("HTTP Code: " . $http_code);

            curl_close($ch);

            // Kita sudah memiliki id_prediksi dari hasil prediksi langsung
            if ($id_prediksi) {
                // Tambahkan log untuk debugging
                error_log("Redirect ke hasil_prediksi.php dengan id_nasabah=$id_nasabah dan id_prediksi=$id_prediksi");

                // Gunakan JavaScript untuk redirect, lebih aman daripada header() yang bisa menyebabkan error
                echo "<script>
                    console.log('Redirect ke hasil_prediksi.php dengan id_nasabah=$id_nasabah dan id_prediksi=$id_prediksi');
                    window.location.href = 'hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi';
                </script>";
                exit;
            } else {
                $error_message = "Gagal mendapatkan hasil prediksi. Silakan coba lagi.";
                error_log("Gagal mendapatkan ID prediksi");
            }
        } else {
            $error_message = "Gagal menyimpan data nasabah: " . mysqli_error($koneksi);
        }
    }
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Tambah Nasabah & Prediksi Kelayakan</h1>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>Form Tambah Nasabah dan Prediksi Kelayakan</h4>
                    </div>
                    <div class="panel-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger">
                                <?php echo $error_message; ?>
                            </div>
                        <?php endif; ?>

                        <form method="post" action="">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Nama Nasabah *</label>
                                        <input type="text" name="nama_nasabah" class="form-control"
                                               value="<?php echo htmlspecialchars($form_data['nama_nasabah']); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label>Jenis Kelamin *</label>
                                        <select name="jenis_kelamin" class="form-control" required>
                                            <option value="Laki-laki" <?php echo $form_data['jenis_kelamin'] == 'Laki-laki' ? 'selected' : ''; ?>>Laki-laki</option>
                                            <option value="Perempuan" <?php echo $form_data['jenis_kelamin'] == 'Perempuan' ? 'selected' : ''; ?>>Perempuan</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Status Perkawinan *</label>
                                        <select name="status_perkawinan" class="form-control" required>
                                            <option value="Menikah" <?php echo $form_data['status_perkawinan'] == 'Menikah' ? 'selected' : ''; ?>>Menikah</option>
                                            <option value="Belum Menikah" <?php echo $form_data['status_perkawinan'] == 'Belum Menikah' ? 'selected' : ''; ?>>Belum Menikah</option>
                                            <option value="Cerai" <?php echo $form_data['status_perkawinan'] == 'Cerai' ? 'selected' : ''; ?>>Cerai</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Pekerjaan *</label>
                                        <select name="pekerjaan" class="form-control" required>
                                            <option value="PNS" <?php echo $form_data['pekerjaan'] == 'PNS' ? 'selected' : ''; ?>>PNS</option>
                                            <option value="Swasta" <?php echo $form_data['pekerjaan'] == 'Swasta' ? 'selected' : ''; ?>>Swasta</option>
                                            <option value="Wiraswasta" <?php echo $form_data['pekerjaan'] == 'Wiraswasta' ? 'selected' : ''; ?>>Wiraswasta</option>
                                            <option value="Lainnya" <?php echo $form_data['pekerjaan'] == 'Lainnya' ? 'selected' : ''; ?>>Lainnya</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label>Umur *</label>
                                        <input type="number" name="umur" class="form-control" min="20" max="70"
                                               value="<?php echo htmlspecialchars($form_data['umur']); ?>" required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Penghasilan per Bulan (Rp) *</label>
                                        <input type="number" name="penghasilan" class="form-control"
                                               value="<?php echo htmlspecialchars($form_data['penghasilan']); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label>Jumlah Tanggungan *</label>
                                        <input type="number" name="jumlah_tanggungan" class="form-control" min="0" max="10"
                                               value="<?php echo htmlspecialchars($form_data['jumlah_tanggungan']); ?>" required>
                                        <small class="text-muted">Jumlah anggota keluarga yang menjadi tanggungan</small>
                                    </div>

                                    <div class="form-group">
                                        <label>Jumlah Pinjaman (Rp) *</label>
                                        <input type="number" name="jumlah_pinjaman" class="form-control"
                                               value="<?php echo htmlspecialchars($form_data['jumlah_pinjaman']); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label>Jangka Waktu (bulan) *</label>
                                        <input type="number" name="jangka_waktu" class="form-control" min="6" max="60"
                                               value="<?php echo htmlspecialchars($form_data['jangka_waktu']); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label>Jaminan *</label>
                                        <select name="jaminan" class="form-control" id="jaminan-select" required>
                                            <option value="BPKB Motor" <?php echo $form_data['jaminan'] == 'BPKB Motor' ? 'selected' : ''; ?>>BPKB Motor</option>
                                            <option value="BPKB Mobil" <?php echo $form_data['jaminan'] == 'BPKB Mobil' ? 'selected' : ''; ?>>BPKB Mobil</option>
                                        </select>
                                    </div>

                                    <div class="form-group kendaraan-detail" id="tahun-kendaraan-group">
                                        <label>Tahun Kendaraan *</label>
                                        <input type="number" name="tahun_kendaraan" class="form-control" min="1990" max="<?php echo date('Y'); ?>"
                                               value="<?php echo htmlspecialchars($form_data['tahun_kendaraan']); ?>" required>
                                        <small class="text-muted">Tahun pembuatan kendaraan (wajib diisi)</small>
                                    </div>

                                    <div class="form-group kendaraan-detail" id="status-pajak-group">
                                        <label>Status Pajak Kendaraan *</label>
                                        <select name="status_pajak" class="form-control" required>
                                            <option value="">-- Pilih Status Pajak --</option>
                                            <option value="Aktif" <?php echo $form_data['status_pajak'] == 'Aktif' ? 'selected' : ''; ?>>Aktif</option>
                                            <option value="Tidak Aktif" <?php echo $form_data['status_pajak'] == 'Tidak Aktif' ? 'selected' : ''; ?>>Tidak Aktif</option>
                                        </select>
                                        <small class="text-muted">Status pajak kendaraan (wajib diisi)</small>
                                    </div>

                                    <div class="form-group">
                                        <label>Kepemilikan Rumah *</label>
                                        <select name="kepemilikan_rumah" class="form-control" required>
                                            <option value="Milik Sendiri" <?php echo $form_data['kepemilikan_rumah'] == 'Milik Sendiri' ? 'selected' : ''; ?>>Milik Sendiri</option>
                                            <option value="Kontrak" <?php echo $form_data['kepemilikan_rumah'] == 'Kontrak' ? 'selected' : ''; ?>>Kontrak</option>
                                            <option value="Orang Tua" <?php echo $form_data['kepemilikan_rumah'] == 'Orang Tua' ? 'selected' : ''; ?>>Orang Tua</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" name="submit" class="btn btn-primary">
                                    <i class="fa fa-save"></i> Simpan & Prediksi Kelayakan
                                </button>
                                <a href="data_nasabah_analis.php" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> Kembali
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-info">
                    <div class="panel-heading">
                        <h4>Informasi Kriteria Penilaian</h4>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Faktor</th>
                                        <th>Kriteria</th>
                                        <th>Bobot</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Rasio Pinjaman/Penghasilan per Kapita</td>
                                        <td>≤50% (30 poin), 51-100% (25 poin), 101-150% (20 poin), 151-200% (15 poin), 201-300% (10 poin), 301-400% (5 poin), >400% (0 poin)</td>
                                        <td>30%</td>
                                    </tr>
                                    <tr>
                                        <td>Jumlah Tanggungan</td>
                                        <td>
                                            Penghasilan dibagi jumlah tanggungan (termasuk pemohon) untuk menghitung penghasilan per kapita<br>
                                            &nbsp;&nbsp;- Penghasilan per kapita ≥ 5 juta: +5 poin bonus<br>
                                            &nbsp;&nbsp;- Penghasilan per kapita ≥ 3 juta: +3 poin bonus<br>
                                            &nbsp;&nbsp;- Tanggungan > 3 dengan penghasilan per kapita < 1,5 juta: -5 poin penalti<br>
                                            &nbsp;&nbsp;- Tanggungan > 5 dengan penghasilan per kapita < 2 juta: -10 poin penalti
                                        </td>
                                        <td>-</td>
                                    </tr>
                                    <tr>
                                        <td>Stabilitas Pekerjaan</td>
                                        <td>PNS/Dokter/Dosen (20 poin), Karyawan/Swasta/Guru (15 poin), Wiraswasta/Pengusaha (10 poin), Freelancer/Petani/Nelayan (5 poin), Lainnya (0 poin)</td>
                                        <td>20%</td>
                                    </tr>
                                    <tr>
                                        <td>Jaminan</td>
                                        <td>
                                            <strong>BPKB Mobil</strong>: 5-25 poin (berdasarkan tahun dan status pajak)<br>
                                            &nbsp;&nbsp;- Mobil ≤ 3 tahun, pajak aktif: 25 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 3 tahun, pajak tidak aktif: 22 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 5 tahun, pajak aktif: 20 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 5 tahun, pajak tidak aktif: 18 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 8 tahun, pajak aktif: 16 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 8 tahun, pajak tidak aktif: 14 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 10 tahun, pajak aktif: 12 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 10 tahun, pajak tidak aktif: 10 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 15 tahun, pajak aktif: 8 poin<br>
                                            &nbsp;&nbsp;- Mobil ≤ 15 tahun, pajak tidak aktif: 6 poin<br>
                                            &nbsp;&nbsp;- Mobil > 15 tahun: 5 poin<br>
                                            <strong>BPKB Motor</strong>: 2-15 poin (berdasarkan tahun dan status pajak)<br>
                                            &nbsp;&nbsp;- Motor ≤ 1 tahun, pajak aktif: 15 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 1 tahun, pajak tidak aktif: 13 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 3 tahun, pajak aktif: 12 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 3 tahun, pajak tidak aktif: 10 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 5 tahun, pajak aktif: 8 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 5 tahun, pajak tidak aktif: 7 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 7 tahun, pajak aktif: 6 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 7 tahun, pajak tidak aktif: 5 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 10 tahun, pajak aktif: 4 poin<br>
                                            &nbsp;&nbsp;- Motor ≤ 10 tahun, pajak tidak aktif: 3 poin<br>
                                            &nbsp;&nbsp;- Motor > 10 tahun: 2 poin
                                        </td>
                                        <td>25%</td>
                                    </tr>
                                    <tr>
                                        <td>Kepemilikan Rumah</td>
                                        <td>Milik Sendiri (15 poin), Keluarga/Orang Tua (10 poin), Kontrak/Sewa (5 poin), Lainnya (0 poin)</td>
                                        <td>15%</td>
                                    </tr>
                                    <tr>
                                        <td>Status Perkawinan</td>
                                        <td>Menikah (10 poin), Belum Menikah (7 poin), Cerai (5 poin)</td>
                                        <td>0%</td>
                                    </tr>
                                    <tr>
                                        <td>Umur</td>
                                        <td>30-50 tahun (10 poin), 25-29 atau 51-55 tahun (7 poin), 21-24 atau 56-60 tahun (5 poin), <21 atau >60 tahun (0 poin)</td>
                                        <td>10%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once 'foot.php';
?>

<script>
// Fungsi untuk menampilkan field detail kendaraan
function toggleKendaraanFields() {
    var kendaraanFields = document.querySelectorAll('.kendaraan-detail');

    // Selalu tampilkan field detail kendaraan karena jaminan hanya BPKB Motor/Mobil
    kendaraanFields.forEach(function(field) {
        field.style.display = 'block';
    });
}

// Jalankan fungsi saat halaman dimuat
document.addEventListener('DOMContentLoaded', function() {
    toggleKendaraanFields();

    // Tambahkan event listener untuk perubahan pada dropdown jaminan
    document.getElementById('jaminan-select').addEventListener('change', toggleKendaraanFields);
});
</script>