<?php
include 'koneksi.php';
include 'cek_session.php';

// Cek akses berdasarkan tabel yang akan ditambahkan
if (isset($_GET['table'])) {
    $table = $_GET['table'];
    if ($table == 'user' || $table == 'users' || $table == 'pengguna') {
        // Hanya admin yang boleh menambah data pengguna
        cek_akses(['admin']);
    } elseif ($table == 'nasabah') {
        // Admin dan analis boleh menambah data nasabah
        cek_akses(['admin', 'analis']);
    }
}

// Fungsi untuk sanitasi input
function sanitize_input($data) {
    global $koneksi;
    return mysqli_real_escape_string($koneksi, htmlspecialchars(trim($data)));
}

// Inisialisasi variabel
$table = '';
$redirect_page = '';
$success_message = '';
$error_message = '';
$title = '';

// Cek parameter tabel
if (isset($_GET['table'])) {
    $table = sanitize_input($_GET['table']);

    // Tentukan judul dan halaman redirect berdasarkan tabel
    switch ($table) {
        case 'nasabah':
            $redirect_page = ($_SESSION['level'] == 'admin') ? 'data_nasabah.php' : 'data_nasabah_analis.php';
            $success_message = 'Data nasabah berhasil ditambahkan';
            $title = 'Tambah Data Nasabah';
            break;
        case 'users':
        case 'user':
        case 'pengguna':
            $redirect_page = 'data_user.php';
            $success_message = 'Data pengguna berhasil ditambahkan';
            $title = 'Tambah Data Pengguna';
            $table = 'users'; // Standardisasi nama tabel
            break;
        default:
            // Tabel tidak valid
            $error_message = 'Tabel tidak valid';
            break;
    }
}

// Proses form submit
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit'])) {
    $table = sanitize_input($_POST['table']);

    // Tentukan halaman redirect berdasarkan tabel
    switch ($table) {
        case 'nasabah':
            $redirect_page = ($_SESSION['level'] == 'admin') ? 'data_nasabah.php' : 'data_nasabah_analis.php';
            $success_message = 'Data nasabah berhasil ditambahkan';
            break;
        case 'pengguna':
        case 'user':
        case 'users':
            $redirect_page = 'data_user.php';
            $success_message = 'Data pengguna berhasil ditambahkan';
            $table = 'users'; // Standardisasi nama tabel
            break;
        default:
            // Tabel tidak valid
            $error_message = 'Tabel tidak valid';
            break;
    }

    // Proses tambah data berdasarkan tabel
    if ($table == 'nasabah') {
        // Sanitasi input
        $nama_nasabah = sanitize_input($_POST['nama_nasabah']);
        $umur = sanitize_input($_POST['umur']);
        $jenis_kelamin = sanitize_input($_POST['jenis_kelamin']);
        $status_perkawinan = sanitize_input($_POST['status_perkawinan']);
        $pekerjaan = sanitize_input($_POST['pekerjaan']);

        // Konversi penghasilan dari format rupiah ke angka
        $penghasilan_input = sanitize_input($_POST['penghasilan']);
        $penghasilan = str_replace('.', '', $penghasilan_input); // Hapus titik sebagai pemisah ribuan

        $jumlah_tanggungan = isset($_POST['jumlah_tanggungan']) ? sanitize_input($_POST['jumlah_tanggungan']) : 0;

        // Konversi jumlah pinjaman dari format rupiah ke angka
        $jumlah_pinjaman_input = sanitize_input($_POST['jumlah_pinjaman']);
        $jumlah_pinjaman = str_replace('.', '', $jumlah_pinjaman_input); // Hapus titik sebagai pemisah ribuan

        $jangka_waktu = sanitize_input($_POST['jangka_waktu']);
        $kepemilikan_rumah = sanitize_input($_POST['kepemilikan_rumah']);
        $jaminan = sanitize_input($_POST['jaminan']);
        $tahun_kendaraan = isset($_POST['tahun_kendaraan']) ? sanitize_input($_POST['tahun_kendaraan']) : '';
        $status_pajak = isset($_POST['status_pajak']) ? sanitize_input($_POST['status_pajak']) : '';
        $tujuan_pinjaman = sanitize_input($_POST['tujuan_pinjaman']);

        // Insert data nasabah
        $insert_sql = "INSERT INTO nasabah (nama_nasabah, umur, jenis_kelamin, status_perkawinan, pekerjaan,
                      penghasilan, jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, kepemilikan_rumah, jaminan,
                      tahun_kendaraan, status_pajak, tujuan_pinjaman)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $insert_stmt = $koneksi->prepare($insert_sql);
        $insert_stmt->bind_param("sisssdidississ",
                               $nama_nasabah,
                               $umur,
                               $jenis_kelamin,
                               $status_perkawinan,
                               $pekerjaan,
                               $penghasilan,
                               $jumlah_tanggungan,
                               $jumlah_pinjaman,
                               $jangka_waktu,
                               $kepemilikan_rumah,
                               $jaminan,
                               $tahun_kendaraan,
                               $status_pajak,
                               $tujuan_pinjaman);

        try {
            if ($insert_stmt->execute()) {
                echo "<script>alert('$success_message'); window.location='$redirect_page';</script>";
                exit;
            } else {
                $error_message = "Gagal menambahkan data: " . $insert_stmt->error;
            }
        } catch (Exception $e) {
            $error_message = "Error: " . $e->getMessage();
        }
    } elseif ($table == 'users' || $table == 'user' || $table == 'pengguna') {
        // Sanitasi input
        $username = sanitize_input($_POST['username']);
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $nama_lengkap = sanitize_input($_POST['nama_lengkap']);
        $level = sanitize_input($_POST['level']);

        // Cek apakah username sudah ada
        $check_sql = "SELECT COUNT(*) as count FROM users WHERE username = ?";
        $check_stmt = $koneksi->prepare($check_sql);
        $check_stmt->bind_param("s", $username);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        $count = $result->fetch_assoc()['count'];

        if ($count > 0) {
            $error_message = "Username sudah digunakan, silakan pilih username lain";
        } else {
            // Insert data pengguna
            $insert_sql = "INSERT INTO users (username, password, nama_lengkap, level) VALUES (?, ?, ?, ?)";
            $insert_stmt = $koneksi->prepare($insert_sql);
            $insert_stmt->bind_param("ssss", $username, $password, $nama_lengkap, $level);

            try {
                if ($insert_stmt->execute()) {
                    echo "<script>alert('$success_message'); window.location='$redirect_page';</script>";
                    exit;
                } else {
                    $error_message = "Gagal menambahkan data: " . $insert_stmt->error;
                }
            } catch (Exception $e) {
                $error_message = "Error: " . $e->getMessage();
            }
        }
    }
}

// Jika ada error, tampilkan pesan
if (!empty($error_message)) {
    echo "<script>alert('$error_message');</script>";
}

// Tentukan layout berdasarkan level pengguna
$nav_file = ($_SESSION['level'] == 'admin') ? 'nav.php' : 'nav_analis.php';
require_once $nav_file;
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header"><?php echo $title; ?></h1>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-plus"></i> Form Tambah Data</h3>
                    </div>
                    <div class="panel-body">
                        <?php if ($table == 'nasabah'): ?>
                        <form method="POST" action="tambah_data_universal.php" class="form-horizontal">
                            <input type="hidden" name="table" value="<?php echo $table; ?>">

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Nama Nasabah</label>
                                <div class="col-sm-9">
                                    <input type="text" name="nama_nasabah" class="form-control" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Umur</label>
                                <div class="col-sm-9">
                                    <input type="number" name="umur" class="form-control" required min="17" max="80">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Jenis Kelamin</label>
                                <div class="col-sm-9">
                                    <select name="jenis_kelamin" class="form-control" required>
                                        <option value="">-- Pilih Jenis Kelamin --</option>
                                        <option value="Laki-laki">Laki-laki</option>
                                        <option value="Perempuan">Perempuan</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Status Perkawinan</label>
                                <div class="col-sm-9">
                                    <select name="status_perkawinan" class="form-control" required>
                                        <option value="">-- Pilih Status Perkawinan --</option>
                                        <option value="Menikah">Menikah</option>
                                        <option value="Belum Menikah">Belum Menikah</option>
                                        <option value="Cerai">Cerai</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Pekerjaan</label>
                                <div class="col-sm-9">
                                    <select name="pekerjaan" class="form-control" required>
                                        <option value="">-- Pilih Pekerjaan --</option>
                                        <option value="PNS">PNS</option>
                                        <option value="Swasta">Swasta</option>
                                        <option value="Wiraswasta">Wiraswasta</option>
                                        <option value="Profesional">Profesional</option>
                                        <option value="Lainnya">Lainnya</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Penghasilan</label>
                                <div class="col-sm-9">
                                    <input type="text" name="penghasilan" class="form-control" required min="0" placeholder="Contoh: 5.000.000">
                                    <p class="help-block">Format: 1.000.000 (tanpa Rp)</p>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Jumlah Tanggungan</label>
                                <div class="col-sm-9">
                                    <input type="number" name="jumlah_tanggungan" class="form-control" min="0" max="10" value="0">
                                    <small class="text-muted">Jumlah anggota keluarga yang menjadi tanggungan</small>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Jumlah Pinjaman</label>
                                <div class="col-sm-9">
                                    <input type="text" name="jumlah_pinjaman" class="form-control" required min="0" placeholder="Contoh: 10.000.000">
                                    <p class="help-block">Format: 1.000.000 (tanpa Rp)</p>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Jangka Waktu (bulan)</label>
                                <div class="col-sm-9">
                                    <select name="jangka_waktu" class="form-control" required>
                                        <option value="">-- Pilih Jangka Waktu --</option>
                                        <option value="12">12 Bulan (1 Tahun)</option>
                                        <option value="24">24 Bulan (2 Tahun)</option>
                                        <option value="36">36 Bulan (3 Tahun)</option>
                                        <option value="48">48 Bulan (4 Tahun)</option>
                                        <option value="60">60 Bulan (5 Tahun)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Kepemilikan Rumah</label>
                                <div class="col-sm-9">
                                    <select name="kepemilikan_rumah" class="form-control" required>
                                        <option value="">-- Pilih Kepemilikan Rumah --</option>
                                        <option value="Milik Sendiri">Milik Sendiri</option>
                                        <option value="Sewa">Sewa</option>
                                        <option value="Milik Keluarga">Milik Keluarga</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Jaminan</label>
                                <div class="col-sm-9">
                                    <select name="jaminan" class="form-control" id="jaminan-select" required>
                                        <option value="">-- Pilih Jaminan --</option>
                                        <option value="BPKB Motor">BPKB Motor</option>
                                        <option value="BPKB Mobil">BPKB Mobil</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group kendaraan-detail" id="tahun-kendaraan-group">
                                <label class="col-sm-3 control-label">Tahun Kendaraan *</label>
                                <div class="col-sm-9">
                                    <input type="number" name="tahun_kendaraan" class="form-control" min="1990" max="<?php echo date('Y'); ?>" required>
                                    <small class="text-muted">Diisi jika jaminan berupa BPKB Motor/Mobil</small>
                                </div>
                            </div>

                            <div class="form-group kendaraan-detail" id="status-pajak-group">
                                <label class="col-sm-3 control-label">Status Pajak Kendaraan *</label>
                                <div class="col-sm-9">
                                    <select name="status_pajak" class="form-control" required>
                                        <option value="">-- Pilih Status Pajak --</option>
                                        <option value="Aktif">Aktif</option>
                                        <option value="Tidak Aktif">Tidak Aktif</option>
                                    </select>
                                    <small class="text-muted">Diisi jika jaminan berupa BPKB Motor/Mobil</small>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Tujuan Pinjaman</label>
                                <div class="col-sm-9">
                                    <textarea name="tujuan_pinjaman" class="form-control" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-offset-3 col-sm-9">
                                    <button type="submit" name="submit" class="btn btn-primary"><i class="fa fa-save"></i> Simpan Data</button>
                                    <a href="<?php echo $redirect_page; ?>" class="btn btn-default"><i class="fa fa-arrow-left"></i> Kembali</a>
                                </div>
                            </div>
                        </form>

                        <?php elseif ($table == 'users' || $table == 'user' || $table == 'pengguna'): ?>
                        <form method="POST" action="tambah_data_universal.php" class="form-horizontal">
                            <input type="hidden" name="table" value="users">

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Username</label>
                                <div class="col-sm-9">
                                    <input type="text" name="username" class="form-control" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Password</label>
                                <div class="col-sm-9">
                                    <input type="password" name="password" class="form-control" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Nama Lengkap</label>
                                <div class="col-sm-9">
                                    <input type="text" name="nama_lengkap" class="form-control" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">Level</label>
                                <div class="col-sm-9">
                                    <select name="level" class="form-control" required>
                                        <option value="">-- Pilih Level --</option>
                                        <option value="admin">Admin</option>
                                        <option value="analis">Analis Kredit</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-offset-3 col-sm-9">
                                    <button type="submit" name="submit" class="btn btn-primary"><i class="fa fa-save"></i> Simpan Data</button>
                                    <a href="<?php echo $redirect_page; ?>" class="btn btn-default"><i class="fa fa-arrow-left"></i> Kembali</a>
                                </div>
                            </div>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fa fa-exclamation-circle"></i> Tabel tidak valid.
                        </div>
                        <a href="index_<?php echo $_SESSION['level']; ?>.php" class="btn btn-default"><i class="fa fa-arrow-left"></i> Kembali ke Dashboard</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'foot.php'; ?>

<script>
// Fungsi untuk menampilkan field detail kendaraan
function toggleKendaraanFields() {
    var jaminan = document.getElementById('jaminan-select');

    // Jika elemen jaminan-select tidak ditemukan, keluar dari fungsi
    if (!jaminan) return;

    var kendaraanFields = document.querySelectorAll('.kendaraan-detail');
    var tahunKendaraan = document.querySelector('input[name="tahun_kendaraan"]');
    var statusPajak = document.querySelector('select[name="status_pajak"]');

    // Selalu tampilkan field detail kendaraan karena jaminan hanya BPKB Motor/Mobil
    kendaraanFields.forEach(function(field) {
        field.style.display = 'block';
    });

    // Pastikan field tahun kendaraan dan status pajak memiliki atribut required
    if (tahunKendaraan) tahunKendaraan.setAttribute('required', 'required');
    if (statusPajak) statusPajak.setAttribute('required', 'required');
}

// Jalankan fungsi saat halaman dimuat
document.addEventListener('DOMContentLoaded', function() {
    toggleKendaraanFields();

    // Tambahkan event listener untuk perubahan pada dropdown jaminan
    var jaminanSelect = document.getElementById('jaminan-select');
    if (jaminanSelect) {
        jaminanSelect.addEventListener('change', toggleKendaraanFields);
    }
});
</script>
