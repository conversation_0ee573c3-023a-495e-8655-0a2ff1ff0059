<?php
// Debug untuk prediksi_baru.php

error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'koneksi.php';
include 'cek_session.php';
cek_akses(['analis']);

echo "<h2>Debug Prediksi Baru</h2>\n";

if (isset($_POST['submit'])) {
    echo "<h3>Data POST yang diterima:</h3>\n";
    echo "<pre>" . print_r($_POST, true) . "</pre>\n";
    
    // Test konversi data
    $penghasilan = (float)str_replace('.', '', $_POST['penghasilan']);
    $jumlah_pinjaman = (float)str_replace('.', '', $_POST['jumlah_pinjaman']);
    
    echo "<h3>Data setelah konversi:</h3>\n";
    echo "Penghasilan: $penghasilan<br>\n";
    echo "<PERSON><PERSON><PERSON>: $jumlah_pinjaman<br>\n";
    
    // Test insert ke database
    try {
        $sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, peker<PERSON><PERSON>, penghasilan, jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak, kepemilikan_rumah, umur, tujuan_pinjaman) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = mysqli_prepare($koneksi, $sql);
        if (!$stmt) {
            throw new Exception("Error prepare: " . mysqli_error($koneksi));
        }
        
        // Konversi data ke variabel terpisah untuk bind_param
        $nama = $_POST['nama_nasabah'];
        $jenis_kelamin = $_POST['jenis_kelamin'];
        $status_perkawinan = $_POST['status_perkawinan'];
        $pekerjaan = $_POST['pekerjaan'];
        $tanggungan = (int)$_POST['jumlah_tanggungan'];
        $jangka_waktu = (int)$_POST['jangka_waktu'];
        $jaminan = $_POST['jaminan'];
        $tahun_kendaraan = $_POST['tahun_kendaraan'];
        $status_pajak = $_POST['status_pajak'];
        $kepemilikan_rumah = $_POST['kepemilikan_rumah'];
        $umur = (int)$_POST['umur'];
        $tujuan_pinjaman = $_POST['tujuan_pinjaman'] ?? 'Kebutuhan Pribadi';

        mysqli_stmt_bind_param($stmt, "ssssdidissssis",
            $nama,
            $jenis_kelamin,
            $status_perkawinan,
            $pekerjaan,
            $penghasilan,
            $tanggungan,
            $jumlah_pinjaman,
            $jangka_waktu,
            $jaminan,
            $tahun_kendaraan,
            $status_pajak,
            $kepemilikan_rumah,
            $umur,
            $tujuan_pinjaman
        );
        
        if (mysqli_stmt_execute($stmt)) {
            $id_nasabah = mysqli_insert_id($koneksi);
            echo "<h3>✅ Data berhasil disimpan dengan ID: $id_nasabah</h3>\n";
            
            // Ambil data nasabah
            $query = "SELECT * FROM nasabah WHERE id_nasabah = ?";
            $stmt2 = mysqli_prepare($koneksi, $query);
            mysqli_stmt_bind_param($stmt2, "i", $id_nasabah);
            mysqli_stmt_execute($stmt2);
            $result = mysqli_stmt_get_result($stmt2);
            $nasabah_data = mysqli_fetch_assoc($result);
            
            echo "<h3>Data nasabah yang diambil:</h3>\n";
            echo "<pre>" . print_r($nasabah_data, true) . "</pre>\n";
            
            // Test prediksi
            require_once 'api_predict.php';

            echo "<h3>Melakukan prediksi...</h3>\n";
            echo "<p>Data nasabah untuk prediksi:</p>\n";
            echo "<ul>\n";
            echo "<li>Penghasilan: Rp " . number_format($nasabah_data['penghasilan'], 0, ',', '.') . "</li>\n";
            echo "<li>Jumlah Pinjaman: Rp " . number_format($nasabah_data['jumlah_pinjaman'], 0, ',', '.') . "</li>\n";
            echo "<li>Jumlah Tanggungan: " . $nasabah_data['jumlah_tanggungan'] . "</li>\n";
            echo "<li>Umur: " . $nasabah_data['umur'] . "</li>\n";
            echo "</ul>\n";

            try {
                $hasil_prediksi = prediksi_dengan_backpropagation($nasabah_data);
                echo "<p>✅ Fungsi prediksi berhasil dipanggil</p>\n";
            } catch (Exception $e) {
                echo "<p>❌ Error dalam fungsi prediksi: " . $e->getMessage() . "</p>\n";
                echo "<p>Mencoba prediksi fallback sederhana...</p>\n";

                // Prediksi fallback sederhana
                $penghasilan = floatval($nasabah_data['penghasilan']);
                $pinjaman = floatval($nasabah_data['jumlah_pinjaman']);
                $tanggungan = max(1, intval($nasabah_data['jumlah_tanggungan']));

                $rasio = $pinjaman / ($penghasilan * 12 / $tanggungan);
                $hasil_prediksi = [
                    'hasil_prediksi' => $rasio <= 0.9 ? 'Layak' : 'Tidak Layak',
                    'probabilitas' => $rasio <= 0.9 ? 0.8 : 0.3,
                    'keterangan' => "Prediksi fallback: rasio pinjaman " . number_format($rasio * 100, 1) . "%"
                ];
                echo "<p>✅ Prediksi fallback berhasil</p>\n";
            }
            
            echo "<h3>Hasil prediksi:</h3>\n";
            echo "<pre>" . print_r($hasil_prediksi, true) . "</pre>\n";
            
            // Test simpan hasil prediksi
            $id_prediksi = simpan_hasil_prediksi($nasabah_data, $hasil_prediksi);
            
            if ($id_prediksi) {
                echo "<h3>✅ Hasil prediksi berhasil disimpan dengan ID: $id_prediksi</h3>\n";
                
                // Tampilkan hasil prediksi
                echo "<div style='background: #d4edda; padding: 20px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
                echo "<h4>🎯 Hasil Prediksi Kelayakan Kredit</h4>";
                echo "<p><strong>Nama:</strong> " . htmlspecialchars($nasabah_data['nama_nasabah']) . "</p>";
                echo "<p><strong>Hasil:</strong> " . $hasil_prediksi['hasil_prediksi'] . "</p>";
                echo "<p><strong>Probabilitas:</strong> " . number_format($hasil_prediksi['probabilitas'] * 100, 2) . "%</p>";
                echo "<p><strong>Keterangan:</strong> " . htmlspecialchars($hasil_prediksi['keterangan']) . "</p>";
                echo "<p><a href='hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi'>Lihat Detail Lengkap</a></p>";
                echo "</div>";
                
            } else {
                echo "<h3>❌ Gagal menyimpan hasil prediksi</h3>\n";
            }
            
        } else {
            throw new Exception("Error execute: " . mysqli_error($koneksi));
        }
        
    } catch (Exception $e) {
        echo "<h3>❌ Error: " . $e->getMessage() . "</h3>\n";
    }
    
    echo "<hr>\n";
}
?>

<form method="post" style="max-width: 800px;">
    <h3>Form Test Debug</h3>
    
    <div style="display: flex; gap: 20px;">
        <div style="flex: 1;">
            <div style="margin: 10px 0;">
                <label>Nama Nasabah:</label><br>
                <input type="text" name="nama_nasabah" required style="width: 100%; padding: 5px;">
            </div>
            
            <div style="margin: 10px 0;">
                <label>Jenis Kelamin:</label><br>
                <select name="jenis_kelamin" required style="width: 100%; padding: 5px;">
                    <option value="Laki-laki">Laki-laki</option>
                    <option value="Perempuan">Perempuan</option>
                </select>
            </div>
            
            <div style="margin: 10px 0;">
                <label>Status Perkawinan:</label><br>
                <select name="status_perkawinan" required style="width: 100%; padding: 5px;">
                    <option value="Menikah">Menikah</option>
                    <option value="Belum Menikah">Belum Menikah</option>
                    <option value="Cerai">Cerai</option>
                </select>
            </div>
            
            <div style="margin: 10px 0;">
                <label>Pekerjaan:</label><br>
                <select name="pekerjaan" required style="width: 100%; padding: 5px;">
                    <option value="PNS">PNS</option>
                    <option value="Swasta">Swasta</option>
                    <option value="Wiraswasta">Wiraswasta</option>
                </select>
            </div>
            
            <div style="margin: 10px 0;">
                <label>Umur:</label><br>
                <input type="number" name="umur" min="20" max="60" required style="width: 100%; padding: 5px;">
            </div>
        </div>
        
        <div style="flex: 1;">
            <div style="margin: 10px 0;">
                <label>Penghasilan (tanpa titik):</label><br>
                <input type="text" name="penghasilan" required style="width: 100%; padding: 5px;">
            </div>
            
            <div style="margin: 10px 0;">
                <label>Jumlah Tanggungan:</label><br>
                <input type="number" name="jumlah_tanggungan" min="0" max="10" required style="width: 100%; padding: 5px;">
            </div>
            
            <div style="margin: 10px 0;">
                <label>Jumlah Pinjaman (tanpa titik):</label><br>
                <input type="text" name="jumlah_pinjaman" required style="width: 100%; padding: 5px;">
            </div>
            
            <div style="margin: 10px 0;">
                <label>Jangka Waktu (bulan):</label><br>
                <input type="number" name="jangka_waktu" min="6" max="60" value="12" required style="width: 100%; padding: 5px;">
            </div>
            
            <div style="margin: 10px 0;">
                <label>Jaminan:</label><br>
                <select name="jaminan" required style="width: 100%; padding: 5px;">
                    <option value="BPKB Motor">BPKB Motor</option>
                    <option value="BPKB Mobil">BPKB Mobil</option>
                </select>
            </div>
            
            <div style="margin: 10px 0;">
                <label>Tahun Kendaraan:</label><br>
                <input type="number" name="tahun_kendaraan" min="1990" max="<?php echo date('Y'); ?>" value="2020" required style="width: 100%; padding: 5px;">
            </div>
            
            <div style="margin: 10px 0;">
                <label>Status Pajak:</label><br>
                <select name="status_pajak" required style="width: 100%; padding: 5px;">
                    <option value="Aktif">Aktif</option>
                    <option value="Tidak Aktif">Tidak Aktif</option>
                </select>
            </div>
            
            <div style="margin: 10px 0;">
                <label>Kepemilikan Rumah:</label><br>
                <select name="kepemilikan_rumah" required style="width: 100%; padding: 5px;">
                    <option value="Milik Sendiri">Milik Sendiri</option>
                    <option value="Kontrak">Kontrak</option>
                    <option value="Orang Tua">Orang Tua</option>
                </select>
            </div>
            
            <div style="margin: 10px 0;">
                <label>Tujuan Pinjaman:</label><br>
                <input type="text" name="tujuan_pinjaman" value="Kebutuhan Pribadi" style="width: 100%; padding: 5px;">
            </div>
        </div>
    </div>
    
    <div style="margin: 20px 0;">
        <button type="submit" name="submit" style="padding: 10px 20px; background: #007cba; color: white; border: none;">
            Test Prediksi Lengkap
        </button>
    </div>
</form>

<p><a href="prediksi_baru.php">Kembali ke Form Asli</a></p>
