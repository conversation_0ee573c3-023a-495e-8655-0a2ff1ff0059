// Custom JavaScript for Leasing Website

$(document).ready(function() {

    // Show vehicle details fields - always show since we only have BPKB options
    if ($('#jaminan').length) {
        // Selalu tampilkan detail kendaraan karena jaminan hanya BPKB Motor/Mobil
        $('.vehicle-details').show();
        // Hapus required attribute dari semua field kendaraan
        $('.vehicle-details input, .vehicle-details select').prop('required', false);
        // Tambahkan required attribute hanya ke field tahun kendaraan dan status pajak
        $('#tahun_kendaraan, #status_pajak').prop('required', true);

        // Tetap tambahkan event listener untuk perubahan (jika diperlukan di masa depan)
        $('#jaminan').change(function() {
            // Selalu tampilkan detail kendaraan
            $('.vehicle-details').show();
            // Hapus required attribute dari semua field kendaraan
            $('.vehicle-details input, .vehicle-details select').prop('required', false);
            // Tambahkan required attribute hanya ke field tahun kendaraan dan status pajak
            $('#tahun_kendaraan, #status_pajak').prop('required', true);
        });
    }

    // Form validation for application form
    if ($('#applicationForm').length) {
        $('#applicationForm').submit(function(e) {
            let isValid = true;

            // Reset error messages
            $('.is-invalid').removeClass('is-invalid');
            $('.invalid-feedback').remove();

            // Validate required fields
            $(this).find('[required]').each(function() {
                if ($(this).val() === '') {
                    $(this).addClass('is-invalid');
                    $(this).after('<div class="invalid-feedback">Field ini harus diisi</div>');
                    isValid = false;
                }
            });

            // Validate numeric fields
            $('.numeric-only').each(function() {
                if ($(this).val() !== '' && !/^\d+$/.test($(this).val())) {
                    $(this).addClass('is-invalid');
                    $(this).after('<div class="invalid-feedback">Hanya angka yang diperbolehkan</div>');
                    isValid = false;
                }
            });

            // Validate penghasilan and jumlah_pinjaman
            if ($('#penghasilan').val() !== '') {
                // Hapus titik sebagai pemisah ribuan sebelum validasi
                let penghasilanValue = $('#penghasilan').val().replace(/\./g, '');
                if (parseInt(penghasilanValue) < 1000000) {
                    $('#penghasilan').addClass('is-invalid');
                    $('#penghasilan').after('<div class="invalid-feedback">Penghasilan minimal Rp 1.000.000</div>');
                    isValid = false;
                }
            }

            if ($('#jumlah_pinjaman').val() !== '') {
                // Hapus titik sebagai pemisah ribuan sebelum validasi
                let pinjamanValue = $('#jumlah_pinjaman').val().replace(/\./g, '');
                if (parseInt(pinjamanValue) < 5000000) {
                    $('#jumlah_pinjaman').addClass('is-invalid');
                    $('#jumlah_pinjaman').after('<div class="invalid-feedback">Jumlah pinjaman minimal Rp 5.000.000</div>');
                    isValid = false;
                }
            }

            // Validasi field kendaraan jika jaminan berupa BPKB
            if ($('#jaminan').val() === 'BPKB Motor' || $('#jaminan').val() === 'BPKB Mobil') {
                // Periksa apakah field kendaraan yang wajib sudah diisi
                let requiredVehicleFields = [
                    'tahun_kendaraan', 'status_pajak'
                ];

                requiredVehicleFields.forEach(function(field) {
                    if ($('#' + field).val() === '') {
                        $('#' + field).addClass('is-invalid');
                        $('#' + field).after('<div class="invalid-feedback">Field ini harus diisi jika jaminan berupa BPKB</div>');
                        isValid = false;
                    }
                });
            }

            // Prevent form submission if validation fails
            if (!isValid) {
                e.preventDefault();

                // Scroll to first error
                $('html, body').animate({
                    scrollTop: $('.is-invalid:first').offset().top - 100
                }, 500);
            }
        });
    }

    // Format currency input
    $('.currency-input').on('input', function() {
        // Remove non-numeric characters
        let value = $(this).val().replace(/\D/g, '');

        // Format with thousand separator
        $(this).val(formatNumber(value));
    });

    // Format number with thousand separator
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }

    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Smooth scrolling for anchor links
    $('a.smooth-scroll').click(function(e) {
        if (this.hash !== '') {
            e.preventDefault();

            const hash = this.hash;

            $('html, body').animate({
                scrollTop: $(hash).offset().top - 70
            }, 800);
        }
    });
});
