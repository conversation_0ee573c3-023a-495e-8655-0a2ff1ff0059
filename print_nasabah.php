<?php
include 'koneksi.php';
include 'cek_session.php';
cek_akses(['admin', 'analis']);

$id_nasabah = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($id_nasabah <= 0) {
    echo '<script>alert("ID Nasabah tidak valid"); window.close();</script>';
    exit;
}

// Ambil data nasabah
$sql_nasabah = "SELECT * FROM nasabah WHERE id_nasabah = ?";
$stmt = mysqli_prepare($koneksi, $sql_nasabah);
mysqli_stmt_bind_param($stmt, "i", $id_nasabah);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$nasabah = mysqli_fetch_assoc($result);

if (!$nasabah) {
    echo '<script>alert("Data nasabah tidak ditemukan"); window.close();</script>';
    exit;
}

// Ambil data prediksi
$sql_prediksi = "SELECT * FROM hasil_prediksi WHERE id_nasabah = ? ORDER BY tanggal_prediksi DESC LIMIT 1";
$stmt_prediksi = mysqli_prepare($koneksi, $sql_prediksi);
mysqli_stmt_bind_param($stmt_prediksi, "i", $id_nasabah);
mysqli_stmt_execute($stmt_prediksi);
$result_prediksi = mysqli_stmt_get_result($stmt_prediksi);
$prediksi = mysqli_fetch_assoc($result_prediksi);

// Hitung analisis
$penghasilan_per_kapita = $nasabah['penghasilan'] / max(1, $nasabah['jumlah_tanggungan']);
$rasio_pinjaman = ($nasabah['jumlah_pinjaman'] / $nasabah['penghasilan']) * 100;
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Laporan Prediksi Kelayakan Kredit - <?php echo htmlspecialchars($nasabah['nama_nasabah']); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        .section {
            margin-bottom: 25px;
        }
        .section-title {
            background-color: #f5f5f5;
            padding: 8px 12px;
            border-left: 4px solid #007bff;
            font-weight: bold;
            margin-bottom: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .result-box {
            border: 2px solid #ddd;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            border-radius: 5px;
        }
        .result-layak {
            border-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .result-tidak-layak {
            border-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .footer {
            margin-top: 40px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
            font-size: 10px;
            color: #666;
        }
        .two-column {
            display: flex;
            gap: 20px;
        }
        .column {
            flex: 1;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>LAPORAN PREDIKSI KELAYAKAN KREDIT</h1>
        <h2>Sistem Prediksi dengan Algoritma Backpropagation Neural Network</h2>
        <p>Tanggal Cetak: <?php echo date('d F Y, H:i:s'); ?></p>
    </div>

    <!-- Hasil Prediksi -->
    <?php if ($prediksi): ?>
    <div class="result-box <?php echo $prediksi['hasil_prediksi'] == 'Layak' ? 'result-layak' : 'result-tidak-layak'; ?>">
        <h2>HASIL PREDIKSI: <?php echo strtoupper($prediksi['hasil_prediksi']); ?></h2>
        <p>Probabilitas: <?php echo number_format($prediksi['probabilitas'] * 100, 1); ?>%</p>
        <p>Tanggal Prediksi: <?php echo date('d F Y, H:i', strtotime($prediksi['tanggal_prediksi'])); ?></p>
    </div>
    <?php endif; ?>

    <!-- Data Nasabah -->
    <div class="section">
        <div class="section-title">DATA NASABAH</div>
        <table>
            <tr>
                <th width="30%">ID Nasabah</th>
                <td><?php echo $nasabah['id_nasabah']; ?></td>
            </tr>
            <tr>
                <th>Nama Lengkap</th>
                <td><?php echo htmlspecialchars($nasabah['nama_nasabah']); ?></td>
            </tr>
            <tr>
                <th>Umur</th>
                <td><?php echo $nasabah['umur']; ?> tahun</td>
            </tr>
            <tr>
                <th>Jenis Kelamin</th>
                <td><?php echo $nasabah['jenis_kelamin']; ?></td>
            </tr>
            <tr>
                <th>Status Perkawinan</th>
                <td><?php echo $nasabah['status_perkawinan']; ?></td>
            </tr>
            <tr>
                <th>Pekerjaan</th>
                <td><?php echo $nasabah['pekerjaan']; ?></td>
            </tr>
            <tr>
                <th>Jumlah Tanggungan</th>
                <td><?php echo $nasabah['jumlah_tanggungan']; ?> orang</td>
            </tr>
        </table>
    </div>

    <!-- Data Finansial -->
    <div class="section">
        <div class="section-title">DATA FINANSIAL</div>
        <table>
            <tr>
                <th width="30%">Penghasilan per Bulan</th>
                <td>Rp <?php echo number_format($nasabah['penghasilan'], 0, ',', '.'); ?></td>
            </tr>
            <tr>
                <th>Penghasilan per Kapita</th>
                <td>Rp <?php echo number_format($penghasilan_per_kapita, 0, ',', '.'); ?></td>
            </tr>
            <tr>
                <th>Jumlah Pinjaman</th>
                <td>Rp <?php echo number_format($nasabah['jumlah_pinjaman'], 0, ',', '.'); ?></td>
            </tr>
            <tr>
                <th>Jangka Waktu</th>
                <td><?php echo $nasabah['jangka_waktu']; ?> bulan</td>
            </tr>
            <tr>
                <th>Rasio Pinjaman terhadap Penghasilan</th>
                <td><?php echo number_format($rasio_pinjaman, 1); ?>%</td>
            </tr>
            <tr>
                <th>Kepemilikan Rumah</th>
                <td><?php echo $nasabah['kepemilikan_rumah']; ?></td>
            </tr>
        </table>
    </div>

    <!-- Data Jaminan -->
    <div class="section">
        <div class="section-title">DATA JAMINAN</div>
        <table>
            <tr>
                <th width="30%">Jenis Jaminan</th>
                <td><?php echo $nasabah['jaminan']; ?></td>
            </tr>
            <tr>
                <th>Tahun Kendaraan</th>
                <td><?php echo $nasabah['tahun_kendaraan']; ?></td>
            </tr>
            <tr>
                <th>Status Pajak Kendaraan</th>
                <td><?php echo $nasabah['status_pajak']; ?></td>
            </tr>
            <tr>
                <th>Tujuan Pinjaman</th>
                <td><?php echo htmlspecialchars($nasabah['tujuan_pinjaman'] ?: 'Kebutuhan Pribadi'); ?></td>
            </tr>
        </table>
    </div>

    <!-- Analisis Kelayakan -->
    <?php if ($prediksi): ?>
    <div class="section">
        <div class="section-title">ANALISIS KELAYAKAN</div>
        <p><strong>Keterangan Prediksi:</strong></p>
        <p><?php echo nl2br(htmlspecialchars($prediksi['keterangan'])); ?></p>
        
        <p><strong>Faktor-faktor yang Dipertimbangkan:</strong></p>
        <ul>
            <li>Rasio penghasilan per kapita: Rp <?php echo number_format($penghasilan_per_kapita, 0, ',', '.'); ?></li>
            <li>Rasio pinjaman terhadap penghasilan: <?php echo number_format($rasio_pinjaman, 1); ?>%</li>
            <li>Stabilitas pekerjaan: <?php echo $nasabah['pekerjaan']; ?></li>
            <li>Jenis dan kondisi jaminan: <?php echo $nasabah['jaminan']; ?> tahun <?php echo $nasabah['tahun_kendaraan']; ?></li>
            <li>Status kepemilikan aset: <?php echo $nasabah['kepemilikan_rumah']; ?></li>
        </ul>
    </div>
    <?php endif; ?>

    <!-- Footer -->
    <div class="footer">
        <p><strong>Catatan:</strong></p>
        <ul>
            <li>Laporan ini dihasilkan secara otomatis oleh sistem prediksi kelayakan kredit</li>
            <li>Prediksi menggunakan algoritma Backpropagation Neural Network</li>
            <li>Hasil prediksi merupakan rekomendasi dan perlu dikombinasikan dengan analisis manual</li>
            <li>Untuk informasi lebih lanjut, hubungi analis kredit</li>
        </ul>
        
        <div style="margin-top: 30px;">
            <table style="border: none; width: 100%;">
                <tr style="border: none;">
                    <td style="border: none; width: 50%; text-align: center;">
                        <p>Dicetak oleh:</p>
                        <br><br>
                        <p>_______________________</p>
                        <p><?php echo htmlspecialchars($_SESSION['nama_lengkap']); ?></p>
                        <p><?php echo ucfirst($_SESSION['role']); ?></p>
                    </td>
                    <td style="border: none; width: 50%; text-align: center;">
                        <p>Disetujui oleh:</p>
                        <br><br>
                        <p>_______________________</p>
                        <p>Manager Kredit</p>
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <script>
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
