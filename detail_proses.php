<?php
include 'koneksi.php';
require_once 'nav_analis.php';

// Cek apakah ada parameter id_prediksi
if (!isset($_GET['id_prediksi'])) {
    header("Location: laporan.php");
    exit;
}

$id_prediksi = $_GET['id_prediksi'];

// Ambil data prediksi
$sql = "SELECT pd.*, n.nama_nasabah, n.umur, n.jenis_kelamin, n.status_perkawin<PERSON>, n.p<PERSON>, 
               n.pen<PERSON><PERSON><PERSON>, n.jum<PERSON>_pinjaman, n.kepemilikan_rumah, n.jaminan,
               lp.akurasi, lp.parameter, m.nama_model, m.jenis_model
        FROM prediksi_detail pd
        JOIN nasabah n ON pd.id_nasabah = n.id_nasabah
        JOIN laporan_prediksi lp ON pd.id_laporan = lp.id_laporan
        JOIN model_ml m ON lp.id_model = m.id_model
        WHERE pd.id_prediksi = ?";
$stmt = $koneksi->prepare($sql);
$stmt->bind_param("i", $id_prediksi);
$stmt->execute();
$prediksi = $stmt->get_result()->fetch_assoc();

if (!$prediksi) {
    header("Location: laporan.php");
    exit;
}

// Ambil data proses perhitungan
$sql_proses = "SELECT * FROM proses_perhitungan WHERE id_prediksi = ? ORDER BY waktu_proses";
$stmt_proses = $koneksi->prepare($sql_proses);
$stmt_proses->bind_param("i", $id_prediksi);
$stmt_proses->execute();
$result_proses = $stmt_proses->get_result();
$proses_perhitungan = [];
while ($row = $result_proses->fetch_assoc()) {
    $proses_perhitungan[] = $row;
}

// Fungsi untuk memformat data JSON
function formatJson($json) {
    $data = json_decode($json, true);
    if ($data === null) {
        return '<pre>' . htmlspecialchars($json) . '</pre>';
    }
    
    $json_string = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    return '<pre>' . htmlspecialchars($json_string) . '</pre>';
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Detail Proses Perhitungan</h1>
                <ol class="breadcrumb">
                    <li><a href="laporan.php">Laporan</a></li>
                    <li><a href="detail_prediksi.php?id_nasabah=<?php echo $prediksi['id_nasabah']; ?>&id_laporan=<?php echo $prediksi['id_laporan']; ?>">Detail Prediksi</a></li>
                    <li class="active">Proses Perhitungan</li>
                </ol>
            </div>

            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>Data Nasabah</h4>
                    </div>
                    <div class="panel-body">
                        <table class="table table-bordered">
                            <tr>
                                <th width="30%">Nama Nasabah</th>
                                <td><?php echo htmlspecialchars($prediksi['nama_nasabah']); ?></td>
                            </tr>
                            <tr>
                                <th>Umur</th>
                                <td><?php echo htmlspecialchars($prediksi['umur']); ?> tahun</td>
                            </tr>
                            <tr>
                                <th>Jenis Kelamin</th>
                                <td><?php echo htmlspecialchars($prediksi['jenis_kelamin']); ?></td>
                            </tr>
                            <tr>
                                <th>Status Perkawinan</th>
                                <td><?php echo htmlspecialchars($prediksi['status_perkawinan']); ?></td>
                            </tr>
                            <tr>
                                <th>Pekerjaan</th>
                                <td><?php echo htmlspecialchars($prediksi['pekerjaan']); ?></td>
                            </tr>
                            <tr>
                                <th>Penghasilan</th>
                                <td>Rp <?php echo number_format($prediksi['penghasilan'], 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <th>Jumlah Pinjaman</th>
                                <td>Rp <?php echo number_format($prediksi['jumlah_pinjaman'], 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <th>Kepemilikan Rumah</th>
                                <td><?php echo htmlspecialchars($prediksi['kepemilikan_rumah']); ?></td>
                            </tr>
                            <tr>
                                <th>Jaminan</th>
                                <td><?php echo htmlspecialchars($prediksi['jaminan']); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="panel panel-<?php echo $prediksi['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?>">
                    <div class="panel-heading">
                        <h4>Hasil Prediksi</h4>
                    </div>
                    <div class="panel-body">
                        <div class="text-center" style="margin-bottom: 20px;">
                            <h3>Status: 
                                <span class="label label-<?php echo $prediksi['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?>" style="font-size: 24px;">
                                    <?php echo htmlspecialchars($prediksi['hasil_prediksi']); ?>
                                </span>
                            </h3>
                        </div>
                        <table class="table table-bordered">
                            <tr>
                                <th width="30%">Tanggal Prediksi</th>
                                <td><?php echo date('d/m/Y H:i', strtotime($prediksi['tanggal_prediksi'])); ?></td>
                            </tr>
                            <tr>
                                <th>Model</th>
                                <td><?php echo htmlspecialchars($prediksi['nama_model']); ?></td>
                            </tr>
                            <tr>
                                <th>Akurasi Model</th>
                                <td><?php echo htmlspecialchars($prediksi['akurasi']); ?>%</td>
                            </tr>
                            <tr>
                                <th>Probabilitas</th>
                                <td><?php echo number_format($prediksi['probabilitas'] * 100, 2); ?>%</td>
                            </tr>
                            <tr>
                                <th>Keterangan</th>
                                <td><?php echo htmlspecialchars($prediksi['keterangan']); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h4>Proses Perhitungan</h4>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Tahap</th>
                                        <th>Waktu Proses</th>
                                        <th>Detail</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($proses_perhitungan)): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">Tidak ada data proses perhitungan</td>
                                    </tr>
                                    <?php else: ?>
                                    <?php $no = 1; foreach ($proses_perhitungan as $proses): ?>
                                    <tr>
                                        <td><?php echo $no++; ?></td>
                                        <td><?php echo htmlspecialchars($proses['tahap']); ?></td>
                                        <td><?php echo date('d/m/Y H:i:s', strtotime($proses['waktu_proses'])); ?></td>
                                        <td>
                                            <button type="button" class="btn btn-info btn-sm" data-toggle="modal" data-target="#modal-<?php echo $proses['id_proses']; ?>">
                                                <i class="fa fa-eye"></i> Lihat Detail
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-12">
                <a href="detail_prediksi.php?id_nasabah=<?php echo $prediksi['id_nasabah']; ?>&id_laporan=<?php echo $prediksi['id_laporan']; ?>" class="btn btn-default">
                    <i class="fa fa-arrow-left"></i> Kembali ke Detail Prediksi
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Modal untuk detail proses perhitungan -->
<?php foreach ($proses_perhitungan as $proses): ?>
<div class="modal fade" id="modal-<?php echo $proses['id_proses']; ?>" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Detail Proses: <?php echo htmlspecialchars($proses['tahap']); ?></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h4>Input Data</h4>
                        <?php echo formatJson($proses['input_data']); ?>
                    </div>
                    <div class="col-md-6">
                        <h4>Output Data</h4>
                        <?php echo formatJson($proses['output_data']); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php 
require_once 'foot.php';
?>
