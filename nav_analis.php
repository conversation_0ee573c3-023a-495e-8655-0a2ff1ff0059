<?php
// Mulai session jika belum dimulai
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
include 'koneksi.php';

// Cek apakah user sudah login
if (!isset($_SESSION['username']) || !isset($_SESSION['level']) || $_SESSION['level'] != 'analis') {
    header("Location: login.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>SISTEM PREDIKSI KELAYAKAN KREDIT</title>

    <!-- Bootstrap Core CSS -->
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="assets/css/metisMenu.min.css" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="assets/css/timeline.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/startmin.css" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="assets/css/morris.css" rel="stylesheet">

    <!-- Font Awesome CSS -->
    <link href="assets/css/font-awesome.min.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="assets/css/dataTables/dataTables.bootstrap.css" rel="stylesheet">

</head>

<body>
    <!-- Sidebar overlay for mobile -->
    <div class="sidebar-overlay"></div>

    <div id="wrapper">
        <!-- Navigation -->
        <nav class="navbar navbar-default navbar-fixed-top" role="navigation">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle sidebar-toggle" data-toggle="collapse" data-target=".sidebar-collapse">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="index_analis.php">Sistem Prediksi Kelayakan Kredit</a>
            </div>

            <!-- Top Navigation Right -->
            <ul class="nav navbar-top-links navbar-right">
                <li class="dropdown">
                    <div class="user-info">
                        <i class="fa fa-user-circle"></i>
                        <span><?php
                        // Pastikan session sudah dimulai dan variabel username ada
                        if (session_status() === PHP_SESSION_NONE) {
                            session_start();
                        }
                        echo isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : 'Analis Kredit';
                        ?></span>
                    </div>
                </li>
                <li>
                    <a href="logout.php" title="Logout">
                        <i class="fa fa-sign-out"></i>
                    </a>
                </li>
            </ul>

            <!-- Sidebar -->
            <div class="navbar-default sidebar" role="navigation">
                <div class="sidebar-nav navbar-collapse sidebar-collapse">
                    <ul class="nav" id="side-menu">
                        <li>
                            <a href="index_analis.php"><i class="fa fa-home fa-fw"></i> Dashboard</a>
                        </li>
                        <li>
                            <a href="data_nasabah_analis.php"><i class="fa fa-users fa-fw"></i> Data Nasabah</a>
                        </li>
                        <li>
                            <a href="#" class="dropdown-toggle" data-toggle="collapse" data-target="#prediksi-submenu"><i class="fa fa-calculator fa-fw"></i> Prediksi Kelayakan <i class="fa fa-caret-down"></i></a>
                            <ul class="nav nav-second-level collapse" id="prediksi-submenu">
                                <li>
                                    <a href="prediksi_baru.php"><i class="fa fa-plus fa-fw"></i> Prediksi Nasabah Baru</a>
                                </li>
                                <li>
                                    <a href="prediksi_nasabah.php"><i class="fa fa-globe fa-fw"></i> Prediksi Nasabah Website</a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="daftar_kelayakan.php"><i class="fa fa-check-circle fa-fw"></i> Daftar Kelayakan</a>
                        </li>
                        <li>
                            <a href="laporan.php"><i class="fa fa-flag fa-fw"></i> Laporan</a>
                        </li>
                    </ul>
                    </ul>
                </div>
            </div>
        </nav>


    </div>
    <!-- jQuery -->
    <script src="assets/js/jquery.min.js"></script>

    <!-- Bootstrap Core JavaScript -->
    <script src="assets/js/bootstrap.min.js"></script>

    <!-- Metis Menu Plugin JavaScript -->
    <script src="assets/js/metisMenu.min.js"></script>

    <!-- Custom Theme JavaScript -->
    <script src="assets/js/startmin.js"></script>

    <!-- Custom Sidebar JavaScript -->
    <script src="assets/js/custom-sidebar.js"></script>
</body>
</html>