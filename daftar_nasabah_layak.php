<?php
include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);
require_once 'nav_analis.php';

// Ambil daftar nasabah dengan kelayakan Layak
$sql = "SELECT id_nasabah, nama_nasabah, umur, jenis_kela<PERSON>, pek<PERSON><PERSON><PERSON>, pengh<PERSON><PERSON>,
              jumlah_pinjaman, kepemilikan_rumah, jaminan, kelayakan
       FROM nasabah
       WHERE kelayakan = 'Layak'
       ORDER BY nama_nasabah";

// Ambil data prediksi terpisah untuk digunakan nanti
// Gunakan subquery untuk mendapatkan id_prediksi terbaru untuk setiap nasabah
$sql_prediksi = "SELECT pd.id_nasabah, pd.id_prediksi, pd.tanggal_prediksi, pd.id_laporan
                FROM prediksi_detail pd
                INNER JOIN (
                    SELECT id_nasabah, MAX(tanggal_prediksi) as max_tanggal
                    FROM prediksi_detail
                    GROUP BY id_nasabah
                ) latest ON pd.id_nasabah = latest.id_nasabah AND pd.tanggal_prediksi = latest.max_tanggal
                WHERE pd.id_nasabah IN (SELECT id_nasabah FROM nasabah WHERE kelayakan = 'Layak')";
$result_prediksi = $koneksi->query($sql_prediksi);

// Buat array untuk menyimpan data prediksi
$prediksi_data = [];
if ($result_prediksi) {
    while ($row = $result_prediksi->fetch_assoc()) {
        $prediksi_data[$row['id_nasabah']] = [
            'id_prediksi' => $row['id_prediksi'],
            'tanggal_prediksi' => $row['tanggal_prediksi'],
            'id_laporan' => $row['id_laporan']
        ];
    }
} else {
    // Log error jika query gagal
    error_log("Error in prediksi query: " . $koneksi->error);
}
$result = $koneksi->query($sql);
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Daftar Nasabah Layak</h1>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-success">
                    <div class="panel-heading">
                        <h4>Daftar Nasabah dengan Status Kelayakan: Layak
                            <?php
                            // Hitung jumlah data
                            $count_query = "SELECT COUNT(*) as total FROM nasabah WHERE kelayakan = 'Layak'";
                            $count_result = mysqli_query($koneksi, $count_query);
                            $count_data = mysqli_fetch_assoc($count_result);
                            echo "(" . $count_data['total'] . " nasabah)";
                            ?>
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover" id="dataTables-kelayakan">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Nama Nasabah</th>
                                        <th>Umur</th>
                                        <th>Jenis Kelamin</th>
                                        <th>Pekerjaan</th>
                                        <th>Penghasilan</th>
                                        <th>Jumlah Pinjaman</th>
                                        <th>Kepemilikan Rumah</th>
                                        <th>Jaminan</th>
                                        <th>Status Kelayakan</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    if ($result->num_rows > 0) {
                                        $no = 1;
                                        while ($row = $result->fetch_assoc()) {
                                    ?>
                                    <tr>
                                        <td><?php echo $no++; ?></td>
                                        <td><?php echo htmlspecialchars($row['nama_nasabah']); ?></td>
                                        <td><?php echo $row['umur']; ?> tahun</td>
                                        <td><?php echo htmlspecialchars($row['jenis_kelamin']); ?></td>
                                        <td><?php echo htmlspecialchars($row['pekerjaan']); ?></td>
                                        <td>Rp <?php echo number_format($row['penghasilan'], 0, ',', '.'); ?></td>
                                        <td>Rp <?php echo number_format($row['jumlah_pinjaman'], 0, ',', '.'); ?></td>
                                        <td><?php echo htmlspecialchars($row['kepemilikan_rumah']); ?></td>
                                        <td><?php echo htmlspecialchars($row['jaminan']); ?></td>
                                        <td>
                                            <span class="label label-success">
                                                <?php echo htmlspecialchars($row['kelayakan']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (isset($prediksi_data[$row['id_nasabah']])): ?>
                                            <a href="detail_prediksi.php?id=<?php echo $prediksi_data[$row['id_nasabah']]['id_prediksi']; ?>" class="btn btn-info btn-sm">
                                                <i class="fa fa-eye"></i>
                                            </a>
                                            <a href="cetak_hasil.php?id_prediksi=<?php echo $prediksi_data[$row['id_nasabah']]['id_prediksi']; ?>" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fa fa-print"></i>
                                            </a>
                                            <?php else: ?>
                                            <button class="btn btn-default btn-sm" disabled>
                                                <i class="fa fa-eye-slash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php
                                        }
                                    } else {
                                    ?>
                                    <tr>
                                        <td colspan="11" class="text-center">Tidak ada data nasabah dengan status kelayakan Layak</td>
                                    </tr>
                                    <?php
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#dataTables-kelayakan').DataTable({
        responsive: true,
        "language": {
            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
            "zeroRecords": "Tidak ada data yang ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total entri)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "pageLength": 25
    });
});
</script>

<?php
require_once 'foot.php';
?>
