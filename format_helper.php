<?php
// Cek apakah fungsi formatRupiah sudah ada
if (!function_exists('formatRupiah')) {
    function formatRupiah($angka, $dengan_rp = true) {
        if (!is_numeric($angka)) {
            return $angka; // Kembalikan nilai asli jika bukan angka
        }
        
        $hasil = number_format($angka, 0, ',', '.');
        if ($dengan_rp) {
            return "Rp " . $hasil;
        }
        return $hasil;
    }
}
?>
