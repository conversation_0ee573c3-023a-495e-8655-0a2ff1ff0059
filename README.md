# Sistem Prediksi Kelayakan Kredit dengan Algoritma Backpropagation

Sistem ini menggunakan algoritma Backpropagation Neural Network untuk memprediksi kelayakan pemberian kredit kepada nasabah berdasarkan berbagai faktor se<PERSON>i pen<PERSON>, jam<PERSON><PERSON>, dan profil nasabah.

## Fitur Utama

- **Prediksi Kelayakan Kredit**: Menggunakan model Backpropagation Neural Network
- **REST API**: Server Python Flask untuk model machine learning
- **Interface Web**: Dashboard PHP untuk input data dan melihat hasil
- **Multi-Role**: Support untuk admin dan analis
- **Database Integration**: Penyimpanan data nasabah dan hasil prediksi

## Persyaratan Sistem

### Software yang Diperlukan:
1. **XAMPP/Laragon** (Apache + MySQL + PHP)
2. **Python 3.8+**
3. **Web Browser** (Chrome, Firefox, Edge)

### Python Dependencies:
- Flask 2.3.3
- scikit-learn 1.3.0
- pandas 2.0.3
- numpy 1.24.3
- mysql-connector-python 8.1.0

## Instalasi dan Setup

### 1. Setup Otomatis (Recommended)
```bash
# Jalankan script setup otomatis
setup_system.bat
```

### 2. Setup Manual

#### A. Install Python Dependencies
```bash
pip install -r requirements.txt
```

#### B. Setup Database
1. Pastikan MySQL/MariaDB berjalan
2. Import database:
```bash
mysql -u root -p < database_setup.sql
```

#### C. Konfigurasi
- Pastikan XAMPP/Laragon berjalan
- Letakkan folder project di `htdocs` atau `www`

## Cara Menjalankan

### 1. Start Server Model Python
```bash
# Jalankan server model di port 5000
start_model_server.bat
```
**PENTING**: Jangan tutup jendela ini selama sistem digunakan!

### 2. Akses Web Interface
Buka browser dan akses:
```
http://localhost/sistem_prediksi_backpropagation(bismillah)/
```

### 3. Login
- **Admin**: username=`admin`, password=`password`
- **Analis**: username=`analis`, password=`password`

## Struktur Project

```
sistem_prediksi_backpropagation(bismillah)/
├── model/                          # Model dan dataset
│   ├── backpropagation_model.pkl   # Model terlatih
│   ├── scaler.pkl                  # Scaler untuk normalisasi
│   └── dataset_nasabah.csv         # Dataset training
├── api/                            # API endpoints PHP
├── assets/                         # CSS, JS, images
├── model_wrapper.py                # Server Flask Python
├── prediksi_baru.php              # Form input prediksi
├── database_setup.sql             # Setup database
├── requirements.txt               # Python dependencies
├── setup_system.bat              # Setup otomatis
└── start_model_server.bat         # Start server model
```

## Cara Menggunakan

### 1. Input Data Nasabah Baru
1. Login sebagai analis
2. Pilih menu "Prediksi Nasabah Baru"
3. Isi form dengan data nasabah:
   - Data pribadi (nama, umur, jenis kelamin, dll)
   - Data finansial (penghasilan, jumlah pinjaman, dll)
   - Data jaminan (BPKB mobil/motor, tahun, status pajak)
4. Klik "Simpan & Prediksi Kelayakan"

### 2. Melihat Hasil Prediksi
- Sistem akan menampilkan hasil prediksi (Layak/Tidak Layak)
- Probabilitas kelayakan (0-100%)
- Penjelasan detail faktor-faktor yang mempengaruhi

### 3. Mengelola Data
- **Admin**: Dapat mengelola semua data dan user
- **Analis**: Dapat mengelola data nasabah dan melihat laporan

## Troubleshooting

### Database Error (Fatal error: Call to a member function query())
```bash
# Perbaikan otomatis
fix_database_issues.bat

# Atau manual
setup_database.bat
php fix_database_compatibility.php
```

### Server Model Tidak Berjalan
```bash
# Cek Python
python --version

# Install dependencies
pip install -r requirements.txt

# Jalankan manual
python model_wrapper.py
```

### Database Connection Error
1. Pastikan MySQL berjalan
2. Cek koneksi database di `koneksi.php`
3. Import ulang `database_setup.sql`
4. Jalankan `setup_database.bat`

### Model Tidak Ditemukan
- Model akan dibuat otomatis saat pertama kali dijalankan
- Jika gagal, cek file `model/dataset_nasabah.csv`

### Error "Connection Refused"
- Pastikan server model Python berjalan di port 5000
- Cek firewall/antivirus yang mungkin memblokir

### Sistem Tidak Berfungsi Setelah Setup
```bash
# Jalankan perbaikan lengkap
fix_database_issues.bat

# Test sistem
test_system.bat
```

## API Endpoints

### Python Flask API (Port 5000)
- `POST /api/predict` - Prediksi kelayakan kredit
- `GET /api/model-info` - Informasi model

### PHP API
- `api/predict.php` - Wrapper untuk Python API
- `api/model-info.php` - Informasi model

## Kontribusi

Untuk melaporkan bug atau request fitur, silakan buat issue di repository ini.

## Lisensi

Project ini dibuat untuk keperluan edukasi dan penelitian.
