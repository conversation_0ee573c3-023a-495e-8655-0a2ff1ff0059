<?php
// Script untuk mengecek status server Python dan memberikan instruksi

echo "<h2>Status Server Python untuk Sistem Prediksi</h2>\n";

// Test koneksi ke server Python
$python_api_url = 'http://localhost:5000/api/predict';
$ch = curl_init($python_api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['test' => true]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "<div style='color: red;'>";
    echo "<h3>❌ Server Python Tidak Berjalan</h3>\n";
    echo "<p><strong>Error:</strong> $curl_error</p>\n";
    echo "<p>Sistem akan menggunakan prediksi fallback (rule-based) yang sudah tersedia.</p>\n";
    echo "</div>";
    
    echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #ccc; margin: 10px 0;'>";
    echo "<h4>📋 Cara Menjalankan Server Python (Opsional):</h4>\n";
    echo "<ol>\n";
    echo "<li>Buka Command Prompt atau Terminal</li>\n";
    echo "<li>Masuk ke direktori: <code>cd c:\\laragon\\www\\ngasal</code></li>\n";
    echo "<li>Jalankan: <code>python model_wrapper.py</code></li>\n";
    echo "<li>Atau double-click file: <code>start_model_server.bat</code></li>\n";
    echo "</ol>\n";
    echo "<p><strong>Catatan:</strong> Server Python tidak wajib dijalankan. Sistem tetap dapat melakukan prediksi menggunakan algoritma rule-based yang sudah terintegrasi.</p>\n";
    echo "</div>";
    
} else {
    echo "<div style='color: green;'>";
    echo "<h3>✅ Server Python Berjalan Normal</h3>\n";
    echo "<p><strong>Status:</strong> HTTP $http_code</p>\n";
    echo "<p><strong>Response:</strong> $response</p>\n";
    echo "<p>Sistem akan menggunakan model Backpropagation Neural Network dari Python.</p>\n";
    echo "</div>";
}

echo "<hr>\n";
echo "<h3>🧪 Test Prediksi Sistem</h3>\n";

// Test dengan data nasabah
include 'koneksi.php';

$query = "SELECT * FROM nasabah LIMIT 1";
$result = mysqli_query($koneksi, $query);

if ($result && mysqli_num_rows($result) > 0) {
    $nasabah = mysqli_fetch_assoc($result);
    
    echo "<p><strong>Data Test:</strong> " . $nasabah['nama_nasabah'] . " (ID: " . $nasabah['id_nasabah'] . ")</p>\n";
    
    try {
        require_once 'api_predict.php';
        
        $start_time = microtime(true);
        $hasil_prediksi = prediksi_dengan_backpropagation($nasabah);
        $end_time = microtime(true);
        $execution_time = round(($end_time - $start_time) * 1000, 2);
        
        echo "<div style='background: #f0fff0; padding: 15px; border: 1px solid #90EE90; margin: 10px 0;'>";
        echo "<h4>✅ Test Prediksi Berhasil</h4>\n";
        echo "<p><strong>Hasil:</strong> " . $hasil_prediksi['hasil_prediksi'] . "</p>\n";
        echo "<p><strong>Probabilitas:</strong> " . number_format($hasil_prediksi['probabilitas'] * 100, 2) . "%</p>\n";
        echo "<p><strong>Waktu Eksekusi:</strong> {$execution_time} ms</p>\n";
        echo "<p><strong>Keterangan:</strong> " . substr($hasil_prediksi['keterangan'], 0, 200) . "...</p>\n";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #fff0f0; padding: 15px; border: 1px solid #ffcccb; margin: 10px 0;'>";
        echo "<h4>❌ Test Prediksi Gagal</h4>\n";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
        echo "</div>";
    }
} else {
    echo "<p>❌ Tidak ada data nasabah untuk test</p>\n";
}

echo "<hr>\n";
echo "<h3>🔗 Navigasi</h3>\n";
echo "<p>\n";
echo "<a href='prediksi_baru.php' style='margin-right: 10px;'>➕ Prediksi Nasabah Baru</a>\n";
echo "<a href='prediksi_nasabah.php' style='margin-right: 10px;'>👥 Prediksi Nasabah Website</a>\n";
echo "<a href='daftar_kelayakan.php' style='margin-right: 10px;'>📋 Daftar Kelayakan</a>\n";
echo "<a href='laporan.php' style='margin-right: 10px;'>📊 Laporan</a>\n";
echo "</p>\n";
?>
