<?php
/**
 * File helper untuk format tanggal
 *
 * File ini berisi fungsi-fungsi untuk memastikan format tanggal yang konsisten
 * di seluruh aplikasi.
 */

// Set zona waktu ke Asia/Jakarta (WIB)
date_default_timezone_set('Asia/Jakarta');

/**
 * Fungsi untuk memformat tanggal dari database ke format yang konsisten
 *
 * @param string|null $tanggal Tanggal dari database (format: Y-m-d H:i:s)
 * @param string $format Format tanggal yang diinginkan (default: d/m/Y H:i)
 * @param bool $use_indonesian_month Gunakan nama bulan dalam bahasa Indonesia
 * @return string Tanggal yang sudah diformat
 */
function format_tanggal($tanggal, $format = 'd/m/Y H:i', $use_indonesian_month = false) {
    if (!$tanggal) {
        return 'N/A';
    }

    // Konversi tanggal ke timestamp
    $timestamp = strtotime($tanggal);

    // Jika konversi gagal atau tanggal di masa depan, gunakan waktu saat ini
    if ($timestamp === false || $timestamp > time()) {
        $timestamp = time();
    }

    // Jika format menggunakan nama bulan dalam bahasa Indonesia
    if ($use_indonesian_month && strpos($format, 'F') !== false) {
        // Gunakan format tanggal Indonesia
        $bulan = array(
            1 => 'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
            'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
        );
        $tanggal = date('d', $timestamp);
        $bulan_index = date('n', $timestamp);
        $tahun = date('Y', $timestamp);
        $jam = date('H:i', $timestamp);

        return $tanggal . ' ' . $bulan[$bulan_index] . ' ' . $tahun . ' ' . $jam;
    }

    // Format tanggal
    return date($format, $timestamp);
}

/**
 * Fungsi untuk mendapatkan tanggal saat ini dalam format database
 *
 * @return string Tanggal saat ini dalam format Y-m-d H:i:s
 */
function get_current_datetime() {
    return date('Y-m-d H:i:s');
}

/**
 * Fungsi untuk menyinkronkan tanggal prediksi di semua tabel
 *
 * @param int $id_nasabah ID nasabah
 * @return bool True jika berhasil, false jika gagal
 */
function sync_prediction_dates($id_nasabah) {
    global $koneksi;

    // Mulai transaksi
    $koneksi->begin_transaction();

    try {
        // Ambil tanggal prediksi terbaru dari prediksi_detail
        $query_latest = "SELECT pd.id_prediksi, pd.id_laporan, pd.tanggal_prediksi
                        FROM prediksi_detail pd
                        WHERE pd.id_nasabah = ?
                        ORDER BY pd.tanggal_prediksi DESC
                        LIMIT 1";
        $stmt_latest = $koneksi->prepare($query_latest);
        $stmt_latest->bind_param("i", $id_nasabah);
        $stmt_latest->execute();
        $result_latest = $stmt_latest->get_result();
        $latest = $result_latest->fetch_assoc();

        if (!$latest) {
            // Tidak ada data prediksi untuk nasabah ini
            return false;
        }

        $id_prediksi = $latest['id_prediksi'];
        $id_laporan = $latest['id_laporan'];
        $tanggal_prediksi = $latest['tanggal_prediksi'];

        // Update tanggal prediksi di tabel hasil_prediksi
        $query_hasil = "UPDATE hasil_prediksi
                       SET tanggal_prediksi = ?
                       WHERE id_nasabah = ?";
        $stmt_hasil = $koneksi->prepare($query_hasil);
        $stmt_hasil->bind_param("si", $tanggal_prediksi, $id_nasabah);
        $stmt_hasil->execute();

        // Update tanggal prediksi di tabel laporan_prediksi
        $query_laporan = "UPDATE laporan_prediksi
                         SET tanggal_prediksi = ?
                         WHERE id_laporan = ?";
        $stmt_laporan = $koneksi->prepare($query_laporan);
        $stmt_laporan->bind_param("si", $tanggal_prediksi, $id_laporan);
        $stmt_laporan->execute();

        // Commit transaksi
        $koneksi->commit();

        return true;
    } catch (Exception $e) {
        // Rollback transaksi jika ada error
        $koneksi->rollback();
        return false;
    }
}
?>
