<?php
// Set zona waktu ke Asia/Jakarta (WIB)
date_default_timezone_set('Asia/Jakarta');

// Koneksi ke database
include 'koneksi.php';

// Tampilkan informasi zona waktu
echo "<h2>Informasi Zona Waktu</h2>";
echo "<p>Zona waktu PHP: " . date_default_timezone_get() . "</p>";
echo "<p>Waktu PHP saat ini: " . date('Y-m-d H:i:s') . "</p>";

// Periksa zona waktu MySQL
$query_timezone = "SELECT @@global.time_zone, @@session.time_zone";
$result_timezone = mysqli_query($koneksi, $query_timezone);
$timezone_data = mysqli_fetch_assoc($result_timezone);

echo "<p>Zona waktu MySQL global: " . $timezone_data['@@global.time_zone'] . "</p>";
echo "<p>Zona waktu MySQL session: " . $timezone_data['@@session.time_zone'] . "</p>";

// Periksa waktu MySQL
$query_now = "SELECT NOW() as mysql_now";
$result_now = mysqli_query($koneksi, $query_now);
$now_data = mysqli_fetch_assoc($result_now);

echo "<p>Waktu MySQL saat ini: " . $now_data['mysql_now'] . "</p>";

// Periksa data tanggal prediksi di database
echo "<h2>Data Tanggal Prediksi di Database</h2>";

// Periksa tabel prediksi_detail
$query_prediksi = "SELECT id_prediksi, id_nasabah, tanggal_prediksi FROM prediksi_detail ORDER BY tanggal_prediksi DESC LIMIT 5";
$result_prediksi = mysqli_query($koneksi, $query_prediksi);

echo "<h3>Tabel prediksi_detail</h3>";
echo "<table border='1'>";
echo "<tr><th>ID Prediksi</th><th>ID Nasabah</th><th>Tanggal Prediksi</th><th>Format d/m/Y H:i</th></tr>";

while ($row = mysqli_fetch_assoc($result_prediksi)) {
    echo "<tr>";
    echo "<td>" . $row['id_prediksi'] . "</td>";
    echo "<td>" . $row['id_nasabah'] . "</td>";
    echo "<td>" . $row['tanggal_prediksi'] . "</td>";
    echo "<td>" . ($row['tanggal_prediksi'] ? date('d/m/Y H:i', strtotime($row['tanggal_prediksi'])) : 'N/A') . "</td>";
    echo "</tr>";
}

echo "</table>";

// Periksa tabel hasil_prediksi
$query_hasil = "SELECT id_prediksi, id_nasabah, tanggal_prediksi FROM hasil_prediksi ORDER BY tanggal_prediksi DESC LIMIT 5";
$result_hasil = mysqli_query($koneksi, $query_hasil);

echo "<h3>Tabel hasil_prediksi</h3>";
echo "<table border='1'>";
echo "<tr><th>ID Prediksi</th><th>ID Nasabah</th><th>Tanggal Prediksi</th><th>Format d/m/Y H:i</th></tr>";

while ($row = mysqli_fetch_assoc($result_hasil)) {
    echo "<tr>";
    echo "<td>" . $row['id_prediksi'] . "</td>";
    echo "<td>" . $row['id_nasabah'] . "</td>";
    echo "<td>" . $row['tanggal_prediksi'] . "</td>";
    echo "<td>" . ($row['tanggal_prediksi'] ? date('d/m/Y H:i', strtotime($row['tanggal_prediksi'])) : 'N/A') . "</td>";
    echo "</tr>";
}

echo "</table>";

// Periksa tabel laporan_prediksi
$query_laporan = "SELECT id_laporan, tanggal_prediksi FROM laporan_prediksi ORDER BY tanggal_prediksi DESC LIMIT 5";
$result_laporan = mysqli_query($koneksi, $query_laporan);

echo "<h3>Tabel laporan_prediksi</h3>";
echo "<table border='1'>";
echo "<tr><th>ID Laporan</th><th>Tanggal Prediksi</th><th>Format d/m/Y H:i</th></tr>";

while ($row = mysqli_fetch_assoc($result_laporan)) {
    echo "<tr>";
    echo "<td>" . $row['id_laporan'] . "</td>";
    echo "<td>" . $row['tanggal_prediksi'] . "</td>";
    echo "<td>" . ($row['tanggal_prediksi'] ? date('d/m/Y H:i', strtotime($row['tanggal_prediksi'])) : 'N/A') . "</td>";
    echo "</tr>";
}

echo "</table>";

// Periksa data nasabah dan prediksi untuk ID tertentu
echo "<h2>Periksa Data Nasabah dan Prediksi untuk ID Tertentu</h2>";

// Ambil ID nasabah dari tabel prediksi_detail
$query_id = "SELECT DISTINCT id_nasabah FROM prediksi_detail LIMIT 1";
$result_id = mysqli_query($koneksi, $query_id);
$id_data = mysqli_fetch_assoc($result_id);
$id_nasabah = $id_data ? $id_data['id_nasabah'] : 0;

echo "<p>ID Nasabah yang diperiksa: " . $id_nasabah . "</p>";

// Periksa data prediksi untuk nasabah tersebut
$query_detail = "SELECT pd.id_prediksi, pd.id_nasabah, pd.tanggal_prediksi as pd_tanggal,
                       hp.tanggal_prediksi as hp_tanggal, lp.tanggal_prediksi as lp_tanggal
                FROM prediksi_detail pd
                LEFT JOIN hasil_prediksi hp ON pd.id_nasabah = hp.id_nasabah
                LEFT JOIN laporan_prediksi lp ON pd.id_laporan = lp.id_laporan
                WHERE pd.id_nasabah = $id_nasabah";
$result_detail = mysqli_query($koneksi, $query_detail);

echo "<h3>Perbandingan Tanggal Prediksi untuk Nasabah ID " . $id_nasabah . "</h3>";
echo "<table border='1'>";
echo "<tr>
        <th>ID Prediksi</th>
        <th>prediksi_detail.tanggal_prediksi</th>
        <th>Format d/m/Y H:i</th>
        <th>hasil_prediksi.tanggal_prediksi</th>
        <th>Format d/m/Y H:i</th>
        <th>laporan_prediksi.tanggal_prediksi</th>
        <th>Format d/m/Y H:i</th>
      </tr>";

while ($row = mysqli_fetch_assoc($result_detail)) {
    echo "<tr>";
    echo "<td>" . $row['id_prediksi'] . "</td>";
    echo "<td>" . $row['pd_tanggal'] . "</td>";
    echo "<td>" . ($row['pd_tanggal'] ? date('d/m/Y H:i', strtotime($row['pd_tanggal'])) : 'N/A') . "</td>";
    echo "<td>" . $row['hp_tanggal'] . "</td>";
    echo "<td>" . ($row['hp_tanggal'] ? date('d/m/Y H:i', strtotime($row['hp_tanggal'])) : 'N/A') . "</td>";
    echo "<td>" . $row['lp_tanggal'] . "</td>";
    echo "<td>" . ($row['lp_tanggal'] ? date('d/m/Y H:i', strtotime($row['lp_tanggal'])) : 'N/A') . "</td>";
    echo "</tr>";
}

echo "</table>";
?>
