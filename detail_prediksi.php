<?php
include 'koneksi.php';
include 'date_helper.php';
require_once 'nav_analis.php';

if(!isset($_GET['id_nasabah']) || !isset($_GET['id_laporan'])) {
    header("Location: laporan.php");
    exit;
}

$id_nasabah = $_GET['id_nasabah'];
$id_laporan = $_GET['id_laporan'];

// Ambil data nasabah dan hasil prediksi
$sql = "SELECT n.*, p.*
       FROM nasabah n
       JOIN prediksi_detail p ON n.id_nasabah = p.id_nasabah
       WHERE n.id_nasabah = ? AND p.id_laporan = ?";
$stmt = $koneksi->prepare($sql);
$stmt->bind_param("ii", $id_nasabah, $id_laporan);
$stmt->execute();
$data = $stmt->get_result()->fetch_assoc();

// Ambil data laporan
$sql_laporan = "SELECT * FROM laporan_prediksi WHERE id_laporan = ?";
$stmt_laporan = $koneksi->prepare($sql_laporan);
$stmt_laporan->bind_param("i", $id_laporan);
$stmt_laporan->execute();
$laporan = $stmt_laporan->get_result()->fetch_assoc();
?>


<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Detail Prediksi Nasabah</h1>
                <ol class="breadcrumb">
                    <li><a href="laporan.php">Laporan</a></li>
                    <li class="active">Detail Prediksi</li>
                </ol>
            </div>

            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>Data Nasabah</h4>
                    </div>
                    <div class="panel-body">
                        <table class="table table-bordered">
                            <tr>
                                <th width="30%">Nama Nasabah</th>
                                <td><?php echo htmlspecialchars($data['nama_nasabah']); ?></td>
                            </tr>
                            <tr>
                                <th>Jenis Kelamin</th>
                                <td><?php echo htmlspecialchars($data['jenis_kelamin']); ?></td>
                            </tr>
                            <tr>
                                <th>Status Perkawinan</th>
                                <td><?php echo htmlspecialchars($data['status_perkawinan']); ?></td>
                            </tr>
                            <tr>
                                <th>Penghasilan</th>
                                <td>Rp <?php echo number_format($data['penghasilan'], 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <th>Jumlah Pinjaman</th>
                                <td>Rp <?php echo number_format($data['jumlah_pinjaman'], 0, ',', '.'); ?></td>
                            </tr>
                            <tr>
                                <th>Jaminan</th>
                                <td><?php echo htmlspecialchars($data['jaminan']); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="panel panel-<?php echo $data['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?>">
                    <div class="panel-heading">
                        <h4>Hasil Prediksi</h4>
                    </div>
                    <div class="panel-body">
                        <div class="text-center" style="margin-bottom: 20px;">
                            <h3>Status:
                                <span class="label label-<?php echo $data['hasil_prediksi'] == 'Layak' ? 'success' : 'danger'; ?>" style="font-size: 24px;">
                                    <?php echo htmlspecialchars($data['hasil_prediksi']); ?>
                                </span>
                            </h3>
                        </div>
                        <table class="table table-bordered">
                            <tr>
                                <th width="30%">Tanggal Prediksi</th>
                                <td><?php echo format_tanggal($data['tanggal_prediksi']); ?></td>
                            </tr>
                            <tr>
                                <th>Akurasi Model</th>
                                <td><?php echo htmlspecialchars($laporan['akurasi']); ?>%</td>
                            </tr>
                            <tr>
                                <th>Keterangan</th>
                                <td><?php echo htmlspecialchars($data['keterangan']); ?></td>
                            </tr>
                            <tr>
                                <th>Parameter Model</th>
                                <td><?php echo htmlspecialchars($laporan['parameter']); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="col-lg-12">
                <div class="btn-group">
                    <a href="laporan.php" class="btn btn-default">
                        <i class="fa fa-arrow-left"></i> Kembali ke Laporan
                    </a>
                    <a href="cetak_hasil.php?id_prediksi=<?php echo htmlspecialchars($data['id_prediksi']); ?>" class="btn btn-primary" target="_blank">
                        <i class="fa fa-print"></i> Cetak Hasil Prediksi
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once 'foot.php';
?>