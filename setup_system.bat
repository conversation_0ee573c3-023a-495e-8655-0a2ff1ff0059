@echo off
echo ======================================================
echo Setup Sistem Prediksi Kelayakan Kredit Backpropagation
echo ======================================================
echo.

echo [1/6] Memeriksa Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python tidak ditemukan! Pastikan Python sudah terinstall.
    echo Download Python dari: https://www.python.org/downloads/
    pause
    exit /b 1
)
echo Python ditemukan!

echo.
echo [2/6] Menginstall dependencies Python...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Gagal menginstall dependencies Python!
    echo Coba jalankan: pip install Flask scikit-learn pandas numpy mysql-connector-python
    pause
    exit /b 1
)
echo Dependencies Python berhasil diinstall!

echo.
echo [3/6] Memeriksa koneksi database MySQL...
echo Pastikan MySQL/MariaDB sudah berjalan dan dapat diakses.
echo Database akan dibuat otomatis jika belum ada.

echo.
echo [4/6] Membuat database dan tabel...
mysql -u root -p < database_setup.sql
if %errorlevel% neq 0 (
    echo WARNING: Gagal menjalankan setup database otomatis.
    echo Silakan import file database_setup.sql secara manual ke MySQL.
    echo Atau jalankan perintah: mysql -u root -p < database_setup.sql
)

echo.
echo [5/6] Memeriksa file model...
if not exist "model\backpropagation_model.pkl" (
    echo Model tidak ditemukan. Model akan dibuat otomatis saat server pertama kali dijalankan.
) else (
    echo Model ditemukan!
)

echo.
echo [6/6] Setup selesai!
echo.
echo ======================================================
echo CARA MENJALANKAN SISTEM:
echo ======================================================
echo 1. Jalankan server model Python:
echo    start_model_server.bat
echo.
echo 2. Buka browser dan akses:
echo    http://localhost/sistem_prediksi_backpropagation(bismillah)/
echo.
echo 3. Login dengan:
echo    Username: admin, Password: password
echo    atau
echo    Username: analis, Password: password
echo.
echo ======================================================
echo.
pause
