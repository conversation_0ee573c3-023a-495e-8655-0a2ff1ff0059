{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Model <PERSON><PERSON><PERSON><PERSON>it dengan Backpropagation\n", "\n", "Notebook ini digunakan untuk membuat model prediksi kelayakan kredit menggunakan algoritma Backpropagation Neural Network (Multi-Layer Perceptron). Model ini akan digunakan untuk memprediksi kelayakan kredit nasabah berdasarkan berbagai fitur se<PERSON>i umur, <PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> tangg<PERSON>, dan la<PERSON><PERSON>."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Library yang <PERSON>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, OneHotEncoder\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.neural_network import MLPClassifier\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report\n", "import pickle\n", "import os\n", "\n", "# Set random seed untuk reproduksibilitas\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Memuat Dataset Asli\n", "\n", "<PERSON>da bagian ini, kita akan memuat dataset asli dari file CSV atau Excel. Pastikan file dataset Anda sudah tersedia."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>umur;jeni<PERSON>_k<PERSON><PERSON>;p<PERSON><PERSON><PERSON><PERSON>;pen<PERSON><PERSON><PERSON>;jum<PERSON>_tanggungan;jumlah_pinjaman;waktu_pen<PERSON><PERSON><PERSON>;kepem<PERSON><PERSON>_rumah;jaminan;tahun_kend<PERSON>an;status_pajak;tujuan_pinjaman;kelayakan</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>30 tahun;<PERSON><PERSON>-la<PERSON>;<PERSON><PERSON><PERSON>;Rp5.000.000;1;Rp10.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30 tahun;<PERSON><PERSON>-la<PERSON>;<PERSON><PERSON><PERSON>;Rp5.000.000;1;Rp10.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>48 tahun;Perempuan;Lainnya;Rp4.500.000;5;Rp35....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>30 tahun;Perempuan;Wiraswasta;Rp10.000.000;3;R...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>32 tahun;Perempuan;Wiraswasta;Rp12.000.000;2;R...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  umur;jeni<PERSON>_k<PERSON><PERSON>;p<PERSON><PERSON><PERSON><PERSON>;pen<PERSON><PERSON><PERSON>;jum<PERSON>_tanggungan;jumlah_pinjaman;waktu_pen<PERSON><PERSON><PERSON>;kepem<PERSON><PERSON>_rumah;jaminan;tahun_kend<PERSON><PERSON>;status_pajak;tujuan_pinjaman;kelayakan\n", "0  30 tahun;<PERSON><PERSON><PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON>;Rp5.000.000;1;Rp10.0...                                                                                                                            \n", "1  30 tahun;<PERSON><PERSON><PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON>;Rp5.000.000;1;Rp10.0...                                                                                                                            \n", "2  48 tahun;<PERSON><PERSON><PERSON><PERSON>;<PERSON><PERSON><PERSON>;Rp4.500.000;5;Rp35....                                                                                                                            \n", "3  30 tahun;Perempuan;Wiraswasta;Rp10.000.000;3;R...                                                                                                                            \n", "4  32 tahun;Perempuan;Wiraswasta;Rp12.000.000;2;R...                                                                                                                            "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["file_path = 'dataset_nasabah.csv'\n", "\n", "# Cek ekstensi file untuk menentukan cara membacanya\n", "if file_path.endswith('.csv'):\n", "    # Baca file CSV\n", "    # Sesuaikan parameter seperti separator (sep) dan encoding jika diperlukan\n", "    df = pd.read_csv(file_path, sep=';', encoding='utf-8')\n", "else:\n", "    raise ValueError(\"Format file tidak didukung. Gunakan CSV atau Excel.\")\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> beberapa baris pertama\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Pra-pemrosesan Dataset"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "IndentationError", "evalue": "unexpected indent (2165614126.py, line 6)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 6\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mdf['kelayakan'] = np.select(conditions, choices, default=default)\u001b[39m\n    ^\n\u001b[31mIndentationError\u001b[39m\u001b[31m:\u001b[39m unexpected indent\n"]}], "source": ["# <PERSON>ik<PERSON> kolom yang ada dalam dataset\n", "print(\"Kolom dalam dataset:\")\n", "print(df.columns.tolist())\n", "\n", " # Buat target\n", "    df['kelayakan'] = np.select(conditions, choices, default=default)\n", "\n", "# <PERSON><PERSON><PERSON> apakah ada nilai yang hilang (missing values)\n", "print(\"\\nJumlah nilai yang hilang per kolom:\")\n", "print(df.isnull().sum())\n", "\n", "# <PERSON>ani nilai yang hilang jika ada\n", "# Contoh: <PERSON><PERSON> nilai numerik yang hilang dengan median\n", "numeric_cols = df.select_dtypes(include=['int64', 'float64']).columns\n", "for col in numeric_cols:\n", "    if df[col].isnull().sum() > 0:\n", "        df[col].fillna(df[col].median(), inplace=True)\n", "\n", "# Contoh: <PERSON><PERSON> nilai kategorikal yang hilang dengan modus\n", "categorical_cols = df.select_dtypes(include=['object']).columns\n", "for col in categorical_cols:\n", "    if df[col].isnull().sum() > 0:\n", "        df[col].fillna(df[col].mode()[0], inplace=True)\n", "\n", "# <PERSON><PERSON><PERSON> fitur turunan jika belum ada\n", "if 'penghasilan_per_kapita' not in df.columns and 'penghasilan' in df.columns and 'jumlah_tanggungan' in df.columns:\n", "    df['penghasilan_per_kapita'] = df['penghasilan'] / np.maximum(df['jumlah_tanggungan'], 1)\n", "\n", "if 'rasio_pinjaman' not in df.columns and 'jumlah_pinjaman' in df.columns and 'penghasilan' in df.columns:\n", "    df['rasio_pinjaman'] = df['jumlah_pinjaman'] / (df['penghasilan'] * 12)\n", "\n", "# Pastikan kolom target 'kelayakan' ada dan dalam format yang benar\n", "if 'kelayakan' in df.columns:\n", "    # <PERSON><PERSON> kelayakan berupa string ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), kon<PERSON>i ke numerik (1, 0)\n", "    if df['kelayakan'].dtype == 'object':\n", "        df['kelayakan'] = df['kelayakan'].map({'Layak': 1, 'Tidak Layak': 0})\n", "else:\n", "    raise ValueError(\"Kolom 'kelayakan' tidak ditemukan dalam dataset. Pastikan dataset memiliki kolom target.\")\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> beberapa baris setelah pra-pemrosesan\n", "df.head()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 525 entries, 0 to 524\n", "Data columns (total 1 columns):\n", " #   Column                                                                                                                                                                          Non-Null Count  Dtype \n", "---  ------                                                                                                                                                                          --------------  ----- \n", " 0   umur;jeni<PERSON>_k<PERSON><PERSON>;p<PERSON><PERSON><PERSON><PERSON>;pen<PERSON><PERSON><PERSON>;jumlah_tanggungan;jumlah_pinjaman;waktu_penge<PERSON><PERSON>;kepemilikan_rumah;jaminan;tahun_kend<PERSON>an;status_pajak;tujuan_pinjaman;kelayakan  525 non-null    object\n", "dtypes: object(1)\n", "memory usage: 4.2+ KB\n"]}], "source": ["# Informasi dataset\n", "df.info()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>umur;jeni<PERSON>_k<PERSON><PERSON>;p<PERSON><PERSON><PERSON><PERSON>;pen<PERSON><PERSON><PERSON>;jum<PERSON>_tanggungan;jumlah_pinjaman;waktu_pen<PERSON><PERSON><PERSON>;kepem<PERSON><PERSON>_rumah;jaminan;tahun_kend<PERSON>an;status_pajak;tujuan_pinjaman;kelayakan</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>525</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>478</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>30 tahun;Perempuan;Swasta;Rp3.000.000;4;Rp25.0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       umur;jeni<PERSON>_k<PERSON><PERSON>;p<PERSON><PERSON><PERSON><PERSON>;pen<PERSON><PERSON><PERSON>;jum<PERSON>_tanggungan;jumlah_pinjaman;waktu_pen<PERSON><PERSON><PERSON>;kepem<PERSON><PERSON>_rumah;jaminan;tahun_kend<PERSON><PERSON>;status_pajak;tujuan_pinjaman;kelayakan\n", "count                                                 525                                                                                                                            \n", "unique                                                478                                                                                                                            \n", "top     30 tahun;Perempuan;Swasta;Rp3.000.000;4;Rp25.0...                                                                                                                            \n", "freq                                                    6                                                                                                                            "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Statistik deskriptif\n", "df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Distribusi target\n", "print(\"Distribusi Kelayakan:\")\n", "print(df['kelayakan'].value_counts())\n", "print(f\"<PERSON><PERSON><PERSON>: {df['kelayakan'].mean() * 100:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Persiapan Data untuk Pelatihan Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON><PERSON> fitur dan target\n", "X = df.drop('kelayakan', axis=1)\n", "y = df['kelayakan']\n", "\n", "# Identifikasi kolom numerik dan kategorikal\n", "# Kolom numerik: umur, <PERSON><PERSON><PERSON><PERSON>, jum<PERSON>_pin<PERSON><PERSON>, jumlah_tan<PERSON><PERSON>, ta<PERSON>_k<PERSON><PERSON>, dll.\n", "# Kolom kategorikal: p<PERSON><PERSON><PERSON><PERSON>, jam<PERSON><PERSON>, kep<PERSON><PERSON><PERSON>_ruma<PERSON>, status_pajak, dll.\n", "numeric_features = X.select_dtypes(include=['int64', 'float64']).columns.tolist()\n", "categorical_features = X.select_dtypes(include=['object']).columns.tolist()\n", "\n", "print(\"Fitur numerik:\")\n", "print(numeric_features)\n", "print(\"\\nFitur kategorikal:\")\n", "print(categorical_features)\n", "\n", "# Buat preprocessor\n", "preprocessor = ColumnTransformer(\n", "    transformers=[\n", "        ('num', StandardScaler(), numeric_features),\n", "        ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)\n", "    ] if categorical_features else [('num', StandardScaler(), numeric_features)]\n", ")\n", "\n", "# Bagi data menjadi training dan testing\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON> dan <PERSON> Model Backpropagation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Buat pipeline dengan preprocessor dan model\n", "model = Pipeline(steps=[\n", "    ('preprocessor', preprocessor),\n", "    ('classifier', MLPClassifier(hidden_layer_sizes=(20, 10), \n", "                                max_iter=1000, \n", "                                activation='relu',\n", "                                solver='adam',\n", "                                random_state=42))\n", "])\n", "\n", "# Latih model\n", "model.fit(X_train, y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prediksi pada data testing\n", "y_pred = model.predict(X_test)\n", "y_prob = model.predict_proba(X_test)[:, 1]\n", "\n", "# Hitung metrik evaluasi\n", "accuracy = accuracy_score(y_test, y_pred)\n", "precision = precision_score(y_test, y_pred)\n", "recall = recall_score(y_test, y_pred)\n", "f1 = f1_score(y_test, y_pred)\n", "\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "print(f\"Precision: {precision:.4f}\")\n", "print(f\"Recall: {recall:.4f}\")\n", "print(f\"F1 Score: {f1:.4f}\")\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> hasil evaluasi dalam bentuk tabel\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred, target_names=['<PERSON><PERSON><PERSON>', '<PERSON>ak']))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix\n", "cm = confusion_matrix(y_test, y_pred)\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['<PERSON><PERSON><PERSON>', '<PERSON>ak'],\n", "            yticklabels=['<PERSON>idak <PERSON>ak', '<PERSON>ak'])\n", "plt.xlabel('Prediksi')\n", "plt.ylabel('Aktual')\n", "plt.title('Confusion Matrix')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> dengan berbagai rasio train/test\n", "from sklearn.model_selection import cross_val_score\n", "\n", "# Daftar rasio train/test yang akan dievaluasi\n", "test_sizes = [0.2, 0.4, 0.5, 0.6, 0.8]\n", "results = []\n", "\n", "for test_size in test_sizes:\n", "    # Bagi data dengan rasio yang berbeda\n", "    X_train_split, X_test_split, y_train_split, y_test_split = train_test_split(\n", "        X, y, test_size=test_size, random_state=42)\n", "    \n", "    # Latih model\n", "    model_split = Pipeline(steps=[\n", "        ('preprocessor', preprocessor),\n", "        ('classifier', MLPClassifier(hidden_layer_sizes=(20, 10), \n", "                                    max_iter=1000, \n", "                                    activation='relu',\n", "                                    solver='adam',\n", "                                    random_state=42))\n", "    ])\n", "    \n", "    model_split.fit(X_train_split, y_train_split)\n", "    \n", "    # <PERSON><PERSON><PERSON>\n", "    y_pred_split = model_split.predict(X_test_split)\n", "    accuracy_split = accuracy_score(y_test_split, y_pred_split)\n", "    precision_split = precision_score(y_test_split, y_pred_split)\n", "    recall_split = recall_score(y_test_split, y_pred_split)\n", "    f1_split = f1_score(y_test_split, y_pred_split)\n", "    \n", "    # <PERSON><PERSON>an hasil\n", "    results.append({\n", "        'test_size': test_size,\n", "        'train_size': 1 - test_size,\n", "        'accuracy': accuracy_split,\n", "        'precision': precision_split,\n", "        'recall': recall_split,\n", "        'f1': f1_split\n", "    })\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> hasil dalam bentuk tabel\n", "results_df = pd.DataFrame(results)\n", "print(\"<PERSON><PERSON><PERSON> den<PERSON> Train/Test:\")\n", "print(results_df.to_string(index=False, float_format=lambda x: f\"{x:.4f}\"))\n", "\n", "# Visual<PERSON>si hasil\n", "plt.figure(figsize=(12, 6))\n", "for metric in ['accuracy', 'precision', 'recall', 'f1']:\n", "    plt.plot(results_df['train_size'], results_df[metric], marker='o', label=metric.capitalize())\n", "plt.xlabel('Rasio Data Training')\n", "plt.ylabel('<PERSON>kor')\n", "plt.title('Performa Model den<PERSON>sio Train/Test')\n", "plt.grid(True, linestyle='--', alpha=0.7)\n", "plt.legend()\n", "plt.xticks(results_df['train_size'])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simpan Model ke File Pickle"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Buat direktori model jika belum ada\n", "if not os.path.exists('model'):\n", "    os.makedirs('model')\n", "\n", "# Simpan model ke file pickle\n", "with open('model/backpropagation_model.pkl', 'wb') as f:\n", "    pickle.dump(model, f)\n", "\n", "# Simpan scaler secara terpisah untuk digunakan dalam prediksi\n", "scaler = model.named_steps['preprocessor'].transformers_[0][1]\n", "with open('model/scaler.pkl', 'wb') as f:\n", "    pickle.dump(scaler, f)\n", "\n", "print(\"Model dan scaler berhasil disimpan!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> <PERSON> dengan <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Fungsi untuk menguji model dengan data baru\n", "def uji_model(data):\n", "    # Konversi ke DataFrame\n", "    df_uji = pd.DataFrame([data])\n", "    \n", "    # <PERSON><PERSON><PERSON> fitur turunan jika belum ada\n", "    if 'penghasilan_per_kapita' not in df_uji.columns and 'penghasilan' in df_uji.columns and 'jumlah_tanggungan' in df_uji.columns:\n", "        df_uji['penghasilan_per_kapita'] = df_uji['penghasilan'] / np.maximum(df_uji['jumlah_tanggungan'], 1)\n", "    \n", "    if 'rasio_pinjaman' not in df_uji.columns and 'jumlah_pinjaman' in df_uji.columns and 'penghasilan' in df_uji.columns:\n", "        df_uji['rasio_pinjaman'] = df_uji['jumlah_pinjaman'] / (df_uji['penghasilan'] * 12)\n", "    \n", "    # Prediksi\n", "    try:\n", "        hasil_prediksi = model.predict(df_uji)\n", "        probabilitas = model.predict_proba(df_uji)[0, 1]\n", "        \n", "        # <PERSON><PERSON><PERSON> pen<PERSON>an\n", "        penjelasan = []\n", "        if probabilitas >= 0.5:  # <PERSON>ak\n", "            penjelasan.append(f\"Nasabah diprediksi LAYAK dengan probabilitas {probabilitas:.2%}.\")\n", "            \n", "            # <PERSON><PERSON><PERSON> pendukung\n", "            if 'penghasilan_per_kapita' in df_uji.columns and df_uji['penghasilan_per_kapita'].values[0] > 3000000:\n", "                penjelasan.append(f\"Penghasilan per kapita tinggi (Rp {df_uji['penghasilan_per_kapita'].values[0]:,.0f}).\")\n", "            \n", "            if 'rasio_pinjaman' in df_uji.columns and df_uji['rasio_pinjaman'].values[0] < 0.3:\n", "                penjelasan.append(f\"<PERSON><PERSON> pinjaman rendah ({df_uji['rasio_pinjaman'].values[0]:.2f}).\")\n", "            \n", "            if 'pekerjaan' in df_uji.columns and df_uji['pekerjaan'].values[0].lower() in ['pns', 'karyawan']:\n", "                penjelasan.append(f\"Pek<PERSON>ja<PERSON> stabil ({df_uji['pekerjaan'].values[0]}).\")\n", "        else:  # <PERSON><PERSON><PERSON>\n", "            penjelasan.append(f\"Nasabah diprediksi TIDAK LAYAK dengan probabilitas {1-probabilitas:.2%}.\")\n", "            \n", "            # <PERSON><PERSON><PERSON>\n", "            if 'penghasilan_per_kapita' in df_uji.columns and df_uji['penghasilan_per_kapita'].values[0] < 2000000:\n", "                penjelasan.append(f\"Penghasilan per kapita rendah (Rp {df_uji['penghasilan_per_kapita'].values[0]:,.0f}).\")\n", "            \n", "            if 'rasio_pinjaman' in df_uji.columns and df_uji['rasio_pinjaman'].values[0] > 0.5:\n", "                penjelasan.append(f\"Rasio pinjaman tinggi ({df_uji['rasio_pinjaman'].values[0]:.2f}).\")\n", "        \n", "        return {\n", "            'hasil': '<PERSON>ak' if hasil_prediksi[0] == 1 else 'T<PERSON>k <PERSON>ak',\n", "            'probabilitas': probabilitas,\n", "            'penjelasan': pen<PERSON><PERSON>an\n", "        }\n", "    except Exception as e:\n", "        return {\n", "            'error': str(e),\n", "            'tips': '<PERSON>ikan semua kolom yang diperlukan tersedia dan memiliki tipe data yang benar.'\n", "        }\n", "\n", "# Contoh data 1: <PERSON><PERSON><PERSON><PERSON><PERSON> layak\n", "data_contoh_1 = {\n", "    'umur': 35,\n", "    'penghasilan': 8000000,\n", "    'jumlah_tanggungan': 2,\n", "    'jumlah_pinjaman': 50000000,\n", "    'ta<PERSON>_kendaraan': 2018,\n", "    'pekerjaan': 'karyawan',\n", "    'jaminan': 'bpkb mobil',\n", "    'status_pajak': 'aktif',\n", "    'kepemilikan_rumah': 'milik sendiri'\n", "}\n", "\n", "# Contoh data 2: Kemungkinan tidak layak\n", "data_contoh_2 = {\n", "    'umur': 25,\n", "    'penghasilan': 3000000,\n", "    'jumlah_tanggungan': 3,\n", "    'jumlah_pinjaman': 80000000,\n", "    'ta<PERSON>_kendaraan': 2010,\n", "    'peker<PERSON><PERSON>': 'freelancer',\n", "    'jaminan': 'bpkb motor',\n", "    'status_pajak': 'tidak aktif',\n", "    'kepemilikan_rumah': 'kontrak'\n", "}\n", "\n", "# Uji model dengan data contoh 1\n", "print(\"Hasil Prediksi untuk Data Contoh 1:\")\n", "hasil_1 = uji_model(data_contoh_1)\n", "if 'error' in hasil_1:\n", "    print(f\"Error: {hasil_1['error']}\")\n", "    print(f\"Tips: {hasil_1['tips']}\")\n", "else:\n", "    print(f\"Hasil: {hasil_1['hasil']}\")\n", "    print(f\"Probabilitas: {hasil_1['probabilitas']:.4f}\")\n", "    print(\"Penjelasan:\")\n", "    for poin in hasil_1['penjelasan']:\n", "        print(f\"- {poin}\")\n", "\n", "print(\"\\nHasil Prediksi untuk Data Contoh 2:\")\n", "hasil_2 = uji_model(data_contoh_2)\n", "if 'error' in hasil_2:\n", "    print(f\"Error: {hasil_2['error']}\")\n", "    print(f\"Tips: {hasil_2['tips']}\")\n", "else:\n", "    print(f\"Hasil: {hasil_2['hasil']}\")\n", "    print(f\"Probabilitas: {hasil_2['probabilitas']:.4f}\")\n", "    print(\"Penjelasan:\")\n", "    for poin in hasil_2['penjelasan']:\n", "        print(f\"- {poin}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "Model backpropagation neural network telah berhasil dibuat dan disimpan. Model ini dapat digunakan untuk memprediksi kelayakan kredit nasabah berdasarkan berbagai fitur se<PERSON>i umur, <PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> tan<PERSON>, dan la<PERSON><PERSON>.\n", "\n", "File model yang di<PERSON>:\n", "1. `model/backpropagation_model.pkl` - Model utama\n", "2. `model/scaler.pkl` - Scaler untuk normalisasi data\n", "\n", "Model ini dapat digunakan dalam aplikasi web dengan memanggil file model menggunakan library pickle di Python."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}