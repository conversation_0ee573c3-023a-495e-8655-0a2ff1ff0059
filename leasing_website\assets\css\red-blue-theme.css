/* Red-Blue Theme for Leasing Website */

:root {
    --primary-red: #e63946;
    --secondary-red: #d62839;
    --primary-blue: #1d3557;
    --secondary-blue: #457b9d;
    --light-blue: #a8dadc;
    --white: #f1faee;
    --dark: #333;
}

/* General Styles */
body {
    font-family: sans-serif;
    color: var(--dark);
    line-height: 1.6;
    font-size: 14px;
    background-color: #f8f8f8;
    padding-top: 0;
    margin-top: 0;
}

/* Header */
header.bg-primary {
    background-color: var(--primary-blue) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Navbar */
.navbar-default {
    background-color: var(--white);
    border-color: var(--light-blue);
}

.navbar-default .navbar-brand {
    color: var(--primary-blue);
}

.navbar-default .navbar-nav > li > a {
    color: var(--primary-blue);
}

.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
    color: var(--primary-red);
    background-color: transparent;
}

.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
    color: var(--white);
    background-color: var(--primary-red);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-blue);
    border-color: var(--secondary-blue);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: var(--secondary-blue) !important;
    border-color: var(--primary-blue) !important;
}

.btn-outline-primary {
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-outline-primary:hover {
    background-color: var(--primary-blue);
    color: var(--white);
}

/* Panels */
.panel-primary {
    border-color: var(--primary-blue);
}

.panel-primary > .panel-heading {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
}

.panel-info {
    border-color: var(--light-blue);
}

.panel-info > .panel-heading {
    background-color: var(--light-blue);
    border-color: var(--light-blue);
    color: var(--primary-blue);
}

.panel-success {
    border-color: var(--secondary-blue);
}

.panel-success > .panel-heading {
    background-color: var(--secondary-blue);
    border-color: var(--secondary-blue);
    color: var(--white);
}

.panel-warning {
    border-color: var(--primary-red);
}

.panel-warning > .panel-heading {
    background-color: var(--primary-red);
    border-color: var(--primary-red);
    color: var(--white);
}

/* Service Cards */
.service-card {
    border: 1px solid var(--light-blue);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(29, 53, 87, 0.2);
}

.service-card .card-title {
    color: var(--primary-blue);
}

.service-card .btn-outline-primary {
    color: var(--primary-red);
    border-color: var(--primary-red);
}

.service-card .btn-outline-primary:hover {
    background-color: var(--primary-red);
    color: var(--white);
    border-color: var(--primary-red);
}

/* CTA Section */
.bg-primary {
    background-color: var(--primary-blue) !important;
}

section.bg-primary .btn-light {
    background-color: var(--white);
    color: var(--primary-red);
    border-color: var(--white);
}

section.bg-primary .btn-light:hover {
    background-color: var(--light-blue);
    color: var(--primary-blue);
}

/* Footer */
footer.bg-primary {
    background-color: var(--primary-blue) !important;
}

footer h5 {
    color: var(--primary-red);
}

footer .social-icons a {
    background-color: rgba(255, 255, 255, 0.1);
}

footer .social-icons a:hover {
    background-color: var(--primary-red);
}

footer hr {
    border-color: var(--secondary-blue);
}

/* Form Elements */
.form-control:focus {
    border-color: var(--secondary-blue);
    box-shadow: 0 0 0 0.2rem rgba(69, 123, 157, 0.25);
}

.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary-blue);
}

.form-section h3 {
    color: var(--primary-blue);
    border-bottom: 2px solid var(--light-blue);
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.form-section h4 {
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.form-label {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 8px;
    display: block;
}

.form-control {
    height: 38px;
    border-radius: 4px;
    border: 1px solid #ddd;
    padding: 8px 12px;
    transition: all 0.3s ease;
}

.form-control:hover {
    border-color: var(--light-blue);
}

.form-select {
    height: 38px;
    border-radius: 4px;
    border: 1px solid #ddd;
    padding: 8px 12px;
    transition: all 0.3s ease;
}

.text-muted {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
}

.mb-3 {
    margin-bottom: 20px !important;
}

.vehicle-details {
    background-color: rgba(230, 57, 70, 0.05);
    padding: 15px;
    border-radius: 6px;
    margin-top: 15px;
    border-left: 4px solid var(--primary-red);
}

.checkbox {
    margin: 20px 0;
}

.checkbox label {
    font-weight: 500;
    color: var(--dark);
}

.checkbox a {
    color: var(--primary-blue);
    text-decoration: underline;
}

.checkbox a:hover {
    color: var(--primary-red);
}

.btn-submit-container {
    margin-top: 30px;
    text-align: center;
}

.btn-submit {
    padding: 10px 30px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.required-field::after {
    content: " *";
    color: var(--primary-red);
    font-weight: bold;
}

/* Page Header */
.page-header {
    color: var(--primary-blue);
    border-bottom: 2px solid var(--light-blue);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(29, 53, 87, 0.8), rgba(29, 53, 87, 0.8)), url('../img/hero-bg.jpg');
}
