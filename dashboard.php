<?php
include 'koneksi.php';
include 'cek_session.php';
cek_akses(['admin', 'analis']);

// Ambil statistik data
$query_stats = "SELECT 
    COUNT(*) as total_nasabah,
    SUM(CASE WHEN kelayakan = 'Layak' THEN 1 ELSE 0 END) as nasabah_layak,
    SUM(CASE WHEN kelayakan = 'Tidak Layak' THEN 1 ELSE 0 END) as nasabah_tidak_layak,
    SUM(CASE WHEN kelayakan IS NULL THEN 1 ELSE 0 END) as belum_diprediksi
FROM nasabah";

$result_stats = mysqli_query($koneksi, $query_stats);
$stats = mysqli_fetch_assoc($result_stats);

// Ambil data untuk grafik per bulan (6 bulan terakhir)
$query_monthly = "SELECT 
    DATE_FORMAT(created_at, '%Y-%m') as bulan,
    COUNT(*) as total,
    SUM(CASE WHEN kelayakan = 'Layak' THEN 1 ELSE 0 END) as layak,
    SUM(CASE WHEN kelayakan = 'Tidak Layak' THEN 1 ELSE 0 END) as tidak_layak
FROM nasabah 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
ORDER BY bulan";

$result_monthly = mysqli_query($koneksi, $query_monthly);
$monthly_data = [];
while ($row = mysqli_fetch_assoc($result_monthly)) {
    $monthly_data[] = $row;
}

// Ambil prediksi terbaru
$query_recent = "SELECT n.nama_nasabah, n.kelayakan, n.created_at, n.penghasilan, n.jumlah_pinjaman
FROM nasabah n 
WHERE n.kelayakan IS NOT NULL 
ORDER BY n.created_at DESC 
LIMIT 5";

$result_recent = mysqli_query($koneksi, $query_recent);
$recent_predictions = [];
while ($row = mysqli_fetch_assoc($result_recent)) {
    $recent_predictions[] = $row;
}

// Include navigation
if ($_SESSION['role'] == 'admin') {
    require_once 'nav_admin.php';
} else {
    require_once 'nav_analis.php';
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Dashboard Sistem Prediksi Kelayakan Kredit</h1>
            </div>
        </div>

        <!-- Statistik Cards -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-users fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge"><?php echo number_format($stats['total_nasabah']); ?></div>
                                <div>Total Nasabah</div>
                            </div>
                        </div>
                    </div>
                    <a href="data_nasabah_analis.php">
                        <div class="panel-footer">
                            <span class="pull-left">Lihat Detail</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="panel panel-success">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-check-circle fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge"><?php echo number_format($stats['nasabah_layak']); ?></div>
                                <div>Nasabah Layak</div>
                            </div>
                        </div>
                    </div>
                    <a href="daftar_kelayakan.php?filter=layak">
                        <div class="panel-footer">
                            <span class="pull-left">Lihat Detail</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="panel panel-danger">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-times-circle fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge"><?php echo number_format($stats['nasabah_tidak_layak']); ?></div>
                                <div>Nasabah Tidak Layak</div>
                            </div>
                        </div>
                    </div>
                    <a href="daftar_kelayakan.php?filter=tidak_layak">
                        <div class="panel-footer">
                            <span class="pull-left">Lihat Detail</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="panel panel-warning">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-clock-o fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge"><?php echo number_format($stats['belum_diprediksi']); ?></div>
                                <div>Belum Diprediksi</div>
                            </div>
                        </div>
                    </div>
                    <a href="prediksi_baru.php">
                        <div class="panel-footer">
                            <span class="pull-left">Prediksi Sekarang</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Grafik dan Tabel -->
        <div class="row">
            <!-- Pie Chart -->
            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-pie-chart"></i> Distribusi Kelayakan Kredit</h3>
                    </div>
                    <div class="panel-body">
                        <canvas id="pieChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Bar Chart -->
            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> Trend Prediksi 6 Bulan Terakhir</h3>
                    </div>
                    <div class="panel-body">
                        <canvas id="barChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Prediksi Terbaru -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-list"></i> Prediksi Terbaru</h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Nama Nasabah</th>
                                        <th>Hasil Prediksi</th>
                                        <th>Penghasilan</th>
                                        <th>Jumlah Pinjaman</th>
                                        <th>Tanggal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_predictions as $pred): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($pred['nama_nasabah']); ?></td>
                                        <td>
                                            <span class="label label-<?php echo $pred['kelayakan'] == 'Layak' ? 'success' : 'danger'; ?>">
                                                <?php echo $pred['kelayakan']; ?>
                                            </span>
                                        </td>
                                        <td>Rp <?php echo number_format($pred['penghasilan'], 0, ',', '.'); ?></td>
                                        <td>Rp <?php echo number_format($pred['jumlah_pinjaman'], 0, ',', '.'); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($pred['created_at'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center">
                            <a href="daftar_kelayakan.php" class="btn btn-primary">Lihat Semua Prediksi</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Data untuk grafik
const pieData = {
    labels: ['Layak', 'Tidak Layak', 'Belum Diprediksi'],
    datasets: [{
        data: [<?php echo $stats['nasabah_layak']; ?>, <?php echo $stats['nasabah_tidak_layak']; ?>, <?php echo $stats['belum_diprediksi']; ?>],
        backgroundColor: ['#5cb85c', '#d9534f', '#f0ad4e'],
        borderWidth: 2
    }]
};

const monthlyLabels = [<?php echo '"' . implode('", "', array_column($monthly_data, 'bulan')) . '"'; ?>];
const monthlyLayak = [<?php echo implode(', ', array_column($monthly_data, 'layak')); ?>];
const monthlyTidakLayak = [<?php echo implode(', ', array_column($monthly_data, 'tidak_layak')); ?>];

// Pie Chart
const pieCtx = document.getElementById('pieChart').getContext('2d');
new Chart(pieCtx, {
    type: 'pie',
    data: pieData,
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Bar Chart
const barCtx = document.getElementById('barChart').getContext('2d');
new Chart(barCtx, {
    type: 'bar',
    data: {
        labels: monthlyLabels,
        datasets: [{
            label: 'Layak',
            data: monthlyLayak,
            backgroundColor: '#5cb85c'
        }, {
            label: 'Tidak Layak',
            data: monthlyTidakLayak,
            backgroundColor: '#d9534f'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>

<?php require_once 'foot.php'; ?>
