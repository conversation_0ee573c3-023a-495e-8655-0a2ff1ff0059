<!DOCTYPE html>
<html>
<head>
    <title>SISTEM PREDIKSI KELAYAKAN KREDIT</title>
    <link href="assets/css/login2.css" rel="stylesheet">
</head>

<?php
if(isset($_GET['pesan'])){
    if($_GET['pesan'] == "gagal"){
        echo "<div class='alert'>Username atau Password tidak sesuai!</div>";
    }
    if($_GET['pesan'] == "belum_login"){
        echo "<div class='alert'>Anda harus login dulu!</div>";
    }
    if($_GET['pesan'] == "akses_ditolak"){
        echo "<div class='alert'>Anda tidak memiliki akses ke halaman tersebut!</div>";
    }
    if($_GET['pesan'] == "logout"){
        echo "<div class='alert success'>Anda berhasil logout</div>";
    }
}
?>

<body>
    <div class="login-container" style="height:45vh;">
        <h2 style="text-align: center; ">LOGIN</h2>
        <form class="login-form" method="POST" action="cek_login.php">
        <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>
        <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="login-btn">Login</button>
        </form>
    </div>

     <script>
        // Hapus parameter pesan dari URL tanpa reload
        if(window.location.href.indexOf('pesan=') > -1) {
            history.replaceState({}, document.title, window.location.pathname);
        }
    </script>

</body>
</html>