<?php
include 'koneksi.php';
include 'cek_session.php';
cek_akses(['admin', 'analis']);

// Ambil parameter filter
$filter_kelayakan = isset($_GET['kelayakan']) ? $_GET['kelayakan'] : 'all';
$filter_bulan = isset($_GET['bulan']) ? $_GET['bulan'] : 'all';
$filter_pekerjaan = isset($_GET['pekerjaan']) ? $_GET['pekerjaan'] : 'all';

// Build WHERE clause
$where_conditions = [];
$where_conditions[] = "kelayakan IS NOT NULL";

if ($filter_kelayakan != 'all') {
    $where_conditions[] = "kelayakan = '" . mysqli_real_escape_string($koneksi, $filter_kelayakan) . "'";
}

if ($filter_bulan != 'all') {
    $where_conditions[] = "DATE_FORMAT(created_at, '%Y-%m') = '" . mysqli_real_escape_string($koneksi, $filter_bulan) . "'";
}

if ($filter_pekerjaan != 'all') {
    $where_conditions[] = "pekerjaan = '" . mysqli_real_escape_string($koneksi, $filter_pekerjaan) . "'";
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

// Query utama
$sql = "SELECT 
    n.*,
    hp.probabilitas,
    hp.tanggal_prediksi as tanggal_prediksi_detail
FROM nasabah n
LEFT JOIN hasil_prediksi hp ON n.id_nasabah = hp.id_nasabah
$where_clause
ORDER BY n.created_at DESC";

$result = mysqli_query($koneksi, $sql);

// Query untuk statistik
$sql_stats = "SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN kelayakan = 'Layak' THEN 1 ELSE 0 END) as layak,
    SUM(CASE WHEN kelayakan = 'Tidak Layak' THEN 1 ELSE 0 END) as tidak_layak,
    AVG(penghasilan) as avg_penghasilan,
    AVG(jumlah_pinjaman) as avg_pinjaman
FROM nasabah n
$where_clause";

$result_stats = mysqli_query($koneksi, $sql_stats);
$stats = mysqli_fetch_assoc($result_stats);

// Query untuk dropdown bulan
$sql_months = "SELECT DISTINCT DATE_FORMAT(created_at, '%Y-%m') as bulan 
FROM nasabah 
WHERE kelayakan IS NOT NULL 
ORDER BY bulan DESC";
$result_months = mysqli_query($koneksi, $sql_months);

// Query untuk dropdown pekerjaan
$sql_jobs = "SELECT DISTINCT pekerjaan 
FROM nasabah 
WHERE kelayakan IS NOT NULL 
ORDER BY pekerjaan";
$result_jobs = mysqli_query($koneksi, $sql_jobs);

// Include navigation
if ($_SESSION['role'] == 'admin') {
    require_once 'nav_admin.php';
} else {
    require_once 'nav_analis.php';
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Laporan Prediksi Kelayakan Kredit</h1>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="row no-print">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-filter"></i> Filter Laporan</h3>
                    </div>
                    <div class="panel-body">
                        <form method="GET" action="" class="form-inline">
                            <div class="form-group">
                                <label>Kelayakan:</label>
                                <select name="kelayakan" class="form-control">
                                    <option value="all" <?php echo $filter_kelayakan == 'all' ? 'selected' : ''; ?>>Semua</option>
                                    <option value="Layak" <?php echo $filter_kelayakan == 'Layak' ? 'selected' : ''; ?>>Layak</option>
                                    <option value="Tidak Layak" <?php echo $filter_kelayakan == 'Tidak Layak' ? 'selected' : ''; ?>>Tidak Layak</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>Bulan:</label>
                                <select name="bulan" class="form-control">
                                    <option value="all">Semua Bulan</option>
                                    <?php while ($month = mysqli_fetch_assoc($result_months)): ?>
                                    <option value="<?php echo $month['bulan']; ?>" <?php echo $filter_bulan == $month['bulan'] ? 'selected' : ''; ?>>
                                        <?php echo date('F Y', strtotime($month['bulan'] . '-01')); ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>Pekerjaan:</label>
                                <select name="pekerjaan" class="form-control">
                                    <option value="all">Semua Pekerjaan</option>
                                    <?php while ($job = mysqli_fetch_assoc($result_jobs)): ?>
                                    <option value="<?php echo $job['pekerjaan']; ?>" <?php echo $filter_pekerjaan == $job['pekerjaan'] ? 'selected' : ''; ?>>
                                        <?php echo $job['pekerjaan']; ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-search"></i> Filter
                            </button>
                            
                            <a href="export_excel.php?<?php echo http_build_query($_GET); ?>" class="btn btn-success">
                                <i class="fa fa-file-excel-o"></i> Export Excel
                            </a>
                            
                            <button type="button" class="btn btn-info" onclick="window.print()">
                                <i class="fa fa-print"></i> Print Laporan
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistik -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-body">
                        <div class="text-center">
                            <h3><?php echo number_format($stats['total']); ?></h3>
                            <p>Total Data</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-success">
                    <div class="panel-body">
                        <div class="text-center">
                            <h3><?php echo number_format($stats['layak']); ?></h3>
                            <p>Nasabah Layak</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-danger">
                    <div class="panel-body">
                        <div class="text-center">
                            <h3><?php echo number_format($stats['tidak_layak']); ?></h3>
                            <p>Nasabah Tidak Layak</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-info">
                    <div class="panel-body">
                        <div class="text-center">
                            <h3><?php echo $stats['total'] > 0 ? number_format(($stats['layak'] / $stats['total']) * 100, 1) : 0; ?>%</h3>
                            <p>Tingkat Kelayakan</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabel Data -->
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title"><i class="fa fa-table"></i> Data Laporan</h3>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="dataTable">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Nama Nasabah</th>
                                        <th>Pekerjaan</th>
                                        <th>Penghasilan</th>
                                        <th>Jumlah Pinjaman</th>
                                        <th>Kelayakan</th>
                                        <th>Probabilitas</th>
                                        <th>Tanggal</th>
                                        <th class="no-print">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $no = 1;
                                    mysqli_data_seek($result, 0); // Reset result pointer
                                    while ($row = mysqli_fetch_assoc($result)): 
                                    ?>
                                    <tr>
                                        <td><?php echo $no++; ?></td>
                                        <td><?php echo htmlspecialchars($row['nama_nasabah']); ?></td>
                                        <td><?php echo $row['pekerjaan']; ?></td>
                                        <td>Rp <?php echo number_format($row['penghasilan'], 0, ',', '.'); ?></td>
                                        <td>Rp <?php echo number_format($row['jumlah_pinjaman'], 0, ',', '.'); ?></td>
                                        <td>
                                            <span class="label label-<?php echo $row['kelayakan'] == 'Layak' ? 'success' : 'danger'; ?>">
                                                <?php echo $row['kelayakan']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php 
                                            if ($row['probabilitas']) {
                                                echo number_format($row['probabilitas'] * 100, 1) . '%';
                                            } else {
                                                echo '-';
                                            }
                                            ?>
                                        </td>
                                        <td><?php echo date('d/m/Y', strtotime($row['created_at'])); ?></td>
                                        <td class="no-print">
                                            <button class="btn btn-sm btn-info" onclick="showDetail(<?php echo $row['id_nasabah']; ?>)">
                                                <i class="fa fa-eye"></i> Detail
                                            </button>
                                            <button class="btn btn-sm btn-primary" onclick="printNasabah(<?php echo $row['id_nasabah']; ?>)">
                                                <i class="fa fa-print"></i> Print
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Detail Nasabah -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Detail Nasabah</h4>
            </div>
            <div class="modal-body" id="detailContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
function showDetail(idNasabah) {
    $('#detailContent').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');
    $('#detailModal').modal('show');
    
    $.ajax({
        url: 'detail_nasabah.php',
        type: 'GET',
        data: { id: idNasabah },
        success: function(response) {
            $('#detailContent').html(response);
        },
        error: function() {
            $('#detailContent').html('<div class="alert alert-danger">Error loading data</div>');
        }
    });
}

function printNasabah(idNasabah) {
    window.open('print_nasabah.php?id=' + idNasabah, '_blank');
}

// DataTable initialization
$(document).ready(function() {
    $('#dataTable').DataTable({
        "pageLength": 25,
        "order": [[ 7, "desc" ]],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});
</script>

<style>
@media print {
    .no-print { display: none !important; }
    .panel-heading { background-color: #f5f5f5 !important; }
    body { font-size: 12px; }
}
</style>

<?php require_once 'foot.php'; ?>
