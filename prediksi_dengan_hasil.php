<?php
// Aktifkan error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

include 'koneksi.php';
include 'cek_session.php';
cek_akses(['analis']);
require_once 'nav_analis.php';

$error_message = '';
$success_message = '';
$show_result = false;
$prediction_result = null;

// Form data default
$form_data = [
    'nama_nasabah' => '',
    'umur' => '',
    'penghasilan' => '',
    'jumlah_tanggungan' => '',
    'jumlah_pinjaman' => '',
    'jangka_waktu' => '12'
];

// Proses form jika ada submit
if (isset($_POST['submit'])) {
    // Ambil data dari form
    $form_data = [
        'nama_nasabah' => $_POST['nama_nasabah'] ?? '',
        'umur' => $_POST['umur'] ?? '',
        'penghasilan' => $_POST['penghasilan'] ?? '',
        'jumlah_tanggungan' => $_POST['jumlah_tanggungan'] ?? '',
        'jumlah_pinjaman' => $_POST['jumlah_pinjaman'] ?? '',
        'jangka_waktu' => $_POST['jangka_waktu'] ?? '12'
    ];

    // Validasi input
    if (empty($form_data['nama_nasabah'])) {
        $error_message = "Nama nasabah harus diisi!";
    } elseif (empty($form_data['penghasilan'])) {
        $error_message = "Penghasilan harus diisi!";
    } elseif (empty($form_data['jumlah_tanggungan'])) {
        $error_message = "Jumlah tanggungan harus diisi!";
    } elseif (empty($form_data['jumlah_pinjaman'])) {
        $error_message = "Jumlah pinjaman harus diisi!";
    } elseif (empty($form_data['umur'])) {
        $error_message = "Umur harus diisi!";
    } else {
        try {
            // Konversi data
            $penghasilan = (float)str_replace(['.', ','], '', $form_data['penghasilan']);
            $pinjaman = (float)str_replace(['.', ','], '', $form_data['jumlah_pinjaman']);
            $umur = (int)$form_data['umur'];
            $tanggungan = (int)$form_data['jumlah_tanggungan'];
            $jangka_waktu = (int)$form_data['jangka_waktu'];

            // Simpan ke database
            $sql = "INSERT INTO nasabah (nama_nasabah, jenis_kelamin, status_perkawinan, pekerjaan, penghasilan, jumlah_tanggungan, jumlah_pinjaman, jangka_waktu, jaminan, tahun_kendaraan, status_pajak, kepemilikan_rumah, umur, tujuan_pinjaman) VALUES (?, 'Laki-laki', 'Menikah', 'Swasta', ?, ?, ?, ?, 'BPKB Motor', 2020, 'Aktif', 'Milik Sendiri', ?, 'Kebutuhan Pribadi')";
            
            $stmt = mysqli_prepare($koneksi, $sql);
            mysqli_stmt_bind_param($stmt, "sdidii", $form_data['nama_nasabah'], $penghasilan, $tanggungan, $pinjaman, $jangka_waktu, $umur);
            
            if (mysqli_stmt_execute($stmt)) {
                $id_nasabah = mysqli_insert_id($koneksi);
                
                // Ambil data nasabah yang baru disimpan
                $query = "SELECT * FROM nasabah WHERE id_nasabah = ?";
                $stmt2 = mysqli_prepare($koneksi, $query);
                mysqli_stmt_bind_param($stmt2, "i", $id_nasabah);
                mysqli_stmt_execute($stmt2);
                $result = mysqli_stmt_get_result($stmt2);
                $nasabah_data = mysqli_fetch_assoc($result);
                
                if ($nasabah_data) {
                    // Lakukan prediksi menggunakan fallback PHP
                    $hasil_prediksi = prediksi_fallback($nasabah_data);
                    
                    // Simpan hasil prediksi
                    require_once 'api_predict.php';
                    $id_prediksi = simpan_hasil_prediksi($nasabah_data, $hasil_prediksi);
                    
                    if ($id_prediksi) {
                        $success_message = "Prediksi berhasil dilakukan!";
                        $show_result = true;
                        $prediction_result = [
                            'id_nasabah' => $id_nasabah,
                            'id_prediksi' => $id_prediksi,
                            'nama_nasabah' => $nasabah_data['nama_nasabah'],
                            'hasil_prediksi' => $hasil_prediksi['hasil_prediksi'],
                            'probabilitas' => $hasil_prediksi['probabilitas'],
                            'keterangan' => $hasil_prediksi['keterangan'],
                            'penghasilan' => $penghasilan,
                            'jumlah_pinjaman' => $pinjaman,
                            'jangka_waktu' => $jangka_waktu
                        ];
                        
                        // Reset form untuk input baru
                        $form_data = [
                            'nama_nasabah' => '',
                            'umur' => '',
                            'penghasilan' => '',
                            'jumlah_tanggungan' => '',
                            'jumlah_pinjaman' => '',
                            'jangka_waktu' => '12'
                        ];
                    } else {
                        $error_message = "Gagal menyimpan hasil prediksi.";
                    }
                } else {
                    $error_message = "Data nasabah tidak ditemukan setelah penyimpanan.";
                }
            } else {
                $error_message = "Gagal menyimpan data nasabah: " . mysqli_error($koneksi);
            }
        } catch (Exception $e) {
            $error_message = "Error: " . $e->getMessage();
        }
    }
}

// Fungsi prediksi fallback
function prediksi_fallback($nasabah) {
    $jumlah_tanggungan = max(1, intval($nasabah['jumlah_tanggungan']));
    $penghasilan = floatval($nasabah['penghasilan']);
    $jumlah_pinjaman = floatval($nasabah['jumlah_pinjaman']);

    $penghasilan_tahunan = $penghasilan * 12;
    $penghasilan_per_kapita_tahunan = $penghasilan_tahunan / $jumlah_tanggungan;
    $rasio_pinjaman = $jumlah_pinjaman / $penghasilan_per_kapita_tahunan;

    $hasil_prediksi = ($rasio_pinjaman <= 0.9) ? 'Layak' : 'Tidak Layak';

    if ($hasil_prediksi == 'Layak') {
        $probabilitas = max(0.6, min(0.95, 1 - ($rasio_pinjaman / 0.9)));
    } else {
        $probabilitas = max(0.3, min(0.6, ($rasio_pinjaman - 0.9) / 0.9));
    }

    $keterangan = "Nasabah diprediksi " . strtolower($hasil_prediksi) . " menerima kredit dengan probabilitas " . number_format($probabilitas * 100, 2) . "%. ";
    $keterangan .= "Rasio pinjaman terhadap penghasilan per kapita tahunan adalah " . number_format($rasio_pinjaman * 100, 0) . "%. ";
    
    if ($hasil_prediksi == 'Layak') {
        $keterangan .= "Rasio ini masih dalam batas aman (di bawah 90%).";
    } else {
        $keterangan .= "Rasio ini melebihi batas aman (di atas 90%). Pertimbangkan untuk mengurangi jumlah pinjaman atau memperpanjang jangka waktu.";
    }

    return [
        'hasil_prediksi' => $hasil_prediksi,
        'probabilitas' => $probabilitas,
        'keterangan' => $keterangan,
        'skor' => $hasil_prediksi == 'Layak' ? 75 : 45,
        'max_skor' => 100
    ];
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Prediksi Kelayakan Kredit dengan Hasil Langsung</h1>
            </div>
        </div>

        <!-- Tampilkan pesan error jika ada -->
        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fa fa-exclamation-circle"></i> <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- Tampilkan pesan sukses jika ada -->
        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fa fa-check-circle"></i> <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <!-- Tampilkan hasil prediksi jika ada -->
        <?php if ($show_result && $prediction_result): ?>
        <div class="panel panel-success">
            <div class="panel-heading">
                <h4><i class="fa fa-check-circle"></i> Hasil Prediksi Kelayakan Kredit</h4>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5><strong>Data Nasabah:</strong></h5>
                        <table class="table table-borderless">
                            <tr><td><strong>Nama:</strong></td><td><?php echo htmlspecialchars($prediction_result['nama_nasabah']); ?></td></tr>
                            <tr><td><strong>Penghasilan:</strong></td><td>Rp <?php echo number_format($prediction_result['penghasilan'], 0, ',', '.'); ?></td></tr>
                            <tr><td><strong>Jumlah Pinjaman:</strong></td><td>Rp <?php echo number_format($prediction_result['jumlah_pinjaman'], 0, ',', '.'); ?></td></tr>
                            <tr><td><strong>Jangka Waktu:</strong></td><td><?php echo $prediction_result['jangka_waktu']; ?> bulan</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <?php if ($prediction_result['hasil_prediksi'] == 'Layak'): ?>
                                <div class="alert alert-success" style="font-size: 18px; font-weight: bold;">
                                    <i class="fa fa-thumbs-up fa-2x"></i><br>
                                    LAYAK MENERIMA KREDIT
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-success" style="width: <?php echo ($prediction_result['probabilitas'] * 100); ?>%">
                                        <?php echo number_format($prediction_result['probabilitas'] * 100, 1); ?>%
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger" style="font-size: 18px; font-weight: bold;">
                                    <i class="fa fa-thumbs-down fa-2x"></i><br>
                                    TIDAK LAYAK MENERIMA KREDIT
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-danger" style="width: <?php echo ((1 - $prediction_result['probabilitas']) * 100); %>%">
                                        <?php echo number_format((1 - $prediction_result['probabilitas']) * 100, 1); ?>%
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <h5><strong>Keterangan:</strong></h5>
                        <div class="well"><?php echo nl2br(htmlspecialchars($prediction_result['keterangan'])); ?></div>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="hasil_prediksi.php?id_nasabah=<?php echo $prediction_result['id_nasabah']; ?>&id_prediksi=<?php echo $prediction_result['id_prediksi']; ?>" class="btn btn-info">
                        <i class="fa fa-eye"></i> Lihat Detail Lengkap
                    </a>
                    <a href="daftar_kelayakan.php" class="btn btn-success">
                        <i class="fa fa-list"></i> Daftar Kelayakan
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Form Input -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4>Form Prediksi Nasabah Baru</h4>
            </div>
            <div class="panel-body">
                <form method="post">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nama Nasabah *</label>
                                <input type="text" name="nama_nasabah" class="form-control" value="<?php echo htmlspecialchars($form_data['nama_nasabah']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Umur *</label>
                                <input type="number" name="umur" class="form-control" min="20" max="60" value="<?php echo htmlspecialchars($form_data['umur']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Penghasilan per Bulan (Rp) *</label>
                                <input type="number" name="penghasilan" class="form-control" value="<?php echo htmlspecialchars($form_data['penghasilan']); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Jumlah Tanggungan *</label>
                                <input type="number" name="jumlah_tanggungan" class="form-control" min="0" max="10" value="<?php echo htmlspecialchars($form_data['jumlah_tanggungan']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Jumlah Pinjaman (Rp) *</label>
                                <input type="number" name="jumlah_pinjaman" class="form-control" value="<?php echo htmlspecialchars($form_data['jumlah_pinjaman']); ?>" required>
                            </div>
                            <div class="form-group">
                                <label>Jangka Waktu (bulan) *</label>
                                <input type="number" name="jangka_waktu" class="form-control" min="6" max="60" value="<?php echo htmlspecialchars($form_data['jangka_waktu']); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group text-center">
                        <button type="submit" name="submit" class="btn btn-primary btn-lg">
                            <i class="fa fa-calculator"></i> Hitung Prediksi Kelayakan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once 'foot.php'; ?>
