<?php
include 'koneksi.php';
include 'date_helper.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);
require_once 'nav_analis.php';

// Proses hapus data laporan
if(isset($_GET['hapus_laporan'])) {
    $id = $_GET['hapus_laporan'];
    $sql = "DELETE FROM laporan_prediksi WHERE id_laporan=?";
    $stmt = $koneksi->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();

    if($stmt->affected_rows > 0) {
        echo "<script>alert('Laporan berhasil dihapus');</script>";
        echo "<script>window.location.href='laporan.php';</script>";
    }
}
?>

<div id="page-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Laporan Hasil Prediksi</h1>
            </div>

            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
                        <span>Data Hasil Prediksi Backpropagation
                            <?php
                            // Hitung jumlah data nasabah yang sudah diprediksi
                            $count_query = "SELECT COUNT(DISTINCT n.id_nasabah) as total
                                           FROM nasabah n
                                           LEFT JOIN hasil_prediksi hp ON n.id_nasabah = hp.id_nasabah
                                           LEFT JOIN prediksi_detail pd ON n.id_nasabah = pd.id_nasabah
                                           WHERE (hp.hasil_prediksi IS NOT NULL
                                                  OR pd.hasil_prediksi IS NOT NULL
                                                  OR n.kelayakan IS NOT NULL)";
                            $count_result = mysqli_query($koneksi, $count_query);
                            $count_data = mysqli_fetch_assoc($count_result);
                            echo "(" . $count_data['total'] . " nasabah)";
                            ?>
                        </span>
                    </div>
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-hover dataTables-laporan">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Nama Nasabah</th>
                                        <th>Penghasilan</th>
                                        <th>Jumlah Tanggungan</th>
                                        <th>Jumlah Pinjaman</th>
                                        <th>Jaminan</th>
                                        <th>Tahun Kendaraan</th>
                                        <th>Status Pajak</th>
                                        <th>Tujuan Pinjaman</th>
                                        <th>Status Kelayakan</th>
                                        <th>Probabilitas</th>
                                        <th>Tanggal Prediksi</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Cek apakah tabel hasil_prediksi dan prediksi_detail ada
                                    $check_hasil_prediksi = "SHOW TABLES LIKE 'hasil_prediksi'";
                                    $check_prediksi_detail = "SHOW TABLES LIKE 'prediksi_detail'";
                                    $hasil_prediksi_exists = mysqli_query($koneksi, $check_hasil_prediksi);
                                    $prediksi_detail_exists = mysqli_query($koneksi, $check_prediksi_detail);

                                    // Query untuk mengambil semua data nasabah yang sudah diprediksi
                                    // Prioritas: hasil_prediksi > nasabah.kelayakan
                                    // Cek apakah tabel hasil_prediksi ada
                                    $check_hasil_prediksi = mysqli_query($koneksi, "SHOW TABLES LIKE 'hasil_prediksi'");
                                    $hasil_prediksi_exists = mysqli_num_rows($check_hasil_prediksi) > 0;

                                    if ($hasil_prediksi_exists) {
                                        // Gunakan query dengan tabel hasil_prediksi
                                        $sql = "SELECT DISTINCT
                                                   n.*,
                                                   COALESCE(hp.hasil_prediksi, n.kelayakan) as hasil_prediksi,
                                                   COALESCE(hp.tanggal_prediksi, n.created_at) as tanggal_prediksi,
                                                   COALESCE(hp.id_prediksi, n.id_nasabah) as id_prediksi,
                                                   COALESCE(hp.probabilitas, 0.5) as probabilitas
                                               FROM nasabah n
                                               LEFT JOIN (
                                                   SELECT hp1.*
                                                   FROM hasil_prediksi hp1
                                                   INNER JOIN (
                                                       SELECT id_nasabah, MAX(tanggal_prediksi) as max_tanggal
                                                       FROM hasil_prediksi
                                                       GROUP BY id_nasabah
                                                   ) hp2 ON hp1.id_nasabah = hp2.id_nasabah AND hp1.tanggal_prediksi = hp2.max_tanggal
                                               ) hp ON n.id_nasabah = hp.id_nasabah
                                               WHERE (hp.hasil_prediksi IS NOT NULL OR n.kelayakan IS NOT NULL)
                                               ORDER BY COALESCE(hp.tanggal_prediksi, n.created_at) DESC";
                                    } else {
                                        // Fallback ke query sederhana jika tabel hasil_prediksi tidak ada
                                        $sql = "SELECT n.*,
                                                   n.kelayakan as hasil_prediksi,
                                                   n.created_at as tanggal_prediksi,
                                                   n.id_nasabah as id_prediksi,
                                                   0.5 as probabilitas
                                               FROM nasabah n
                                               WHERE n.kelayakan IS NOT NULL
                                               ORDER BY n.created_at DESC";
                                    }

                                    $hasil = mysqli_query($koneksi, $sql);

                                    if (!$hasil) {
                                        echo '<div class="alert alert-danger">Error: ' . mysqli_error($koneksi) . '</div>';
                                        echo '<div class="alert alert-info">
                                                <h4>Solusi:</h4>
                                                <p>Jalankan script berikut untuk membuat tabel yang hilang:</p>
                                                <code>mysql -u root -p < create_missing_tables.sql</code>
                                              </div>';
                                        $hasil = false;
                                    }
                                    $no = 0;

                                    if ($hasil && mysqli_num_rows($hasil) > 0) {
                                        while ($data = mysqli_fetch_array($hasil)) {
                                        $no++;
                                        $badge_class = $data['hasil_prediksi'] == 'Layak' ? 'success' : 'danger';

                                        // Format probabilitas
                                        $probabilitas = floatval($data['probabilitas'] ?? 0.5);
                                        if ($data['hasil_prediksi'] == 'Tidak Layak') {
                                            $probabilitas = 1 - $probabilitas; // Tampilkan probabilitas tidak layak
                                        }
                                        $probabilitas_persen = number_format($probabilitas * 100, 1) . '%';
                                    ?>
                                    <tr>
                                        <td><?php echo $no; ?></td>
                                        <td><?php echo htmlspecialchars($data["nama_nasabah"]); ?></td>
                                        <td><?php echo number_format($data["penghasilan"], 0, ',', '.'); ?></td>
                                        <td><?php echo htmlspecialchars($data["jumlah_tanggungan"] ?? '-'); ?></td>
                                        <td><?php echo number_format($data["jumlah_pinjaman"], 0, ',', '.'); ?></td>
                                        <td><?php echo htmlspecialchars($data["jaminan"]); ?></td>
                                        <td><?php echo htmlspecialchars($data["tahun_kendaraan"] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($data["status_pajak"] ?? '-'); ?></td>
                                        <td><?php echo htmlspecialchars($data["tujuan_pinjaman"] ?? 'Kebutuhan Pribadi'); ?></td>
                                        <td>
                                            <span class="label label-<?php echo $badge_class; ?>">
                                                <?php echo htmlspecialchars($data["hasil_prediksi"]); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="label label-<?php echo $badge_class; ?>">
                                                <?php echo $probabilitas_persen; ?>
                                            </span>
                                        </td>
                                        <td><?php echo format_tanggal($data["tanggal_prediksi"]); ?></td>
                                        <td>
                                            <a href="hasil_prediksi.php?id_nasabah=<?php echo htmlspecialchars($data['id_nasabah']); ?>&id_prediksi=<?php echo htmlspecialchars($data['id_prediksi']); ?>"
                                               class="btn btn-info btn-sm" title="Lihat Detail">
                                                <i class="fa fa-eye"></i>
                                            </a>
                                            <a href="cetak_hasil.php?id_prediksi=<?php echo htmlspecialchars($data['id_prediksi']); ?>" class="btn btn-primary btn-sm" target="_blank" title="Cetak">
                                                <i class="fa fa-print"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php
                                        }
                                    } else {
                                        // Jika tidak ada data atau error
                                        echo '<tr>';
                                        echo '<td colspan="13" class="text-center">';
                                        if ($hasil === false) {
                                            echo 'Error: Tabel tidak ditemukan atau query gagal. Silakan setup database terlebih dahulu.';
                                        } else {
                                            echo 'Tidak ada data prediksi yang ditemukan. Silakan lakukan prediksi terlebih dahulu.';
                                        }
                                        echo '</td>';
                                        echo '</tr>';
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once 'foot.php';
?>

<script>
$(document).ready(function() {
    $('.dataTables-laporan').DataTable({
        responsive: true,
        "language": {
            "lengthMenu": "Tampilkan _MENU_ entri per halaman",
            "zeroRecords": "Tidak ada data yang ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data yang tersedia",
            "infoFiltered": "(difilter dari _MAX_ total entri)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "lengthMenu": [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]],
        "pageLength": 25,
        "order": [[ 11, "desc" ]] 
    });
});
</script>