<?php
// Script untuk update struktur database

include 'koneksi.php';

echo "<h2>Update Struktur Database</h2>\n";

// 1. Tambahkan kolom sumber_data ke tabel nasabah
echo "<h3>1. Menambahkan kolom sumber_data ke tabel nasabah</h3>\n";

$check_column = "SHOW COLUMNS FROM nasabah LIKE 'sumber_data'";
$result_check = mysqli_query($koneksi, $check_column);

if (mysqli_num_rows($result_check) == 0) {
    $add_column = "ALTER TABLE nasabah ADD COLUMN sumber_data ENUM('manual', 'website') DEFAULT 'manual' AFTER tujuan_pinjaman";
    
    if (mysqli_query($koneksi, $add_column)) {
        echo "✅ Kolom sumber_data berhasil ditambahkan<br>\n";
        
        // Update data yang sudah ada
        echo "<h4>Update data yang sudah ada:</h4>\n";
        
        // Set data yang dibuat dari prediksi_baru.php sebagai 'manual'
        $update_manual = "UPDATE nasabah SET sumber_data = 'manual' WHERE sumber_data IS NULL OR sumber_data = 'manual'";
        if (mysqli_query($koneksi, $update_manual)) {
            $affected = mysqli_affected_rows($koneksi);
            echo "✅ $affected data diset sebagai sumber 'manual'<br>\n";
        }
        
        // Jika ada data dengan tujuan_pinjaman yang mengindikasikan dari website, set sebagai 'website'
        $update_website = "UPDATE nasabah SET sumber_data = 'website' WHERE tujuan_pinjaman LIKE '%Website%' OR tujuan_pinjaman LIKE '%Online%' OR tujuan_pinjaman LIKE '%Leasing%'";
        if (mysqli_query($koneksi, $update_website)) {
            $affected = mysqli_affected_rows($koneksi);
            echo "✅ $affected data diset sebagai sumber 'website'<br>\n";
        }
        
    } else {
        echo "❌ Error menambahkan kolom: " . mysqli_error($koneksi) . "<br>\n";
    }
} else {
    echo "ℹ️ Kolom sumber_data sudah ada<br>\n";
}

// 2. Update prediksi_baru.php untuk menggunakan sumber_data = 'manual'
echo "<h3>2. Informasi untuk Update Kode</h3>\n";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #ccc; margin: 10px 0;'>";
echo "<h4>📋 Update yang perlu dilakukan pada kode:</h4>\n";
echo "<ol>\n";
echo "<li><strong>prediksi_baru.php:</strong> Tambahkan sumber_data = 'manual' saat insert</li>\n";
echo "<li><strong>prediksi_nasabah.php:</strong> Filter hanya sumber_data = 'website'</li>\n";
echo "<li><strong>Form website leasing:</strong> Set sumber_data = 'website' saat insert</li>\n";
echo "</ol>\n";
echo "</div>";

// 3. Tampilkan statistik data berdasarkan sumber
echo "<h3>3. Statistik Data Berdasarkan Sumber</h3>\n";
$stats_query = "SELECT sumber_data, COUNT(*) as jumlah FROM nasabah GROUP BY sumber_data";
$stats_result = mysqli_query($koneksi, $stats_query);

if ($stats_result) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Sumber Data</th><th>Jumlah</th></tr>\n";
    while ($row = mysqli_fetch_assoc($stats_result)) {
        echo "<tr><td>" . $row['sumber_data'] . "</td><td>" . $row['jumlah'] . "</td></tr>\n";
    }
    echo "</table>\n";
} else {
    echo "❌ Error mengambil statistik: " . mysqli_error($koneksi) . "<br>\n";
}

// 4. Tampilkan data nasabah dari website yang belum diprediksi
echo "<h3>4. Data Nasabah Website yang Belum Diprediksi</h3>\n";
$website_query = "SELECT n.id_nasabah, n.nama_nasabah, n.sumber_data, n.created_at
                  FROM nasabah n
                  LEFT JOIN hasil_prediksi hp ON n.id_nasabah = hp.id_nasabah
                  WHERE n.sumber_data = 'website' AND hp.id_prediksi IS NULL
                  ORDER BY n.created_at DESC";
$website_result = mysqli_query($koneksi, $website_query);

if ($website_result && mysqli_num_rows($website_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>ID</th><th>Nama</th><th>Sumber</th><th>Tanggal</th></tr>\n";
    while ($row = mysqli_fetch_assoc($website_result)) {
        echo "<tr>";
        echo "<td>" . $row['id_nasabah'] . "</td>";
        echo "<td>" . htmlspecialchars($row['nama_nasabah']) . "</td>";
        echo "<td>" . $row['sumber_data'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>ℹ️ Tidak ada data nasabah website yang belum diprediksi</p>\n";
}

echo "<hr>\n";
echo "<p><a href='prediksi_nasabah.php'>Test Prediksi Nasabah Website</a> | ";
echo "<a href='prediksi_baru.php'>Test Prediksi Baru</a> | ";
echo "<a href='daftar_kelayakan.php'>Daftar Kelayakan</a></p>\n";
?>
