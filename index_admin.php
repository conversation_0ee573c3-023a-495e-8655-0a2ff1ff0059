<?php
    include 'koneksi.php';

    // Mulai session jika belum dimulai
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Cek apakah yang mengakses halaman ini sudah login sebagai admin
    if(!isset($_SESSION['level']) || $_SESSION['level'] != "admin"){
        // Redirect ke halaman login dengan pesan error
        header("location:login.php?pesan=akses_ditolak");
        exit(); // Penting untuk menghentikan eksekusi script setelah redirect
    }

    require_once 'nav.php';

        // Query untuk mendapatkan jumlah user
        $query_user = "SELECT COUNT(*) as total_user FROM users";
        $result_user = mysqli_query($koneksi, $query_user);
        $data_user = mysqli_fetch_assoc($result_user);

        // Query untuk mendapatkan jumlah nasabah
        $query_nasabah = "SELECT COUNT(*) as total_nasabah FROM nasabah WHERE kelayakan IS NOT NULL";
        $result_nasabah = mysqli_query($koneksi, $query_nasabah);
        $data_nasabah = mysqli_fetch_assoc($result_nasabah);

        // Query untuk mendapatkan jumlah semua nasabah (termasuk yang belum memiliki kelayakan)
        $query_all_nasabah = "SELECT COUNT(*) as total_all_nasabah FROM nasabah";
        $result_all_nasabah = mysqli_query($koneksi, $query_all_nasabah);
        $data_all_nasabah = mysqli_fetch_assoc($result_all_nasabah);
?>

            <div id="page-wrapper">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-lg-12">
                            <h1 class="page-header">Selamat Datang, <?php echo htmlspecialchars($_SESSION['username'] ?? 'Pengguna'); ?></h1>
                        </div>
                        <div class="col-lg-12">
                        <div class="panel panel-default">
                    <div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
                        <span>Data User</span>

                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <!-- Kolom Total User -->
                            <div class="col-md-6">
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <div class="row">
                                            <div class="col-xs-3">
                                                <i class="fa fa-user fa-4x"></i>
                                            </div>
                                            <div class="col-xs-9 text-right">
                                                <div class="huge"><?php echo $data_user['total_user']; ?></div>
                                                <div>Total User</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Kolom Total Nasabah dengan Kelayakan -->
                            <div class="col-md-6">
                                <div class="panel panel-success">
                                    <div class="panel-heading">
                                        <div class="row">
                                            <div class="col-xs-3">
                                                <i class="fa fa-users fa-4x"></i>
                                            </div>
                                            <div class="col-xs-9 text-right">
                                                <div class="huge"><?php echo $data_nasabah['total_nasabah']; ?></div>
                                                <div>Nasabah dengan Kelayakan</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Kolom Total Semua Nasabah -->
                            <div class="col-md-6">
                                <div class="panel panel-primary">
                                    <div class="panel-heading">
                                        <div class="row">
                                            <div class="col-xs-3">
                                                <i class="fa fa-database fa-4x"></i>
                                            </div>
                                            <div class="col-xs-9 text-right">
                                                <div class="huge"><?php echo $data_all_nasabah['total_all_nasabah']; ?></div>
                                                <div>Total Semua Nasabah</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Kolom Nasabah Tanpa Kelayakan -->
                            <div class="col-md-6">
                                <div class="panel panel-warning">
                                    <div class="panel-heading">
                                        <div class="row">
                                            <div class="col-xs-3">
                                                <i class="fa fa-question-circle fa-4x"></i>
                                            </div>
                                            <div class="col-xs-9 text-right">
                                                <div class="huge"><?php echo $data_all_nasabah['total_all_nasabah'] - $data_nasabah['total_nasabah']; ?></div>
                                                <div>Nasabah Tanpa Kelayakan</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- /.row -->
    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- /#page-wrapper -->
            <!-- /#page-wrapper -->

        </div>
        <!-- /#wrapper -->

<?php
    require_once 'foot.php';
 ?>