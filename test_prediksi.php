<?php
// Script untuk menguji prediksi
require_once 'koneksi.php';

echo "=== TEST PREDIKSI SISTEM ===\n";

// Ambil data nasabah pertama dari database
$query = "SELECT * FROM nasabah LIMIT 1";
$result = mysqli_query($koneksi, $query);

if ($result && mysqli_num_rows($result) > 0) {
    $nasabah = mysqli_fetch_assoc($result);
    
    echo "Data nasabah untuk test:\n";
    echo "ID: " . $nasabah['id_nasabah'] . "\n";
    echo "Nama: " . $nasabah['nama_nasabah'] . "\n";
    echo "Penghasilan: Rp " . number_format($nasabah['penghasilan'], 0, ',', '.') . "\n";
    echo "Pinjaman: Rp " . number_format($nasabah['jumlah_pinjaman'], 0, ',', '.') . "\n";
    echo "\n";
    
    // Test prediksi menggunakan api_predict.php
    echo "Menguji prediksi...\n";
    
    try {
        require_once 'api_predict.php';
        
        $hasil_prediksi = prediksi_dengan_backpropagation($nasabah);
        
        echo "HASIL PREDIKSI:\n";
        echo "Hasil: " . $hasil_prediksi['hasil_prediksi'] . "\n";
        echo "Probabilitas: " . ($hasil_prediksi['probabilitas'] * 100) . "%\n";
        echo "Keterangan: " . $hasil_prediksi['keterangan'] . "\n";
        
        // Simpan hasil prediksi
        $id_prediksi = simpan_hasil_prediksi($nasabah, $hasil_prediksi);
        
        if ($id_prediksi) {
            echo "Hasil prediksi berhasil disimpan dengan ID: $id_prediksi\n";
        } else {
            echo "Gagal menyimpan hasil prediksi\n";
        }
        
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "Tidak ada data nasabah di database\n";
    echo "Silakan tambah data nasabah terlebih dahulu\n";
}

echo "\n=== TEST SELESAI ===\n";
?>
