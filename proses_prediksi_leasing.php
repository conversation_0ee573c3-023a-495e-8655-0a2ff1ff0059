<?php
include 'koneksi.php';
include 'cek_session.php';
// Cek akses: hanya analis yang boleh mengakses halaman ini
cek_akses(['analis']);

// Inisialisasi variabel
$error_message = '';

// Cek apakah form telah disubmit
if (isset($_POST['predict_leasing']) && isset($_POST['id_nasabah'])) {
    $id_nasabah = $_POST['id_nasabah'];

    // Validasi ID nasabah
    if (empty($id_nasabah)) {
        $error_message = "Silakan pilih nasabah dari daftar terlebih dahulu.";
    } else {
        // Cek apakah nasabah ada di database
        $sql_check = "SELECT * FROM nasabah WHERE id_nasabah = ?";
        $stmt_check = mysqli_prepare($koneksi, $sql_check);
        mysqli_stmt_bind_param($stmt_check, "i", $id_nasabah);
        mysqli_stmt_execute($stmt_check);
        $result_check = mysqli_stmt_get_result($stmt_check);

        if (mysqli_num_rows($result_check) == 0) {
            $error_message = "Nasabah tidak ditemukan.";
        } else {
            // Lakukan prediksi menggunakan API
            $api_url = 'http://localhost/sistem_prediksi_backpropagation/api_predict.php';

            // Tambahkan log untuk debugging
            error_log("Prediksi untuk nasabah ID: " . $id_nasabah);

            // Gunakan cURL untuk memanggil API
            $ch = curl_init($api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(['id_nasabah' => $id_nasabah]));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            // Tambahkan log untuk debugging
            error_log("API Response: " . $response);
            error_log("HTTP Code: " . $http_code);

            curl_close($ch);

            // Periksa apakah API berhasil dipanggil
            if ($http_code === 200) {
                // Parse response JSON
                $result = json_decode($response, true);

                // Periksa apakah response berisi ID prediksi
                if (isset($result['id_prediksi'])) {
                    $id_prediksi = $result['id_prediksi'];

                    // Tambahkan log untuk debugging
                    error_log("Redirect ke hasil_prediksi.php dengan id_nasabah=$id_nasabah dan id_prediksi=$id_prediksi");

                    // Redirect ke halaman hasil prediksi
                    header("Location: hasil_prediksi.php?id_nasabah=$id_nasabah&id_prediksi=$id_prediksi");
                    exit;
                } else {
                    $error_message = "Gagal mendapatkan hasil prediksi: " . ($result['error'] ?? 'Unknown error');
                    error_log("Gagal mendapatkan ID prediksi: " . ($result['error'] ?? 'Unknown error'));
                }
            } else {
                $error_message = "Gagal melakukan prediksi. HTTP Code: $http_code";
                error_log("Gagal melakukan prediksi. HTTP Code: $http_code");
            }
        }
    }
}

// Jika terjadi error, redirect kembali ke halaman form dengan pesan error
if ($error_message) {
    header("Location: prediksi_baru.php?error=" . urlencode($error_message) . "&mode=leasing");
    exit;
}
?>
